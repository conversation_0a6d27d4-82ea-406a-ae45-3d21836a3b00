# Subscription

## Create Subscription

Create a subscription on your integration.

### Headers

| Key           | Type   | Description                            |
|---------------|--------|----------------------------------------|
| authorization | String | Set value to `Bearer SECRET_KEY`       |
| content-type  | String | Set value to `application/json`        |

### Body Parameters

| Key           | Type   | Description                                                                                   |
|----------------|--------|-----------------------------------------------------------------------------------------------|
| customer       | String | Customer's email address or customer code                                                     |
| plan           | String | Plan code                                                                                     |
| authorization  | String | If customer has multiple authorizations, you can set the desired authorization.              |
| start_date     | String | Set the date for the first debit (ISO 8601 format), e.g., `2017-05-16T00:30:13+01:00`         |

### Example Request

```js
const https = require('https')

const params = JSON.stringify({
  "customer": "CUS_xnxdt6s1zg1f4nx",
  "plan": "PLN_gx2wn530m0i3w3m"
})

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/subscription',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
    'Content-Type': 'application/json'
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.write(params)
req.end()
```

### Response

```json
{
  "status": true,
  "message": "Subscription successfully created",
  "data": {
    "customer": 1173,
    "plan": 28,
    "integration": 100032,
    "domain": "test",
    "start": **********,
    "status": "active",
    "quantity": 1,
    "amount": 50000,
    "authorization": {
      "authorization_code": "AUTH_6tmt288t0o",
      "bin": "408408",
      "last4": "4081",
      "exp_month": "12",
      "exp_year": "2020",
      "channel": "card",
      "card_type": "visa visa",
      "bank": "TEST BANK",
      "country_code": "NG",
      "brand": "visa",
      "reusable": true,
      "signature": "SIG_uSYN4fv1adlAuoij8QXh",
      "account_name": "BoJack Horseman"
    },
    "subscription_code": "SUB_vsyqdmlzble3uii",
    "email_token": "d7gofp6yppn3qz7",
    "id": 9,
    "createdAt": "2016-03-30T00:01:04.687Z",
    "updatedAt": "2016-03-30T00:01:04.687Z"
  }
}
```

---

## Fetch Subscription

Get details of a subscription on your integration.

### Headers

| Key           | Type   | Description                      |
|---------------|--------|----------------------------------|
| authorization | String | Set value to `Bearer SECRET_KEY` |

### Path Parameters

| Key         | Type   | Description                                |
|-------------|--------|--------------------------------------------|
| id_or_code  | String | The subscription ID or code to be fetched  |

### Example Request

```js
const https = require('https')

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/subscription/:id_or_code',
  method: 'GET',
  headers: {
    Authorization: 'Bearer SECRET_KEY'
  }
}

https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})
```

### Response

```json
{
  "status": true,
  "message": "Subscription retrieved successfully",
  "data": {
    "invoices": [],
    "customer": {
      "first_name": "BoJack",
      "last_name": "Horseman",
      "email": "<EMAIL>",
      "phone": null,
      "metadata": {
        "photos": [
          {
            "type": "twitter",
            "typeId": "twitter",
            "typeName": "Twitter",
            "url": "https://d2ojpxxtu63wzl.cloudfront.net/static/61b1a0a1d4dda2c9fe9e165fed07f812_a722ae7148870cc2e33465d1807dfdc6efca33ad2c4e1f8943a79eead3c21311",
            "isPrimary": false
          }
        ]
      },
      "domain": "test",
      "customer_code": "CUS_xnxdt6s1zg1f4nx",
      "id": 1173,
      "integration": 100032,
      "createdAt": "2016-03-29T20:03:09.000Z",
      "updatedAt": "2016-03-29T20:53:05.000Z"
    },
    "plan": {
      "domain": "test",
      "name": "Monthly retainer (renamed)",
      "plan_code": "PLN_gx2wn530m0i3w3m",
      "description": null,
      "amount": 50000,
      "interval": "monthly",
      "send_invoices": true,
      "send_sms": true,
      "hosted_page": false,
      "hosted_page_url": null,
      "hosted_page_summary": null,
      "currency": "NGN",
      "id": 28,
      "integration": 100032,
      "createdAt": "2016-03-29T22:42:50.000Z",
      "updatedAt": "2016-03-29T23:51:41.000Z"
    },
    "integration": 100032,
    "authorization": {
      "authorization_code": "AUTH_6tmt288t0o",
      "bin": "408408",
      "last4": "4081",
      "exp_month": "12",
      "exp_year": "2020",
      "channel": "card",
      "card_type": "visa visa",
      "bank": "TEST BANK",
      "country_code": "NG",
      "brand": "visa",
      "reusable": true,
      "signature": "SIG_uSYN4fv1adlAuoij8QXh",
      "account_name": "BoJack Horseman"
    },
    "domain": "test",
    "start": **********,
    "status": "active",
    "quantity": 1,
    "amount": 50000,
    "subscription_code": "SUB_vsyqdmlzble3uii",
    "email_token": "d7gofp6yppn3qz7",
    "easy_cron_id": null,
    "cron_expression": "0 0 28 * *",
    "next_payment_date": "2016-04-28T07:00:00.000Z",
    "open_invoice": null,
    "id": 9,
    "createdAt": "2016-03-30T00:01:04.000Z",
    "updatedAt": "2016-03-30T00:22:58.000Z"
  }
}
```

---

## Enable Subscription

Enable a subscription on your integration.

### Headers

| Key           | Type   | Description                      |
|---------------|--------|----------------------------------|
| authorization | String | Set value to `Bearer SECRET_KEY` |
| content-type  | String | Set value to `application/json`  |

### Body Parameters

| Key   | Type   | Description          |
|-------|--------|----------------------|
| code  | String | Subscription code    |
| token | String | Email token          |

### Example Request

```js
const https = require('https')

const params = JSON.stringify({
  "code": "SUB_vsyqdmlzble3uii",
  "token": "d7gofp6yppn3qz7"
})

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/subscription/enable',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
    'Content-Type': 'application/json'
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.write(params)
req.end()
```

### Response

```json
{
  "status": true,
  "message": "Subscription enabled successfully"
}
```

---

## Disable Subscription

Disable a subscription on your integration.

### Headers

| Key           | Type   | Description                      |
|---------------|--------|----------------------------------|
| authorization | String | Set value to `Bearer SECRET_KEY` |
| content-type  | String | Set value to `application/json`  |

### Example Request

```js
const https = require('https')

const params = JSON.stringify({
  "code": "SUB_vsyqdmlzble3uii",
  "token": "d7gofp6yppn3qz7"
})

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/subscription/disable',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
    'Content-Type': 'application/json'
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.write(params)
req.end()
```

### Response

```json
{
  "status": true,
  "message": "Subscription disabled successfully"
}
```
