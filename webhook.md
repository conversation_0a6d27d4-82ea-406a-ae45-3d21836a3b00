
# Webhooks

Learn how to handle Flutterwave events on your webhook endpoint.

> 🚧 **Prerequisites for Webhook Implementation**  
> Before implementing a webhook, make sure you've read the following sections: Structure of a Webhook Payload, Implementing a Webhook, and Best Practices.

Webhooks are an important part of your payment integration. They allow Flutterwave to notify you about events that happen on your account, such as successful payment or a failed transaction. When your system receives a webhook notification, it's your responsibility to verify the information before taking any action. This helps your application process only legitimate and valid transactions.

A webhook URL is an endpoint on your server where these notifications are sent. When an event occurs, Flutterwave sends a POST request to that endpoint. The request contains a JSON body with details about the event, including the event type and associated data.

> 📘 **Flutterwave's IP Addresses are Dynamic and May Change Over Time**  
> To ensure seamless webhook integration, we recommend avoiding strict IP whitelisting. Instead, consider implementing signature verification for security.

---

## When to Use Webhooks

Webhooks are supported for all kinds of payment methods, but they're especially useful for methods and events that happen outside your application's control, such as:

- Getting paid via mobile money or USSD  
- A customer is being charged for their subscription (recurring payments)  
- A pending payment transitioning to successful  

These are all asynchronous actions, as your application does not control them, so you won't know when they are completed unless we notify you or you check later.

Setting up a webhook allows us to notify you when these payments are completed. Within your webhook endpoint, you can then:

- Update a customer's membership records in your database when a subscription payment succeeds.  
- Email a customer when a subscription payment fails.  
- Update your order records when the status of a pending payment is updated to successful.  

---

## Enabling Webhooks

Here is how to set up a webhook on your Flutterwave account:

1. Log in to your dashboard and click on **Settings**.  
2. Navigate to **Webhooks** to add your webhook URL.  
3. Check all the boxes and save your Settings.  

> ℹ️ **Tip**  
> When testing, you can get an instant webhook URL by visiting [webhook.site](https://webhook.site). This will allow you to inspect the received payload without having to write any code or set up a server.

---

## Structure of a Webhook Payload

All webhook payloads (except virtual card debit) follow the same basic structure:

- An `event` field describing the type of event.  
- A `data` object containing event-specific details, such as:
  - `id` — the ID of the transaction.  
  - `status` — the status of the transaction.  
  - Payment or customer details, if applicable.  

### Sample Payload

```json
{
  "event": "charge.completed",
  "data": {
    "id": *********,
    "tx_ref": "Links-************",
    "flw_ref": "PeterEkene/FLW270177170",
    "device_fingerprint": "a42937f4a73ce8bb8b8df14e63a2df31",
    "amount": 100,
    "currency": "NGN",
    "charged_amount": 100,
    "app_fee": 1.4,
    "merchant_fee": 0,
    "processor_response": "Approved by Financial Institution",
    "auth_model": "PIN",
    "ip": "*************",
    "narration": "CARD Transaction ",
    "status": "successful",
    "payment_type": "card",
    "created_at": "2020-07-06T19:17:04.000Z",
    "account_id": 17321,
    "customer": {
      "id": *********,
      "name": "Yemi Desola",
      "phone_number": null,
      "email": "<EMAIL>",
      "created_at": "2020-07-06T19:17:04.000Z"
    },
    "card": {
      "first_6digits": "123456",
      "last_4digits": "7889",
      "issuer": "VERVE FIRST CITY MONUMENT BANK PLC",
      "country": "NG",
      "type": "VERVE",
      "expiry": "02/23"
    }
  }
}
````

---

## Implementing a Webhook

Creating a webhook endpoint on your server is the same as writing any other API endpoint, but there are a few important details to note:

### Verifying Webhook Signatures

When enabling webhooks, you have the option to set a **secret hash**. Since webhook endpoints are publicly accessible, the secret hash allows you to verify that incoming requests are from Flutterwave. You should store the secret hash as an environment variable.

We’ll include the hash in the request header `verif-hash`. In your endpoint, check that the hash matches your secret hash. If not, discard the request.

---

### Responding to Webhook Requests

To acknowledge receipt of a webhook, your endpoint **must return a 200 HTTP status code**. Any other status code is treated as a failure.

---

### Webhook Timeout

Webhook requests timeout after **60 seconds**. Your webhook must respond within this window. If webhook retries are enabled, we'll resend the request.

> ℹ️ **Handling Webhook Retries**
> Be sure to enable webhook retries on your dashboard. We’ll retry failed webhook calls 3 times, at 30-minute intervals.

---

## Examples

> 🚧 **Rails and Django**
> Web frameworks like Rails or Django check POST requests for CSRF tokens. Be sure to exclude webhook endpoints from CSRF protection.

### Node.js (Express)

```js
// In an Express-like app:

app.post("/flw-webhook", (req, res) => {
    const secretHash = process.env.FLW_SECRET_HASH;
    const signature = req.headers["verif-hash"];
    if (!signature || (signature !== secretHash)) {
        res.status(401).end();
    }
    const payload = req.body;
    log(payload);
    res.status(200).end();
});
```

---

## Best Practices

### Always Verify Critical Transaction Data

Before giving value to a customer based on a webhook notification, **always re-query Flutterwave's API** to confirm the transaction details.

```js
const payload = req.body;
const response = await flw.Transaction.verify({id: payload.id});
if (
    response.data.status === "successful"
    && response.data.amount === expectedAmount
    && response.data.currency === expectedCurrency
    && response.data.tx_ref === expectedReference
) {
    // Success! Confirm the customer's payment
} else {
    // Inform the customer their payment was unsuccessful
}
```

---

### Don't Rely Solely on Webhooks

Have a **backup strategy**. Set up a job that polls the transaction verification endpoint for pending payments every few minutes until they’re resolved.

---

### Use a Secret Hash

Webhook endpoints are public. Use a **secret hash** to verify the authenticity of incoming requests.

---

### Respond Quickly

Avoid slow processing in the webhook. Respond immediately with a 200 status and handle logic asynchronously (e.g., job queue).

---

### Be Idempotent

Flutterwave may send the same webhook multiple times. Your logic should be **idempotent** to avoid duplicate processing.

```js
const payload = req.body;
const existingEvent = await PaymentEvent.where({id: payload.id}).find();
if (existingEvent.status === payload.status) {
    res.status(200).end();
}

// Record this event
await PaymentEvent.save(payload);
// Process event...
```


