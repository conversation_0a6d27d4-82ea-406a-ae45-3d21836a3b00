import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    domains: ["i.imgur.com", "0uknh5ir68.ufs.sh"],
    minimumCacheTTL: 60 * 60 * 24 * 7, // 7 days
    formats: ["image/webp"],
    deviceSizes: [640, 750, 828, 1080, 1200],
    imageSizes: [16, 32, 48, 64, 96, 128, 256],
    remotePatterns: [
      {
        protocol: "https",
        hostname: "0uknh5ir68.ufs.sh",
        pathname: "/f/**",
      },
    ],
  },
  serverExternalPackages: [
    "onnxruntime-node",
    "@huggingface/transformers",
    "chonkie",
  ],
  webpack: (config: any, { isServer }: { isServer: boolean }) => {
    // Exclude large dependencies from the server bundle
    if (isServer) {
      config.externals = [
        ...(config.externals || []),
        "onnxruntime-node",
        "@huggingface/transformers",
        "chonkie",
      ];
    }

    return config;
  },
  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://us.i.posthog.com/:path*",
      },
      {
        source: "/ingest/decide",
        destination: "https://us.i.posthog.com/decide",
      },
    ];
  },
  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,

  // Exclude test endpoints in production
  async redirects() {
    if (process.env.NODE_ENV === "production") {
      return [
        {
          source: "/api/webhooks/test",
          destination: "/404",
          permanent: false,
        },
      ];
    }
    return [];
  },
};

export default nextConfig;
