export type ContentType = "youtube" | "pdf" | "text" | "webpage";
export type OutputType = "summary" | "notes";

export interface ContentSegment {
  text: string;
  offset?: number;
  duration?: number;
}

export interface ContentMetadata {
  title: string;
  sourceId: string;
  contentId: string;
  contentType: ContentType;
  pageCount?: number;
  isOcr?: boolean;
  author?: string;
  url?: string;
  shareToken?: string;
  isShared?: boolean;
}

export interface ProgressInfo {
  progress: number;
  stage: string;
  message?: string;
  estimatedTimeRemaining?: number;
  formattedTimeRemaining?: string;
}

export interface ContentState {
  // State properties
  contentType: ContentType;
  contentId: string;
  sourceContent: string | ContentSegment[];
  outputContent: string;
  outputType: OutputType;
  error: string;
  metadata: ContentMetadata | null;
  isSourceLoading: boolean;
  isOutputLoading: boolean;
  generatedOutputType: OutputType | null;
  translatedOutput: string;
  isNewTransformation: boolean;

  // Methods
  setSourceContent: (content: string | ContentSegment[]) => void;
  setOutputContent: (content: string) => void;
  setError: (error: string) => void;
  setMetadata: (metadata: Partial<ContentMetadata>) => void;
  setSourceLoading: (isLoading: boolean) => void;
  setOutputLoading: (isLoading: boolean) => void;
  resetState: () => void;
  setContentType: (type: ContentType) => void;
  setContentId: (id: string) => void;
  setOutputType: (type: OutputType) => void;
  setIsNewTransformation: (isNew: boolean) => void;
  updateState: (updates: Record<string, unknown>) => void;
  generateOutput: () => Promise<void>;
  generateContentId: () => string;
  setTranslatedOutput: (translated: string) => void;
}
