import { PlanType } from "@/data/pricingData";

export interface BillingHistoryItem {
  id: string;
  planType: string;
  status: string;
  billingCycle: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  cancelAtPeriodEnd: boolean;
  canceledAt: string | null;
  createdAt: string;
  updatedAt: string;
  amount?: number;
  currency?: string;
}

export interface CreateSubscriptionRequest {
  planType: PlanType;
  billingCycle: "monthly" | "annual";

  customerInfo: {
    email: string;
    name: string;
    phone?: string;
  };
}

export interface SubscriptionData {
  planType: PlanType;
  status: string | null; // SubscriptionStatus: PENDING | ACTIVE | NON_RENEWING | ATTENTION | COMPLETED | CANCELLED
  pendingPlanType?: PlanType; // Add this for abandoned checkouts
  pendingPriceId?: string; // Added
  pendingPlanEffectiveDate?: string; // Added (typically string if from JSON)
  billingCycle?: string;
  currentPeriodStart?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  usage: {
    transformations: { count: number; limit: number };
    quizzes: { count: number; limit: number };
    flashcards: { count: number; limit: number };
    aiChat: { count: number; limit: number };
  };
}

export interface UsageLimitResult {
  allowed: boolean;
  current: number;
  limit: number;
  percentage: number;
  unlimited?: boolean;
  remaining?: number;
}

export enum FeatureType {
  TRANSFORMATIONS = "transformations",
  QUIZZES = "quizzes",
  FLASHCARDS = "flashcards",
  AI_CHAT = "aiChat",
}

export interface UsageData {
  transformations: { count: number; limit: number };
  quizzes: { count: number; limit: number };
  flashcards: { count: number; limit: number };
  aiChat: { count: number; limit: number };
}

export interface UsageMetadata {
  userAgent?: string;
  ipAddress?: string;
  timestamp: Date;
  requestId?: string;
  contentId?: string;
}

export interface PlanLimits {
  transformations?: { weekly?: number; unlimited?: boolean };
  quizzes?: { perContent?: number; unlimited?: boolean };
  flashcards?: { perContent?: number; unlimited?: boolean };
  aiChat?: { daily?: number; unlimited?: boolean };
  sharing?: boolean;
  translation?: boolean;
  analytics?: boolean;
  priority?: boolean;
  [key: string]: any; // Allow dynamic access
}

export interface UsageCheckRequest {
  feature: string;
  increment?: number;
}

export interface UsageIncrementRequest {
  feature: string;
  increment?: number;
}

export interface CancelSubscriptionRequest {
  cancelAtPeriodEnd?: boolean;
}

export interface CancelSubscriptionResponse {
  success: boolean;
  message: string;
  subscription: {
    id: string;
    status: string;
    cancelAtPeriodEnd: boolean;
    canceledAt: string | null;
    currentPeriodEnd: string;
  };
}

export interface SubscriptionState {
  subscription: SubscriptionData | null;
  loading: boolean;
  error: string | null;
  lastFetched: number | null;

  // Billing History State
  billingHistory: BillingHistoryItem[];
  historyLoading: boolean;
  historyError: string | null;
  historyLastFetched: number | null;

  // Actions
  setSubscription: (subscription: SubscriptionData | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  fetchSubscription: (forceRefresh?: boolean) => Promise<void>;
  reactivateSubscription: () => Promise<{ success: boolean; message: string }>;
  reactivating: boolean; // Added property

  // Billing History Actions
  setBillingHistory: (history: BillingHistoryItem[]) => void;
  setHistoryLoading: (loading: boolean) => void;
  setHistoryError: (error: string | null) => void;
  fetchBillingHistory: (forceRefresh?: boolean) => Promise<void>;

  createSubscription: (
    planType: PlanType,
    billingCycle: "monthly" | "annual",
    customerInfo: {
      email: string;
      name: string;
      phone?: string;
    }
  ) => Promise<{
    success?: boolean;
    subscriptionId: string;
    paymentLink: string;
    isUpgrade: boolean;
    message?: string;
  }>;
  cancelSubscription: (
    cancelAtPeriodEnd?: boolean
  ) => Promise<{ success: boolean; message: string }>;
  clearSubscription: () => void;
}
