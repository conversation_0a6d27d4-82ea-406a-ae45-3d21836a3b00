declare module 'flutterwave-node-v3' {
  interface FlutterwaveConfig {
    public_key: string;
    secret_key: string;
  }

  interface FlutterwaveCustomer {
    email: string;
    name: string;
    phone?: string;
    phonenumber?: string;
  }

  interface FlutterwaveCustomizations {
    title: string;
    description: string;
    logo?: string;
  }

  interface FlutterwavePaymentData {
    tx_ref: string;
    amount: number;
    currency: string;
    redirect_url: string;
    customer: FlutterwaveCustomer;
    customizations: FlutterwaveCustomizations;
    payment_plan?: string;
    meta?: Record<string, any>;
  }

  interface FlutterwaveResponse {
    status: string;
    message: string;
    data?: any;
  }

  interface FlutterwaveStandardResponse {
    status: string;
    message: string;
    data?: {
      link: string;
      id?: number;
      tx_ref?: string;
      flw_ref?: string;
    };
  }

  interface FlutterwaveTransactionResponse {
    status: string;
    message: string;
    data?: {
      id: number;
      tx_ref: string;
      flw_ref: string;
      device_fingerprint: string;
      amount: number;
      currency: string;
      charged_amount: number;
      app_fee: number;
      merchant_fee: number;
      processor_response: string;
      auth_model: string;
      ip: string;
      narration: string;
      status: string;
      payment_type: string;
      created_at: string;
      account_id: number;
      customer: FlutterwaveCustomer;
    };
  }

  interface FlutterwaveSubscriptionResponse {
    status: string;
    message: string;
    data?: {
      id: number;
      status: string;
      plan: string;
      customer: FlutterwaveCustomer;
      created_at: string;
      updated_at: string;
    };
  }

  class FlutterwavePayment {
    standard(data: FlutterwavePaymentData): Promise<FlutterwaveStandardResponse>;
  }

  class FlutterwaveTransaction {
    verify(params: { id: string | number }): Promise<FlutterwaveTransactionResponse>;
  }

  class FlutterwaveSubscription {
    cancel(params: { id: string | number }): Promise<FlutterwaveResponse>;
    fetch(params: { id: string | number }): Promise<FlutterwaveSubscriptionResponse>;
    activate(params: { id: string | number }): Promise<FlutterwaveResponse>;
  }

  class Flutterwave {
    constructor(publicKey: string, secretKey: string);
    
    Payment: FlutterwavePayment;
    Transaction: FlutterwaveTransaction;
    Subscription: FlutterwaveSubscription;
  }

  export = Flutterwave;
}
