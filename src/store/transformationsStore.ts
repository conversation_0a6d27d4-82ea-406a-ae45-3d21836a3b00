// store/transformationsStore.ts
import { create } from "zustand";

// Updated interface for content summaries
export interface ContentSummary {
  id: string;
  contentId: string;
  title: string;
  content: string;
  contentType: string;
  sourceId: string;
  outputType: string;
  generatedAt: Date;
  isShared: boolean;
  shareToken?: string;
}

interface PaginationData {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  isLoadingMore?: boolean;
  hasMore?: boolean;
}

interface TransformationsStore {
  content: ContentSummary[];
  filteredContent: ContentSummary[];
  isLoading: boolean;
  error: string | null;
  dataFetched: boolean;
  searchQuery: string;
  lastFetchTime: number;
  pagination: PaginationData;
  fetchedPages: Set<number>;
  fetchInitialContent: () => Promise<void>;
  fetchContent: () => Promise<void>;
  fetchPageContent: (page: number, pageSize?: number) => Promise<void>;
  deleteContent: (contentId: string) => Promise<boolean>;
  setSearchQuery: (query: string) => void;
  resetDataFetchedFlag: () => void;
  resetPagination: () => void;
}

export const useTransformationsStore = create<TransformationsStore>(
  (set, get) => ({
    content: [],
    filteredContent: [],
    isLoading: false,
    error: null,
    dataFetched: false,
    searchQuery: "",
    lastFetchTime: 0,
    pagination: {
      page: 1,
      pageSize: 12,
      totalItems: 0,
      totalPages: 1,
      isLoadingMore: false,
      hasMore: false,
    },
    fetchedPages: new Set<number>(),

    fetchInitialContent: async () => {
      const state = get();
      const now = Date.now();
      const timeSinceLastFetch = now - state.lastFetchTime;
      const REFRESH_INTERVAL = 120000; // 2 minutes

      // Only fetch if:
      // 1. Data hasn't been fetched yet
      // 2. It's been more than REFRESH_INTERVAL since last fetch
      // 3. Not currently loading
      if (
        (!state.dataFetched || timeSinceLastFetch > REFRESH_INTERVAL) &&
        !state.isLoading
      ) {
        try {
          set({
            isLoading: true,
            error: null,
            // Reset fetchedPages to ensure we get fresh data
            fetchedPages: new Set<number>(),
          });

          // Fetch first page
          await get().fetchPageContent(1);

          // Note: We don't need to set dataFetched and lastFetchTime here
          // as they're now set in fetchPageContent
        } catch (error) {
          console.error("Error fetching initial content:", error);
          set({
            error: (error as Error).message,
            isLoading: false,
          });
        }
      }
    },

    fetchContent: async () => {
      // Legacy method - now just calls fetchInitialContent
      await get().fetchInitialContent();
    },

    fetchPageContent: async (page: number, pageSize = 12) => {
      const state = get();

      // For "Load More" functionality, we should only skip if we're currently loading
      // Allow fetching new pages that haven't been fetched yet
      if (state.fetchedPages.has(page)) {
        return;
      }

      try {
        // Only set isLoading to true for the first page
        // For subsequent pages, we'll use a separate loading indicator
        if (page === 1) {
          set({ isLoading: true, error: null });
        } else {
          // For subsequent pages, we'll set a loadingMore flag
          set((state) => ({
            ...state,
            pagination: {
              ...state.pagination,
              isLoadingMore: true,
            },
          }));
        }

        const response = await fetch(
          `/api/transformations?page=${page}&pageSize=${pageSize}`,
          {
            // Add cache headers for better performance
            headers: {
              "Cache-Control": "max-age=60", // Cache for 1 minute
            },
          }
        );

        // Check if response is JSON before parsing
        const contentType = response.headers.get("content-type");
        if (!contentType || !contentType.includes("application/json")) {
          const text = await response.text();
          console.error("Non-JSON response received:", text);
          throw new Error("Server returned an invalid response format");
        }

        const data = await response.json();

        if (!response.ok)
          throw new Error(data.error || "Failed to fetch content");

        // Optimize content processing - only process dates, avoid unnecessary operations
        const processedContent = data.content.map(
          (item: {
            id: string;
            contentId: string;
            title: string;
            content?: string;
            contentType: string;
            sourceId: string;
            outputType: string;
            generatedAt: string | Date;
            isShared: boolean;
            shareToken?: string;
          }) => ({
            ...item,
            generatedAt: new Date(item.generatedAt),
            // Ensure content field exists (might be undefined if not requested)
            content: item.content || "",
          })
        );

        // Extract pagination info from API response
        const pagination = {
          page,
          pageSize,
          totalItems: data.pagination?.totalItems || 0,
          totalPages: data.pagination?.totalPages || 1,
          isLoadingMore: false,
          hasMore: page < (data.pagination?.totalPages || 1),
        };

        // Update the set of fetched pages
        const fetchedPages = new Set(get().fetchedPages);
        fetchedPages.add(page);

        // If it's the first page, replace the content array
        // For other pages, append the new content to the existing array
        let updatedContent;
        if (page === 1) {
          updatedContent = processedContent;
        } else {
          // Create a new array with existing content
          updatedContent = [...get().content];

          // Add new items that don't exist in our current content
          const existingIds = new Set(
            updatedContent.map((item: ContentSummary) => item.id)
          );

          processedContent.forEach((item: ContentSummary) => {
            if (!existingIds.has(item.id)) {
              updatedContent.push(item);
            }
          });
        }

        // Update filtered content only if there's a search query
        const currentSearchQuery = get().searchQuery;
        const filteredContent = currentSearchQuery
          ? updatedContent.filter(
              (item: ContentSummary) =>
                item.title
                  .toLowerCase()
                  .includes(currentSearchQuery.toLowerCase()) ||
                item.contentType
                  .toLowerCase()
                  .includes(currentSearchQuery.toLowerCase())
            )
          : updatedContent;

        set({
          content: updatedContent,
          filteredContent,
          isLoading: false,
          pagination,
          fetchedPages,
          dataFetched: true,
          lastFetchTime: Date.now(),
        });
      } catch (error) {
        console.error("Error fetching page content:", error);
        set({
          error: (error as Error).message,
          isLoading: false,
          pagination: {
            ...get().pagination,
            isLoadingMore: false,
          },
        });
      }
    },

    resetDataFetchedFlag: () => {
      set({ dataFetched: false });
    },

    resetPagination: () => {
      set({
        fetchedPages: new Set<number>(),
        pagination: {
          page: 1,
          pageSize: 12,
          totalItems: 0,
          totalPages: 1,
          isLoadingMore: false,
          hasMore: false,
        },
      });
    },

    setSearchQuery: (query: string) => {
      const searchTerm = query.toLowerCase().trim();
      const currentContent = get().content;

      // Optimize search by avoiding unnecessary filtering
      const filteredContent = searchTerm
        ? currentContent.filter(
            (item) =>
              item.title.toLowerCase().includes(searchTerm) ||
              item.contentType.toLowerCase().includes(searchTerm)
          )
        : currentContent;

      set({
        searchQuery: query,
        filteredContent,
      });
    },

    deleteContent: async (contentId: string) => {
      try {
        const response = await fetch(`/api/transformations/${contentId}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || "Failed to delete content");
        }

        // Update local state on successful deletion
        set((state) => {
          const updatedContent = state.content.filter(
            (item) => item.id !== contentId
          );
          const updatedFilteredContent = state.filteredContent.filter(
            (item) => item.id !== contentId
          );

          // Update pagination total items
          const updatedPagination = {
            ...state.pagination,
            totalItems: Math.max(0, state.pagination.totalItems - 1),
            totalPages: Math.ceil(
              Math.max(0, state.pagination.totalItems - 1) /
                state.pagination.pageSize
            ),
          };

          return {
            content: updatedContent,
            filteredContent: updatedFilteredContent,
            pagination: updatedPagination,
          };
        });

        return true;
      } catch (error) {
        console.error("Error deleting content:", error);
        set({ error: (error as Error).message });
        return false;
      }
    },
  })
);
