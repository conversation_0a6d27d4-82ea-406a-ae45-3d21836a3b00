import { create } from "zustand";

interface UsageStats {
  current: number;
  limit: number;
  remaining: number;
  percentage: number;
  unlimited: boolean;
  allowed: boolean;
}

interface DashboardStore {
  stats: {
    totalContent: number;
  };
  usage: {
    transformations: UsageStats;
    quizzes: UsageStats;
    flashcards: UsageStats;
    aiChat: UsageStats;
  };
  planType: string;
  isLoading: boolean;
  error: string | null;
  currentUserId: string | null;
  fetchDashboardData: (userId: string, force?: boolean) => Promise<void>;
  clearUserData: () => void;
}

export const useDashboardStore = create<DashboardStore>((set, get) => ({
  stats: {
    totalContent: 0,
  },
  usage: {
    transformations: {
      current: 0,
      limit: 0,
      remaining: 0,
      percentage: 0,
      unlimited: false,
      allowed: true,
    },
    quizzes: {
      current: 0,
      limit: 0,
      remaining: 0,
      percentage: 0,
      unlimited: false,
      allowed: true,
    },
    flashcards: {
      current: 0,
      limit: 0,
      remaining: 0,
      percentage: 0,
      unlimited: false,
      allowed: true,
    },
    aiChat: {
      current: 0,
      limit: 0,
      remaining: 0,
      percentage: 0,
      unlimited: false,
      allowed: true,
    },
  },
  planType: "Free",
  isLoading: false,
  error: null,
  currentUserId: null,

  fetchDashboardData: async (userId: string, force: boolean = false) => {
    const state = get();

    // Clear data if user changed
    if (state.currentUserId && state.currentUserId !== userId) {
      get().clearUserData();
    }

    if (!state.isLoading || force) {
      try {
        set({ isLoading: true, error: null, currentUserId: userId });

        // Fetch usage data and total content count in parallel
        const [usageResponse, statsResponse] = await Promise.all([
          fetch("/api/dashboard/usage"),
          fetch("/api/users/stats"), // Added call to the new stats endpoint
        ]);

        if (!usageResponse.ok) {
          const usageData = await usageResponse.json();
          throw new Error(usageData.error || "Failed to fetch usage data");
        }
        const usageData = await usageResponse.json();

        let totalContent = state.stats.totalContent; // Default to current if stats fetch fails
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          totalContent =
            statsData.totalContent !== undefined
              ? statsData.totalContent
              : totalContent;
        } else {
          console.warn(
            "Failed to fetch user stats (totalContent), using previous value or default."
          );
        }

        const newUsage = {
          transformations:
            usageData.usage?.transformations || state.usage.transformations,
          quizzes: usageData.usage?.quizzes || state.usage.quizzes,
          flashcards: usageData.usage?.flashcards || state.usage.flashcards,
          aiChat: usageData.usage?.aiChat || state.usage.aiChat,
        };

        set({
          usage: newUsage,
          stats: {
            ...state.stats,
            totalContent: totalContent, // Update totalContent
          },
          planType: usageData.planType || "Free",
          isLoading: false,
          error: null, // Clear error on success
        });
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        set({
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch dashboard data",
          isLoading: false,
        });
      }
    }
  },

  clearUserData: () => {
    set({
      stats: {
        totalContent: 0, // Reset
      },
      usage: {
        transformations: {
          current: 0,
          limit: 0,
          remaining: 0,
          percentage: 0,
          unlimited: false,
          allowed: true,
        },
        quizzes: {
          current: 0,
          limit: 0,
          remaining: 0,
          percentage: 0,
          unlimited: false,
          allowed: true,
        },
        flashcards: {
          current: 0,
          limit: 0,
          remaining: 0,
          percentage: 0,
          unlimited: false,
          allowed: true,
        },
        aiChat: {
          current: 0,
          limit: 0,
          remaining: 0,
          percentage: 0,
          unlimited: false,
          allowed: true,
        },
      },
      planType: "Free",
      currentUserId: null,
      error: null,
      isLoading: false, // Ensure loading is reset
    });
  },
}));
