import { create } from "zustand";
import { persist } from "zustand/middleware";
import { PlanType } from "@/data/pricingData";

import { SubscriptionState } from "@/types/subscription";

// Cache duration: 1 minute (reduced from 5 minutes)
const CACHE_DURATION = 60 * 1000;

export const useSubscriptionStore = create<SubscriptionState>()(
  persist(
    (set, get) => ({
      subscription: null,
      loading: false,
      error: null,
      lastFetched: null,
      reactivating: false, // Initialize reactivating state

      // Billing History State
      billingHistory: [],
      historyLoading: false,
      historyError: null,
      historyLastFetched: null,

      setSubscription: (subscription) => {
        set({
          subscription,
          lastFetched: Date.now(),
          error: null,
        });
      },

      setLoading: (loading) => set({ loading }),

      setError: (error) => set({ error, loading: false }),

      // Billing History Actions
      setBillingHistory: (billingHistory) => {
        set({
          billingHistory,
          historyLastFetched: Date.now(),
          historyError: null,
        });
      },

      setHistoryLoading: (historyLoading) => set({ historyLoading }),

      setHistoryError: (historyError) =>
        set({ historyError, historyLoading: false }),

      fetchSubscription: async (forceRefresh = false) => {
        const state = get();

        // Check cache validity (skip if force refresh is requested)
        if (
          !forceRefresh &&
          state.subscription &&
          state.lastFetched &&
          Date.now() - state.lastFetched < CACHE_DURATION
        ) {
          return;
        }

        try {
          set({ loading: true, error: null });

          const response = await fetch("/api/subscriptions/status", {
            cache: "no-store",
            headers: {
              "Cache-Control": "no-cache",
            },
          });

          if (!response.ok) {
            throw new Error("Failed to fetch subscription");
          }

          const data = await response.json();

          set({
            subscription: data,
            loading: false,
            error: null,
            lastFetched: Date.now(),
          });
        } catch (err) {
          const errorMessage =
            err instanceof Error ? err.message : "Unknown error";
          set({
            error: errorMessage,
            loading: false,
          });
        }
      },

      fetchBillingHistory: async (forceRefresh = false) => {
        const state = get();

        // Check cache validity (skip if force refresh is requested)
        if (
          !forceRefresh &&
          state.billingHistory.length > 0 &&
          state.historyLastFetched &&
          Date.now() - state.historyLastFetched < CACHE_DURATION
        ) {
          return;
        }

        try {
          set({ historyLoading: true, historyError: null });

          const response = await fetch("/api/subscriptions/history", {
            cache: "no-store",
            headers: {
              "Cache-Control": "no-cache",
            },
          });

          if (!response.ok) {
            throw new Error("Failed to fetch billing history");
          }

          const data = await response.json();

          set({
            billingHistory: data.subscriptions || [],
            historyLoading: false,
            historyError: null,
            historyLastFetched: Date.now(),
          });
        } catch (err) {
          const errorMessage =
            err instanceof Error ? err.message : "Unknown error";
          set({
            historyError: errorMessage,
            historyLoading: false,
          });
        }
      },

      createSubscription: async (
        planType,
        billingCycle,
        customerInfo
      ) => {
        try {
          const response = await fetch("/api/subscriptions/create", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              planType,
              billingCycle,
              customerInfo,
            }),
          });

          const data = await response.json();
          if (response.ok) {
            // Force refresh subscription data after creation
            await get().fetchSubscription(true);
            return {
              success: true,
              subscriptionId: data.subscriptionId,
              paymentLink: data.paymentLink,
              transactionRef: data.transactionRef,
              accessCode: data.accessCode,
              isUpgrade: data.isUpgrade,
              message: data.message,
            };
          } else {
            throw new Error(data.error || "Failed to create subscription");
          }
        } catch (err) {
          console.error("Subscription creation error:", err);
          throw err;
        }
      },

      reactivateSubscription: async () => {
        set({ reactivating: true });
        try {
          const response = await fetch("/api/subscriptions/reactivate", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || "Failed to reactivate subscription");
          }

          // Force refresh subscription data after reactivation
          await get().fetchSubscription(true);
          return data;
        } catch (err) {
          console.error("Subscription reactivation error:", err);
          throw err;
        } finally {
          set({ reactivating: false });
        }
      },

      cancelSubscription: async (cancelAtPeriodEnd = true) => {
        try {
          const response = await fetch("/api/subscriptions/cancel", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ cancelAtPeriodEnd }),
          });

          const data = await response.json();

          if (response.ok) {
            // Force refresh subscription data after cancellation
            await get().fetchSubscription(true);
            return data;
          } else {
            throw new Error(data.error || "Failed to cancel subscription");
          }
        } catch (err) {
          console.error("Subscription cancellation error:", err);
          throw err;
        }
      },

      changePlan: async (
        newPlanType: PlanType,
        newBillingCycle: "monthly" | "annual"
      ) => {
        try {
          const response = await fetch("/api/subscriptions/change-plan", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              newPlanType,
              newBillingCycle,
            }),
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || "Failed to initiate plan change");
          }

          // Force refresh subscription data after initiating plan change
          // This will show the subscription as cancelled but still active
          await get().fetchSubscription(true);

          return {
            success: true,
            checkoutUrl: data.checkoutUrl,
            message: data.message || "Plan change initiated. Complete checkout to activate new plan.",
            pendingPlan: newPlanType,
            pendingBillingCycle: newBillingCycle
          };
        } catch (err) {
          console.error("Plan change error:", err);
          throw err;
        }
      },

      clearSubscription: () => {
        set({
          subscription: null,
          loading: false,
          error: null,
          lastFetched: null,
        });
      },
    }),
    {
      name: "subscription-store",
      partialize: (state) => ({
        subscription: state.subscription,
        lastFetched: state.lastFetched,
        billingHistory: state.billingHistory,
        historyLastFetched: state.historyLastFetched,
      }),
    }
  )
);

// Convenience hook for components that only need subscription data
export const useSubscription = () => {
  const { subscription, loading, error, fetchSubscription } =
    useSubscriptionStore();

  return {
    subscription,
    loading,
    error,
    refetch: fetchSubscription,
  };
};

// Convenience hook for components that only need billing history
export const useBillingHistory = () => {
  const { billingHistory, historyLoading, historyError, fetchBillingHistory } =
    useSubscriptionStore();

  return {
    billingHistory,
    loading: historyLoading,
    error: historyError,
    refetch: fetchBillingHistory,
  };
};
