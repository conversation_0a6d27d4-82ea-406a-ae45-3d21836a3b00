import { create } from "zustand";
import { nanoid } from "nanoid";
import {
  ContentType,
  ContentMetadata,
  OutputType,
  ContentSegment,
  ContentState
} from "@/types/contentTypes";

export const useContentStore = create<ContentState>((set, get) => ({
  contentType: "youtube",
  contentId: "",
  sourceContent: [],
  outputContent: "",
  outputType: "summary",
  error: "",
  metadata: null,
  isSourceLoading: false,
  isOutputLoading: false,
  generatedOutputType: null,
  translatedOutput: "",
  isNewTransformation: false,

  // Helper function for selective state updates
  updateState: (updates: Record<string, unknown>) =>
    set((state) => ({ ...state, ...updates })),

  setContentType: (contentType: ContentType) =>
    set((state) => ({
      ...state,
      contentType,
      sourceContent: [],
      outputContent: "",
      error: "",
      metadata: null,
      generatedOutputType: null,
    })),

  setContentId: (contentId: string) =>
    set((state) => ({ ...state, contentId })),

  generateContentId: () => {
    const id = nanoid(10);
    set((state) => ({ ...state, contentId: id }));
    return id;
  },

  setSourceContent: (sourceContent: string | ContentSegment[]) =>
    set((state) => ({ ...state, sourceContent })),

  setOutputContent: (outputContent: string) =>
    set((state) => ({ ...state, outputContent })),

  setOutputType: (outputType: OutputType) =>
    set((state) => ({
      ...state,
      outputType,
      outputContent:
        state.generatedOutputType === outputType ? state.outputContent : "",
      generatedOutputType:
        state.generatedOutputType === outputType
          ? state.generatedOutputType
          : null,
    })),

  setError: (error: string) => set((state) => ({ ...state, error })),

  setMetadata: (metadataUpdate: Partial<ContentMetadata>) =>
    set((state) => {
      if (!state.metadata && !metadataUpdate.contentId) {
        const contentId = nanoid(10);
        return {
          metadata: {
            ...metadataUpdate,
            contentId,
            contentType: metadataUpdate.contentType || state.contentType,
            title: metadataUpdate.title || "",
            sourceId: metadataUpdate.sourceId || "",
          } as ContentMetadata,
          contentId,
        };
      }

      return {
        metadata: state.metadata
          ? { ...state.metadata, ...metadataUpdate }
          : ({
              contentId: metadataUpdate.contentId || nanoid(10),
              contentType: metadataUpdate.contentType || state.contentType,
              title: metadataUpdate.title || "",
              sourceId: metadataUpdate.sourceId || "",
              ...metadataUpdate,
            } as ContentMetadata),
      };
    }),

  setSourceLoading: (isSourceLoading: boolean) =>
    set((state) => ({ ...state, isSourceLoading })),

  setOutputLoading: (isOutputLoading: boolean) =>
    set((state) => ({ ...state, isOutputLoading })),

  setTranslatedOutput: (translatedOutput: string) =>
    set((state) => ({ ...state, translatedOutput })),

  setIsNewTransformation: (isNewTransformation: boolean) =>
    set((state) => ({ ...state, isNewTransformation })),

  generateOutput: async () => {
    const {
      sourceContent,
      outputType,
      metadata,
      isOutputLoading,
      outputContent,
      generatedOutputType,
      contentType,
    } = get();

    if (
      isOutputLoading ||
      !sourceContent ||
      !metadata ||
      (outputContent && generatedOutputType === outputType)
    ) {
      return;
    }

    set({
      isOutputLoading: true,
      error: "",
    });

    try {
      const response = await fetch("/api/transform", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sourceContent,
          outputType,
          metadata,
          contentType,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (data.isRateLimit) {
          throw new Error(
            "Rate limit reached. Please wait a minute before trying again."
          );
        }

        if (response.status === 504 && data.isTimeout) {
          throw new Error(
            "The request took too long to process. Please try again with shorter content."
          );
        }
        throw new Error(data.error || "Failed to generate output");
      }

      if (!data.outputContent) {
        throw new Error("No output content received from server");
      }

      // Set content
      set({
        outputContent: data.outputContent,
        generatedOutputType: outputType,
      });
    } catch (error) {
      // Show error message
      set({
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        outputContent: "",
      });
    } finally {
      set({ isOutputLoading: false });
    }
  },

  resetState: () => {
    set({
      contentType: "text",
      contentId: "",
      sourceContent: [],
      outputContent: "",
      outputType: "notes",
      error: "",
      metadata: null,
      isSourceLoading: false,
      isOutputLoading: false,
      generatedOutputType: null,
      translatedOutput: "",
      isNewTransformation: false,
    });
  },
}));
