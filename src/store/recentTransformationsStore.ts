import { create } from "zustand";

interface RecentContentSummary {
  id: string;
  contentId: string;
  title: string;
  contentType: string;
  outputType: string;
  generatedAt: Date;
}

interface RecentTransformationsStore {
  recentContent: RecentContentSummary[];
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number;
  fetchRecentTransformations: (force?: boolean) => Promise<void>;
  startPolling: () => void;
  stopPolling: () => void;
}

const POLLING_INTERVAL = 60 * 1000; // 1 minute
let pollIntervalId: NodeJS.Timeout | null = null;

export const useRecentTransformationsStore = create<RecentTransformationsStore>(
  (set, get) => ({
    recentContent: [],
    isLoading: false,
    error: null,
    lastFetchTime: 0,

    fetchRecentTransformations: async (force: boolean = false) => {
      const state = get();
      const now = Date.now();
      const timeSinceLastFetch = now - state.lastFetchTime;

      // Fetch if forced or if not currently loading and interval has passed (or never fetched)
      const shouldFetch =
        force ||
        (!state.isLoading &&
          (state.lastFetchTime === 0 ||
            timeSinceLastFetch > POLLING_INTERVAL / 2));

      if (shouldFetch) {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch("/api/recent-transformations");
          if (!response.ok) {
            const data = await response.json();
            throw new Error(
              data.error || "Failed to fetch recent transformations"
            );
          }
          const data = await response.json();
          const processedContent =
            data.recentContent?.map((item: any) => ({
              ...item,
              generatedAt:
                item.generatedAt instanceof Date
                  ? item.generatedAt
                  : new Date(item.generatedAt),
            })) || [];

          set({
            recentContent: processedContent,
            isLoading: false,
            lastFetchTime: now,
            error: null,
          });
        } catch (error) {
          console.error("Error fetching recent transformations:", error);
          set({
            error:
              error instanceof Error
                ? error.message
                : "Failed to fetch recent transformations",
            isLoading: false,
          });
        }
      }
    },

    startPolling: () => {
      get().stopPolling(); // Clear any existing interval
      get().fetchRecentTransformations(true); // Fetch immediately
      pollIntervalId = setInterval(() => {
        get().fetchRecentTransformations();
      }, POLLING_INTERVAL);
      console.log("Recent transformations polling started.");
    },

    stopPolling: () => {
      if (pollIntervalId) {
        clearInterval(pollIntervalId);
        pollIntervalId = null;
        console.log("Recent transformations polling stopped.");
      }
    },
  })
);

// Initialize polling when the store is first used or app loads
// This might be better handled in a global app setup component (e.g., _app.tsx or Layout)
// For now, let's assume it's called from a relevant part of the app.
// Example: useRecentTransformationsStore.getState().startPolling();
