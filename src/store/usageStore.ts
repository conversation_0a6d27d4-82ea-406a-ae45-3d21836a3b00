import { create } from "zustand";
import { persist } from "zustand/middleware";
import { FeatureType } from "@/types/subscription";
import { UsageCheckResult } from "@/lib/usage-utils";

// Define PlanType locally since we're moving away from Prisma
type PlanType = "Free" | "Starter" | "Pro" | "Unlimited";

interface UsageState {
  // Core usage data
  usageData: Record<FeatureType, UsageCheckResult>;

  // Plan information
  planType: PlanType;

  // UI state
  isLoading: boolean;
  error: string | null;
  lastFetched: Date | null;

  // Actions
  fetchUsageData: () => Promise<void>;
  checkFeatureUsage: (feature: FeatureType) => Promise<UsageCheckResult>;
  recordUsage: (feature: FeatureType) => void;

  // Getters
  getUsageForFeature: (feature: FeatureType) => UsageCheckResult | null;
  hasReachedLimit: (feature: FeatureType) => boolean;
}

export const useUsageStore = create<UsageState>()(
  persist(
    (set, get) => ({
      // Initial state
      usageData: {} as Record<FeatureType, UsageCheckResult>,
      planType: "Free" as PlanType,
      isLoading: false,
      error: null,
      lastFetched: null,

      // Actions
      fetchUsageData: async () => {
        set({ isLoading: true, error: null });

        try {
          const response = await fetch("/api/dashboard/usage");
          if (!response.ok) {
            throw new Error("Failed to fetch usage data");
          }

          const data = await response.json();

          set({
            usageData: data.usage,
            planType: data.planType,
            lastFetched: new Date(),
            isLoading: false,
          });
        } catch (error) {
          console.error("Error fetching usage data:", error);
          set({
            error: error instanceof Error ? error.message : "Unknown error",
            isLoading: false,
          });
        }
      },

      checkFeatureUsage: async (
        feature: FeatureType
      ): Promise<UsageCheckResult> => {
        try {
          const response = await fetch("/api/dashboard/usage/check", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ feature }),
          });

          if (!response.ok) {
            throw new Error("Failed to check usage limit");
          }

          const result = await response.json();

          // Update local state
          set({
            usageData: {
              ...get().usageData,
              [feature]: result,
            },
          });

          return result;
        } catch (error) {
          console.error("Error checking usage limit:", error);
          return {
            allowed: false,
            current: 0,
            limit: 0,
            percentage: 100,
            unlimited: false,
            remaining: 0,
            message: "Unable to check usage limit",
          };
        }
      },

      recordUsage: (feature: FeatureType) => {
        // Optimistically update local state
        const currentData = get().usageData[feature];
        if (currentData && !currentData.unlimited) {
          set({
            usageData: {
              ...get().usageData,
              [feature]: {
                ...currentData,
                current: currentData.current + 1,
                remaining: Math.max(0, currentData.remaining - 1),
                percentage:
                  currentData.limit > 0
                    ? Math.min(
                        100,
                        ((currentData.current + 1) / currentData.limit) * 100
                      )
                    : 0,
              },
            },
          });
        }
      },

      // Getters
      getUsageForFeature: (feature: FeatureType) => {
        return get().usageData[feature] || null;
      },

      hasReachedLimit: (feature: FeatureType) => {
        const usage = get().usageData[feature];
        return usage ? !usage.allowed : false;
      },
    }),
    {
      name: "usage-store",
      partialize: (state) => ({
        usageData: state.usageData,
        lastFetched: state.lastFetched,
      }),
    }
  )
);

// Selector hooks for better performance
export const useUsageData = () => useUsageStore((state) => state.usageData);
export const usePlanType = () => useUsageStore((state) => state.planType);
export const useUsageLoading = () => useUsageStore((state) => state.isLoading);
export const useUsageError = () => useUsageStore((state) => state.error);

// Feature-specific hooks
export const useFeatureUsage = (feature: FeatureType) => {
  return useUsageStore((state) => {
    const usageData = state.getUsageForFeature(feature);
    return {
      usage: usageData || {
        current: 0,
        limit: 0,
        percentage: 0,
        allowed: true,
        unlimited: false,
        remaining: 0,
      },
      hasReachedLimit: state.hasReachedLimit(feature),
    };
  });
};

// Actions hook
export const useUsageActions = () => {
  return useUsageStore((state) => ({
    fetchUsageData: state.fetchUsageData,
    checkFeatureUsage: state.checkFeatureUsage,
    recordUsage: state.recordUsage,
  }));
};

// Combined hook for convenience
export const useUsage = () => {
  const data = useUsageData();
  const planType = usePlanType();
  const isLoading = useUsageLoading();
  const error = useUsageError();
  const actions = useUsageActions();

  return {
    data,
    planType,
    isLoading,
    error,
    ...actions,
  };
};
