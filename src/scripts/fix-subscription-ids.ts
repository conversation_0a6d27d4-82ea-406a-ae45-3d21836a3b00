/**
 * <PERSON><PERSON><PERSON> to fix existing subscriptions that have transaction references
 * instead of subscription codes as their subscriptionId.
 * 
 * This script will:
 * 1. Find subscriptions with transaction reference format IDs
 * 2. Try to fetch the correct subscription_code from Paystack
 * 3. Update the database with the correct subscription_code
 */

import { prisma } from "../lib/prisma";
import { verifyTransaction } from "../lib/paystack";

async function fixSubscriptionIds() {
  console.log("🔧 Starting subscription ID fix process...");
  
  try {
    // Find subscriptions that might have transaction references instead of subscription codes
    // Transaction references typically don't start with "SUB_"
    const suspiciousSubscriptions = await prisma.subscription.findMany({
      where: {
        subscriptionId: {
          not: {
            startsWith: "SUB_"
          }
        },
        status: {
          in: ["ACTIVE", "NON_RENEWING"]
        }
      }
    });
    
    console.log(`📊 Found ${suspiciousSubscriptions.length} subscriptions with potential transaction reference IDs`);
    
    let fixedCount = 0;
    let errorCount = 0;
    
    for (const subscription of suspiciousSubscriptions) {
      console.log(`\n🔍 Processing subscription ${subscription.id} with ID: ${subscription.subscriptionId}`);
      
      try {
        // Try to verify the transaction to get subscription details
        const transactionDetails = await verifyTransaction(subscription.subscriptionId);
        
        if (transactionDetails?.data?.subscription?.subscription_code) {
          const subscriptionCode = transactionDetails.data.subscription.subscription_code;
          const emailToken = transactionDetails.data.subscription.email_token;
          
          console.log(`✅ Found subscription code: ${subscriptionCode}`);
          
          // Update the subscription with the correct subscription_code
          await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              subscriptionId: subscriptionCode,
              emailToken: emailToken,
              metadata: {
                ...((subscription.metadata as any) || {}),
                fixedSubscriptionId: true,
                fixedAt: new Date().toISOString(),
                originalTransactionReference: subscription.subscriptionId
              }
            }
          });
          
          console.log(`✅ Updated subscription ${subscription.id} with correct subscription code`);
          fixedCount++;
        } else {
          console.log(`⚠️ No subscription details found in transaction for ${subscription.subscriptionId}`);
        }
      } catch (error) {
        console.error(`❌ Error processing subscription ${subscription.id}:`, error instanceof Error ? error.message : String(error));
        errorCount++;
      }
    }
    
    console.log(`\n📊 Fix process completed:`);
    console.log(`✅ Fixed: ${fixedCount} subscriptions`);
    console.log(`❌ Errors: ${errorCount} subscriptions`);
    console.log(`📋 Total processed: ${suspiciousSubscriptions.length} subscriptions`);
    
  } catch (error) {
    console.error("❌ Fatal error in fix process:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if called directly
if (require.main === module) {
  fixSubscriptionIds().catch(console.error);
}

export { fixSubscriptionIds };