import { getPaymentPlan } from "@/lib/flutterwave";
import { FLUTTERWAVE_PLAN_CODES } from "@/lib/flutterwave-config";

/**
 * <PERSON><PERSON><PERSON> to verify all payment plan IDs in Flutterwave dashboard
 * This will help identify which plan IDs are correct and their actual amounts
 */
async function verifyAllPlans() {
  console.log("🔍 Verifying all payment plans in Flutterwave dashboard...\n");

  const allPlans = {
    ...FLUTTERWAVE_PLAN_CODES.nigeria,
    ...FLUTTERWAVE_PLAN_CODES.international,
  };

  for (const [planName, planId] of Object.entries(allPlans)) {
    try {
      console.log(`\n📋 Checking plan: ${planName} (ID: ${planId})`);
      const planDetails = await getPaymentPlan(planId);

      if (planDetails.status === "success" && planDetails.data) {
        console.log("✅ Plan found:", {
          id: planDetails.data.id,
          name: planDetails.data.name,
          amount: planDetails.data.amount,
          currency: planDetails.data.currency,
          interval: planDetails.data.interval,
          status: planDetails.data.status,
        });
      } else {
        console.log("❌ Plan not found or inactive");
      }
    } catch (error) {
      console.log("❌ Error fetching plan:", error.message);
    }

    // Add delay to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Run the verification
verifyAllPlans().catch(console.error);
