@import "tailwindcss";

@plugin "tailwindcss-animate";
@plugin "@tailwindcss/typography";
@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(222.2 84% 4.9%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(222.2 84% 4.9%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(222.2 84% 4.9%);
  --primary: hsl(221.2 83.2% 53.3%);
  --primary-foreground: hsl(210 40% 98%);
  --secondary: hsl(210 40% 96.1%);
  --secondary-foreground: hsl(222.2 47.4% 11.2%);
  --muted: hsl(210 40% 96.1%);
  --muted-foreground: hsl(215.4 16.3% 46.9%);
  --accent: hsl(210 40% 96.1%);
  --accent-foreground: hsl(222.2 47.4% 11.2%);
  --destructive: hsl(0 84.2% 60.2%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(214.3 31.8% 91.4%);
  --input: hsl(214.3 31.8% 91.4%);
  --ring: hsl(221.2 83.2% 53.3%);
  --radius: 0.75rem;
  --sidebar-background: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
  --sidebar: hsl(0 0% 98%);
}

.dark {
  --background: hsl(222.2 84% 4.9%);
  --foreground: hsl(210 40% 98%);
  --card: hsl(222.2 84% 4.9%);
  --card-foreground: hsl(210 40% 98%);
  --popover: hsl(222.2 84% 4.9%);
  --popover-foreground: hsl(210 40% 98%);
  --primary: hsl(217.2 91.2% 59.8%);
  --primary-foreground: hsl(222.2 47.4% 11.2%);
  --secondary: hsl(217.2 32.6% 17.5%);
  --secondary-foreground: hsl(210 40% 98%);
  --muted: hsl(217.2 32.6% 17.5%);
  --muted-foreground: hsl(215 20.2% 65.1%);
  --accent: hsl(217.2 32.6% 17.5%);
  --accent-foreground: hsl(210 40% 98%);
  --destructive: hsl(0 62.8% 30.6%);
  --destructive-foreground: hsl(210 40% 98%);
  --border: hsl(217.2 32.6% 17.5%);
  --input: hsl(217.2 32.6% 17.5%);
  --ring: hsl(224.3 76.3% 48%);
  --sidebar-background: hsl(222.2 84% 4.9%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(217.2 32.6% 17.5%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);
  --sidebar: hsl(240 5.9% 10%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
  .cf-turnstile > iframe {
    max-height: 78px !important;
    width: 100%;
    height: auto !important;
  }
}

/* markdown styles */
.prose {
  color: hsl(var(--foreground));
  max-width: none;
}

/* Ensure ReactMarkdown content is visible in both modes */
.prose p,
.prose li,
.prose blockquote {
  color: hsl(var(--foreground) / 0.9);
}

/* Headings with enhanced visibility */
.prose h1 {
  color: hsl(var(--foreground));
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.prose h2 {
  color: hsl(var(--foreground));
  font-size: 1.875rem;
  font-weight: 600;
  margin: 1.5rem 0 1rem;
  border-bottom: 2px solid hsl(var(--border));
  padding-bottom: 0.75rem;
}

.prose h3 {
  color: hsl(var(--foreground));
  font-size: 1.5rem;
  font-weight: 600;
  margin: 2rem 0 1rem;
}

/* Enhanced code blocks with better styling */
.prose code {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace;
  font-size: 0.875rem;
}

/* Inline code styling */
.prose :not(pre) > code {
  background-color: hsl(var(--muted));
  color: hsl(var(--foreground));
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  border: 1px solid hsl(var(--border));
  font-weight: 500;
}

/* Code block container styling */
.prose pre {
  padding: 0 !important;
  margin: 1rem 0 !important;
  border-radius: 0.5rem;
  overflow: visible;
}

/* Force dark background for all code blocks regardless of theme */
.prose pre > div {
  background-color: #1f2937 !important;
  border: 1px solid #374151 !important;
}

/* Ensure code blocks work properly in flashcards and other contexts */
.prose-invert pre > div {
  background-color: #1f2937 !important;
  border: 1px solid #374151 !important;
}

/* Fix code block alignment in centered containers */
.prose code,
.prose-invert code {
  text-align: left !important;
}

.prose pre,
.prose-invert pre {
  text-align: left !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Specific fixes for flashcard code blocks */
.prose-invert .relative.group {
  width: 100% !important;
  max-width: 100% !important;
}

.prose-invert .relative.group > div:last-child {
  width: 100% !important;
  text-align: left !important;
}

/* Ensure syntax highlighter content is properly formatted */
.prose-invert pre[class*="language-"],
.prose pre[class*="language-"] {
  text-align: left !important;
  white-space: pre !important;
  word-wrap: normal !important;
}

/* Ensure code blocks are responsive */
.prose pre code {
  display: block;
  overflow-x: auto;
  padding: 0;
  background: transparent;
  border: none;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Mobile responsiveness for code blocks */
@media (max-width: 768px) {
  .prose pre {
    margin-left: -1rem;
    margin-right: -1rem;
    border-radius: 0;
  }

  .prose pre code {
    font-size: 0.8rem;
    padding: 0.75rem;
  }
}

/* Links with proper visibility */
.prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 0.2em;
}

.prose a:hover {
  text-decoration: none;
}

/* Block quotes */
.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  font-style: italic;
  margin: 1.5rem 0;
}

/* Ensure proper content container backgrounds */
.prose > div {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
}

/* Adjusted dark mode styles to work with .dark class */
.dark .prose > div {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
}

.dark .prose h1,
.dark .prose h2,
.dark .prose h3,
.dark .prose h4,
.dark .prose p,
.dark .prose li,
.dark .prose blockquote {
  color: hsl(var(--foreground));
}

.dark .prose :not(pre) > code {
  background-color: hsl(var(--muted));
  color: hsl(var(--accent-foreground));
  border: 1px solid hsl(var(--border));
}

/* Dark mode code block enhancements */
.dark .prose pre {
  border: 1px solid hsl(var(--border));
}

/* Force dark background for code blocks in dark mode - higher specificity */
.dark .prose pre > div,
.dark .prose-invert pre > div {
  background-color: #1f2937 !important;
  border: 1px solid #374151 !important;
}

/* Ensure copy button is visible in dark mode */
.dark .prose .group:hover button {
  background-color: hsl(var(--background) / 0.9);
  border-color: hsl(var(--border));
}

/* Adjust note and example styles to use CSS variables */
.prose .note {
  background-color: hsl(var(--muted) / 0.5);
  border: 1px solid hsl(var(--border));
  border-left: 4px solid hsl(var(--primary));
}

.prose .example {
  background-color: hsl(var(--muted) / 0.5);
  border: 1px solid hsl(var(--border));
  border-left: 4px solid hsl(142 76% 45%);
}

.dark .prose .note,
.dark .prose .example {
  background-color: hsl(var(--muted) / 0.2);
}

@layer components {
  .prose-container {
    @apply prose prose-invert max-w-none;
  }

  .prose-container pre {
    @apply my-4 rounded-lg overflow-hidden;
    margin: 0;
    background-color: transparent !important;
  }

  /* Force dark background for code blocks in prose-container */
  .prose-container pre > div {
    background-color: #1f2937 !important;
    border: 1px solid #374151 !important;
  }

  .prose-container code {
    @apply text-sm font-mono;
  }

  .prose-container :not(pre) > code {
    @apply px-1.5 py-0.5 rounded border;
    background-color: hsl(var(--muted));
    color: hsl(var(--foreground));
    border-color: hsl(var(--border));
  }

  .prose-container .timestamp {
    @apply text-sm text-muted-foreground font-mono ml-2;
  }

  .prose-container .example {
    @apply my-4 p-4 rounded-lg bg-slate-900/50;
  }

  .prose-container .note {
    @apply my-4 p-4 rounded-lg bg-blue-500/10 border border-blue-500/20;
  }

  .prose-container h1 {
    @apply text-3xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent;
  }

  .prose-container h2 {
    @apply text-2xl font-semibold mt-8 mb-4 text-primary/90;
  }

  .prose-container h3 {
    @apply text-xl font-medium mt-6 mb-3;
  }

  .prose-container ul {
    @apply my-4 list-disc list-inside;
  }

  .prose-container li {
    @apply my-2;
  }

  .prose-container p {
    @apply my-3 leading-7;
  }

  .code-block-wrapper {
    @apply my-4;
  }

  .math-block {
    @apply my-4 p-4 bg-slate-800 rounded-lg overflow-x-auto;
  }
}

/* Flashcard styles */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Flashcard content styles */
.prose ul,
.prose ol {
  text-align: left;
  margin-left: 0;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  text-align: left;
}

.prose li > p {
  margin: 0;
  display: inline;
}

/* Ensure flashcard content is properly aligned */
.prose > div {
  width: 100%;
  max-width: 100%;
}

/* scrollbar styles */
::-webkit-scrollbar {
  width: 6px; /* Make scrollbar thinner */
}

::-webkit-scrollbar-track {
  background: transparent; /* Make track invisible */
}

::-webkit-scrollbar-thumb {
  background: #666; /* Dark grey thumb */
  border-radius: 10px; /* Rounded corners */
}
iframe[data-turnstile] {
  margin: 0 auto;
  display: block;
  width: 100%;
  height: 10px;
}

/* For Firefox */
* {
  scrollbar-width: thin; /* "auto" or "thin" */
  scrollbar-color: #666 transparent; /* thumb and track color */
}
