"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader2 } from "lucide-react";
import { useSubscription } from "@/store/subscriptionStore";
import { Button } from "@/components/ui/button";

export default function SubscriptionSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { subscription, refetch } = useSubscription();
  const [isActivating, setIsActivating] = useState(true);
  const [activationError, setActivationError] = useState<string | null>(null);

  // Direct activation instead of polling
  useEffect(() => {
    const activateSubscription = async () => {
      try {
        // Get transaction details from URL params or localStorage
        const transactionRef =
          searchParams.get("tx_ref") ||
          localStorage.getItem("lastTransactionRef");
        const flutterwaveRef =
          searchParams.get("flw_ref") ||
          localStorage.getItem("lastFlutterwaveRef");

        console.log("🔄 Attempting direct activation:", {
          transactionRef,
          flutterwaveRef,
        });

        if (!transactionRef && !flutterwaveRef) {
          console.log(
            "❌ No transaction reference found, falling back to polling"
          );
          // Fall back to polling if no transaction reference
          await refetch();
          setIsActivating(false);
          return;
        }

        // Call the activation endpoint directly
        const response = await fetch("/api/subscriptions/activate", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            transactionRef,
            flutterwaveRef,
          }),
        });

        const result = await response.json();

        if (response.ok && result.success) {
          console.log("✅ Subscription activated successfully");
          // Refresh subscription data
          await refetch();
          setIsActivating(false);

          // Redirect to dashboard after 2 seconds
          setTimeout(() => {
            router.push("/dashboard");
          }, 2000);
        } else {
          console.error("❌ Activation failed:", result);
          setActivationError(result.error || "Failed to activate subscription");
          setIsActivating(false);
        }
      } catch (error) {
        console.error("❌ Activation error:", error);
        setActivationError(
          "An error occurred while activating your subscription"
        );
        setIsActivating(false);
      }
    };

    // Only try activation if subscription is not already active
    if (
      subscription?.status !== "ACTIVE" &&
      subscription?.status !== "NON_RENEWING"
    ) {
      activateSubscription();
    } else {
      setIsActivating(false);
      // Redirect to dashboard if already active
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    }
  }, [subscription?.status, router, refetch, searchParams]);

  const isActive =
    subscription?.status === "ACTIVE" ||
    subscription?.status === "NON_RENEWING";

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-md">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {isActive ? (
                <CheckCircle className="h-12 w-12 text-green-500" />
              ) : activationError ? (
                <CheckCircle className="h-12 w-12 text-red-500" />
              ) : (
                <Loader2 className="h-12 w-12 animate-spin" />
              )}
            </div>
            <CardTitle className="text-2xl">
              {isActive
                ? "Subscription Activated!"
                : activationError
                ? "Activation Issue"
                : "Activating Subscription..."}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              {isActive
                ? "Your subscription has been activated successfully. Redirecting you to the dashboard..."
                : activationError
                ? activationError
                : "Please wait while we activate your subscription."}
            </p>

            {(!isActivating && !isActive) || activationError ? (
              <>
                <p className="text-sm text-muted-foreground">
                  {activationError
                    ? "Don't worry, your payment was processed. You can continue to the dashboard and your subscription will be activated shortly."
                    : "Taking longer than expected? You can continue to the dashboard and check your subscription status there."}
                </p>
                <Button
                  onClick={() => router.push("/dashboard")}
                  className="w-full"
                >
                  Go to Dashboard
                </Button>
              </>
            ) : null}

            {isActive && subscription && (
              <div className="bg-muted p-4 rounded-lg text-left">
                <p className="font-medium">Plan Details:</p>
                <p className="text-sm text-muted-foreground">
                  {subscription.planType} - {subscription.billingCycle}
                </p>
                {subscription.currentPeriodEnd && (
                  <p className="text-sm text-muted-foreground">
                    Next billing:{" "}
                    {new Date(
                      subscription.currentPeriodEnd
                    ).toLocaleDateString()}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
