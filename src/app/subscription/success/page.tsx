"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Loader2 } from "lucide-react";
import { useSubscription } from "@/store/subscriptionStore";
import { Button } from "@/components/ui/button";

export default function SubscriptionSuccessPage() {
  const router = useRouter();
  const { subscription, refetch } = useSubscription();
  const [isPolling, setIsPolling] = useState(true);

  // Poll for subscription status
  useEffect(() => {
    let interval: NodeJS.Timeout;
    let timeoutId: NodeJS.Timeout;

    const checkStatus = async () => {
      if (subscription?.status === "ACTIVE" || subscription?.status === "NON_RENEWING") {
        setIsPolling(false);
        // Redirect to dashboard after 2 seconds when active
        timeoutId = setTimeout(() => {
          router.push("/dashboard");
        }, 2000);
        return;
      }

      // Poll every 3 seconds
      interval = setInterval(async () => {
        await refetch();
      }, 3000);

      // Stop polling after 30 seconds
      timeoutId = setTimeout(() => {
        setIsPolling(false);
        if (interval) clearInterval(interval);
      }, 30000);
    };

    checkStatus();

    return () => {
      if (interval) clearInterval(interval);
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [subscription?.status, router, refetch]);

  const isActive = subscription?.status === "ACTIVE" || subscription?.status === "NON_RENEWING";

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-md">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4">
              {isActive ? (
                <CheckCircle className="h-12 w-12 text-green-500" />
              ) : (
                <Loader2 className="h-12 w-12 animate-spin" />
              )}
            </div>
            <CardTitle className="text-2xl">
              {isActive ? "Subscription Activated!" : "Processing Payment..."}
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-muted-foreground">
              {isActive
                ? "Your subscription has been activated successfully. Redirecting you to the dashboard..."
                : "Please wait while we process your payment and activate your subscription."}
            </p>

            {!isPolling && !isActive && (
              <>
                <p className="text-sm text-muted-foreground">
                  Taking longer than expected? You can continue to the dashboard
                  and check your subscription status there.
                </p>
                <Button
                  onClick={() => router.push("/dashboard")}
                  className="w-full"
                >
                  Go to Dashboard
                </Button>
              </>
            )}

            {isActive && subscription && (
              <div className="bg-muted p-4 rounded-lg text-left">
                <p className="font-medium">Plan Details:</p>
                <p className="text-sm text-muted-foreground">
                  {subscription.planType} - {subscription.billingCycle}
                </p>
                {subscription.currentPeriodEnd && (
                  <p className="text-sm text-muted-foreground">
                    Next billing:{" "}
                    {new Date(
                      subscription.currentPeriodEnd
                    ).toLocaleDateString()}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
