import { db, contentSummary } from "@/lib/db";
import { eq } from "drizzle-orm";
import { notFound } from "next/navigation";
import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from "@/components/ui/resizable";
import ShareMainComponent from "@/components/share/MainContent";
import ShareSidebar from "@/components/share/Sidebar";

export default async function SharedSummaryPage({
  params,
}: {
  params: Promise<{ shareToken: string }>;
}) {
  const { shareToken } = await params;

  // Find a content summary by shareToken.
  const summaryResult = await db
    .select({
      contentId: contentSummary.contentId,
      contentType: contentSummary.contentType,
      title: contentSummary.title,
      content: contentSummary.content,
      originalContent: contentSummary.originalContent,
      sourceId: contentSummary.sourceId,
      generatedAt: contentSummary.generatedAt,
      isShared: contentSummary.isShared,
      shareToken: contentSummary.shareToken,
      outputType: contentSummary.outputType,
    })
    .from(contentSummary)
    .where(eq(contentSummary.shareToken, shareToken))
    .limit(1);

  const summary = summaryResult[0];

  // If not found or sharing is disabled, show 404.
  if (!summary || !summary.isShared) {
    notFound();
  }

  // Convert generatedAt (a Date) to a string.
  const summaryWithIso = {
    ...summary,
    generatedAt: summary.generatedAt.toISOString(),
    shareToken: summary.shareToken || "",
  };

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        <ResizablePanel defaultSize={60} minSize={45}>
          <ShareMainComponent summary={summaryWithIso} />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel
          defaultSize={40}
          minSize={30}
          className="hidden md:block"
        >
          <ShareSidebar summary={summaryWithIso} />
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
