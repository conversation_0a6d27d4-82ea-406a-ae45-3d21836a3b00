import { NextResponse } from "next/server";
import prisma from "@/utils/prisma";
import { createClient } from "@/utils/supabase/server";
import { NextRequest } from "next/server";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

/**
 * Efficient content lookup API that can find content by various identifiers in a single query
 * This reduces the need for multiple database calls when checking if content exists
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { 
      contentId, 
      sourceId, 
      contentType,
      url,
      includeOriginalContent = false 
    } = body;

    if (!contentId && !sourceId && !url) {
      return NextResponse.json(
        { error: "At least one identifier (contentId, sourceId, or url) is required" },
        { status: 400 }
      );
    }

    // Build the where clause based on provided identifiers
    const whereClause: any = {
      userId: user.id,
    };

    if (contentId) {
      whereClause.contentId = contentId;
    }

    if (sourceId) {
      whereClause.sourceId = sourceId;
    }

    if (url && contentType === "WEBPAGE") {
      whereClause.sourceId = url;
      whereClause.contentType = "WEBPAGE";
    }

    if (contentType) {
      whereClause.contentType = contentType.toUpperCase();
    }

    // For YouTube videos, we might need to check if the video ID exists as sourceId
    if (contentType === "youtube" && contentId && contentId.length === 11) {
      // If it's a YouTube video ID, also check sourceId
      delete whereClause.contentId;
      whereClause.OR = [
        { contentId },
        { sourceId: contentId, contentType: "YOUTUBE" }
      ];
    }

    // Perform a single efficient database query
    const content = await prisma.contentSummary.findFirst({
      where: whereClause,
      select: {
        id: true,
        contentId: true,
        contentType: true,
        title: true,
        content: true,
        sourceId: true,
        generatedAt: true,
        outputType: true,
        // Only include original content if explicitly requested
        ...(includeOriginalContent && { originalContent: true }),
      },
    });

    if (!content) {
      return NextResponse.json({ found: false });
    }

    // Parse original content if needed
    let parsedOriginalContent = null;
    if (includeOriginalContent && content.originalContent) {
      try {
        parsedOriginalContent = typeof content.originalContent === 'string' 
          ? JSON.parse(content.originalContent)
          : content.originalContent;
      } catch (parseError) {
        console.warn("Failed to parse original content as JSON:", parseError);
        // If it's not valid JSON, return it as-is
        parsedOriginalContent = content.originalContent;
      }
    }

    return NextResponse.json({
      found: true,
      content: {
        ...content,
        ...(parsedOriginalContent && { parsedOriginalContent }),
      },
    });
  } catch (error) {
    console.error("Content lookup API error:", error);
    return NextResponse.json(
      { error: "Failed to lookup content" },
      { status: 500 }
    );
  }
}
