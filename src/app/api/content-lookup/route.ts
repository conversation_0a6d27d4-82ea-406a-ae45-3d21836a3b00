import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { NextRequest } from "next/server";
import { eq, and, or } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

/**
 * Efficient content lookup API that can find content by various identifiers in a single query
 * This reduces the need for multiple database calls when checking if content exists
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      contentId,
      sourceId,
      contentType,
      url,
      includeOriginalContent = false,
    } = body;

    if (!contentId && !sourceId && !url) {
      return NextResponse.json(
        {
          error:
            "At least one identifier (contentId, sourceId, or url) is required",
        },
        { status: 400 }
      );
    }

    // Build the where clause based on provided identifiers
    let whereConditions = [eq(contentSummary.userId, user.id)];

    if (contentType === "youtube" && contentId && contentId.length === 11) {
      // For YouTube videos, check both contentId and sourceId
      whereConditions.push(
        or(
          eq(contentSummary.contentId, contentId),
          and(
            eq(contentSummary.sourceId, contentId),
            eq(contentSummary.contentType, "YOUTUBE")
          )
        )!
      );
    } else {
      // Build conditions for other cases
      if (contentId) {
        whereConditions.push(eq(contentSummary.contentId, contentId));
      }

      if (sourceId) {
        whereConditions.push(eq(contentSummary.sourceId, sourceId));
      }

      if (url && contentType === "WEBPAGE") {
        whereConditions.push(eq(contentSummary.sourceId, url));
        whereConditions.push(eq(contentSummary.contentType, "WEBPAGE"));
      }

      if (contentType && contentType !== "youtube") {
        whereConditions.push(
          eq(contentSummary.contentType, contentType.toUpperCase() as any)
        );
      }
    }

    // Perform a single efficient database query
    const selectFields = {
      id: contentSummary.id,
      contentId: contentSummary.contentId,
      contentType: contentSummary.contentType,
      title: contentSummary.title,
      content: contentSummary.content,
      sourceId: contentSummary.sourceId,
      generatedAt: contentSummary.generatedAt,
      outputType: contentSummary.outputType,
      ...(includeOriginalContent && {
        originalContent: contentSummary.originalContent,
      }),
    };

    const contentResult = await db
      .select(selectFields)
      .from(contentSummary)
      .where(and(...whereConditions))
      .limit(1);

    const content = contentResult[0];

    if (!content) {
      return NextResponse.json({ found: false });
    }

    // Parse original content if needed
    let parsedOriginalContent = null;
    if (includeOriginalContent && content.originalContent) {
      try {
        parsedOriginalContent =
          typeof content.originalContent === "string"
            ? JSON.parse(content.originalContent)
            : content.originalContent;
      } catch (parseError) {
        console.warn("Failed to parse original content as JSON:", parseError);
        // If it's not valid JSON, return it as-is
        parsedOriginalContent = content.originalContent;
      }
    }

    return NextResponse.json({
      found: true,
      content: {
        ...content,
        ...(parsedOriginalContent && { parsedOriginalContent }),
      },
    });
  } catch (error) {
    console.error("Content lookup API error:", error);
    return NextResponse.json(
      { error: "Failed to lookup content" },
      { status: 500 }
    );
  }
}
