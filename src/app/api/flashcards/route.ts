import { NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { db, contentSummary, flashcard } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { parseFlashcards } from "@/utils/jsonParser";
import { checkUsageLimit, recordUsage } from "@/lib/usage-utils";
import { FeatureType } from "@/types/subscription";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";
export const maxDuration = 60; // Maximum duration in seconds

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

const FLASHCARD_PROMPT = `You are an AI flashcard generator. Create educational flashcards based on the provided content.
Follow these rules:
1. Generate high-quality flashcards that capture key concepts (between 5-30 cards depending on content length)
2. Use markdown formatting for:
  - Code snippets (with triple backticks and language specifier)
  - Mathematical formulas (using LaTeX syntax with $ for inline and $$ for block)
  - Emphasis, bold, or other markdown elements as appropriate
3. Flashcard front should be a concise, thought-provoking question or key concept
4. IMPORTANT: Flashcard back MUST be extremely concise - no more than 3-4 bullet points maximum
5. Format the response as a JSON array of objects with the following structure:
  {
    "front": "Question or concept with optional markdown",
    "back": "Concise answer with key points, using bullet points or numbered lists when appropriate"
  }
6. Vary the difficulty and type of flashcards:
  - Definitional
  - Conceptual
  - Problem-solving
  - Code/technical (if applicable)
  - Mathematical (if applicable)
7. For shorter content (under 1000 words), create 5-10 cards
8. For medium content (1000-5000 words), create 10-20 cards
9. For longer content (over 5000 words), create 20-30 cards
10. Focus on the most important concepts - quality over quantity
11. DO NOT include any timestamps or time references
12. Keep answers EXTREMELY direct and to the point - skip unnecessary fluff
13. Use bullet points for answers - MAXIMUM 3-4 bullet points per card
14. Bold key terms in the answers for emphasis
15. Instead of creating long answers, create more cards with shorter answers
16. Each bullet point should be 1-2 lines maximum
17. Focus on practical, actionable information
18. CRITICAL: If a concept requires more explanation, split it into multiple cards rather than creating one card with a long answer

`;

export async function POST(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check usage limit
    const usageCheck = await checkUsageLimit(user.id, FeatureType.FLASHCARDS);
    if (!usageCheck.allowed) {
      return NextResponse.json(
        {
          error: usageCheck.message,
          usageData: usageCheck,
        },
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      sourceContent,
      metadata,
      contentId,
      contentType = "youtube",
    } = body;

    if (!sourceContent || !metadata || !contentId) {
      return NextResponse.json(
        { error: "Missing required data" },
        { status: 400 }
      );
    }

    try {
      // Get content summary
      const contentSummaryResult = await db
        .select()
        .from(contentSummary)
        .where(eq(contentSummary.contentId, contentId))
        .limit(1);

      const contentSummaryData = contentSummaryResult[0];

      if (!contentSummaryData) {
        return NextResponse.json(
          { error: "Content summary not found" },
          { status: 404 }
        );
      }

      // Determine content length to guide number of flashcards
      const contentLength =
        typeof sourceContent === "string"
          ? sourceContent.length
          : Array.isArray(sourceContent)
          ? sourceContent.reduce(
              (acc, item) => acc + (item.text?.length || 0),
              0
            )
          : 0;

      // Determine target number of flashcards based on content length
      let targetCardCount = "5-10";
      if (contentLength > 5000) {
        targetCardCount = "20-30";
      } else if (contentLength > 1000) {
        targetCardCount = "10-20";
      }

      // Generate flashcards using AI
      const model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
      });

      // Customize prompt based on content type
      let contentDescription = "";
      if (contentType === "youtube") {
        contentDescription = `Video Title: ${metadata.title}
Channel: ${metadata.channelTitle || "Unknown"}`;
      } else if (contentType === "text") {
        contentDescription = `Text Title: ${metadata.title || "Untitled Text"}`;
      } else if (contentType === "pdf") {
        contentDescription = `PDF Title: ${metadata.title || "Untitled PDF"}`;
      } else if (contentType === "webpage") {
        contentDescription = `Webpage: ${
          metadata.title || metadata.sourceId || "Untitled Webpage"
        }`;
      }

      const prompt = `${FLASHCARD_PROMPT}

${contentDescription}

Content Length: ${contentLength} characters
Target Number of Flashcards: ${targetCardCount}

Content:
${sourceContent}

Summary:
${contentSummaryData.content}`;

      const result = await model.generateContent(prompt);
      const response = await result.response;
      const content = response.text();

      console.log(`[Flashcards API] AI response length: ${content.length}`);
      console.log(
        `[Flashcards API] AI response preview: ${content.substring(0, 300)}...`
      );

      // Use robust JSON parser
      const parseResult = parseFlashcards(content, true);

      if (!parseResult.success) {
        console.error(
          "[Flashcards API] Failed to parse flashcards:",
          parseResult.error,
          "Method attempted:",
          parseResult.method
        );
        throw new Error(
          `Failed to parse flashcards from AI response: ${parseResult.error}`
        );
      }

      const flashcards = parseResult.data;
      if (!flashcards || flashcards.length === 0) {
        throw new Error("No valid flashcards were generated");
      }

      console.log(
        `[Flashcards API] Successfully parsed ${flashcards.length} flashcards using method: ${parseResult.method}`
      );

      // Store flashcards in database
      const flashcardResult = await db
        .insert(flashcard)
        .values({
          id: nanoid(),
          contentSummaryId: contentSummaryData.id,
          userId: user.id,
          cards: JSON.stringify(flashcards),
          studyProgress: JSON.stringify({
            studied: [],
            lastStudied: null,
          }),
        })
        .returning({ id: flashcard.id });

      const flashcardSet = flashcardResult[0];

      // Record usage after successful flashcard generation
      await recordUsage(user.id, FeatureType.FLASHCARDS);

      return NextResponse.json({ flashcards, flashcardId: flashcardSet.id });
    } catch (aiError) {
      console.error("AI flashcard generation error:", aiError);
      return NextResponse.json(
        {
          error:
            "Failed to generate flashcards. The service may be temporarily unavailable.",
        },
        { status: 503 }
      );
    }
  } catch (error) {
    console.error("Error in flashcard processing:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { flashcardId, studiedCards } = body;

    if (!flashcardId || !Array.isArray(studiedCards)) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const updatedFlashcardResult = await db
      .update(flashcard)
      .set({
        studyProgress: JSON.stringify({
          studied: studiedCards,
          lastStudied: new Date(),
        }),
      })
      .where(eq(flashcard.id, flashcardId))
      .returning();

    const updatedFlashcard = updatedFlashcardResult[0];

    return NextResponse.json({ flashcard: updatedFlashcard });
  } catch (error) {
    console.error("Error updating study progress:", error);
    return NextResponse.json(
      { error: "Failed to update study progress" },
      { status: 500 }
    );
  }
}

export async function GET(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const contentId = searchParams.get("contentId");

    if (!contentId) {
      return NextResponse.json(
        { error: "Content ID is required" },
        { status: 400 }
      );
    }

    // Get content summary
    const contentSummaryResult = await db
      .select()
      .from(contentSummary)
      .where(eq(contentSummary.contentId, contentId))
      .limit(1);

    const contentSummaryData = contentSummaryResult[0];

    if (!contentSummaryData) {
      return NextResponse.json(
        { error: "Content summary not found" },
        { status: 404 }
      );
    }

    // Get flashcards for this content summary
    const flashcards = await db
      .select()
      .from(flashcard)
      .where(eq(flashcard.contentSummaryId, contentSummaryData.id));

    return NextResponse.json({ flashcards });
  } catch (error) {
    console.error("Error retrieving flashcards:", error);
    return NextResponse.json(
      { error: "Failed to retrieve flashcards" },
      { status: 500 }
    );
  }
}
