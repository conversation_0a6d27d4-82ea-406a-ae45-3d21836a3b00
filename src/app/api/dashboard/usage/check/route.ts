import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { checkUsageLimit } from "@/lib/usage-utils";
import { FeatureType } from "@/types/subscription";
import { getUserSubscription } from "@/lib/subscription-config";

export async function POST(req: NextRequest) {
  try {
    // Authentication
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const { feature } = body;

    // Validate feature type
    if (!feature || !Object.values(FeatureType).includes(feature)) {
      return NextResponse.json(
        { error: "Invalid or missing feature type" },
        { status: 400 }
      );
    }

    // Get user's subscription to determine plan type
    const subscription = await getUserSubscription(user.id);
    const planType = subscription?.planType || "Free";

    // Check usage limit
    const result = await checkUsageLimit(user.id, feature, planType);

    return NextResponse.json(result);
  } catch (error) {
    console.error("Error checking usage limit:", error);
    return NextResponse.json(
      { error: "Failed to check usage limit" },
      { status: 500 }
    );
  }
}
