import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getUserUsageData } from "@/lib/usage-utils";
import { getUserSubscription } from "@/lib/subscription-config";

export async function GET(req: NextRequest) {
  try {
    // Authentication
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscription to determine plan type
    const subscription = await getUserSubscription(user.id);
    const planType = subscription?.planType || "Free";

    // Get usage data for all features
    const usageData = await getUserUsageData(user.id, planType);

    // Convert array to object for easier frontend consumption
    const usage = usageData.reduce((acc, item) => {
      acc[item.feature] = {
        allowed: item.allowed,
        current: item.current,
        limit: item.limit,
        remaining: item.remaining,
        percentage: item.percentage,
        unlimited: item.unlimited,
        message: item.message,
      };
      return acc;
    }, {} as Record<string, any>);

    return NextResponse.json({
      success: true,
      usage,
      planType,
    });
  } catch (error) {
    console.error("Error fetching usage data:", error);
    return NextResponse.json(
      { error: "Failed to fetch usage data" },
      { status: 500 }
    );
  }
}
