import { NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai";
import { db, contentSummary, chatMessage } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { CHAT_SYSTEM_PROMPT } from "@/utils/prompts/chat";
import { cacheData, getCachedData } from "@/utils/server-cache";
import { checkUsageLimit, recordUsage } from "@/lib/usage-utils";
import { FeatureType } from "@/types/subscription";
import { getUserSubscription } from "@/lib/subscription-config";
import { eq, and } from "drizzle-orm";
import { nanoid } from "nanoid";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";
export const maxDuration = 60; // Maximum duration in seconds

// Initialize AI model with singleton pattern to avoid multiple initializations
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

// Cache key generator for chat responses
const getChatCacheKey = (
  contentId: string,
  message: string,
  conversationLength: number
) => {
  return `chat:${contentId}:${conversationLength}:${message
    .slice(0, 50)
    .replace(/\s+/g, "_")}`;
};

export async function POST(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscription to determine plan type
    const subscription = await getUserSubscription(user.id);
    const planType = subscription?.planType || "Free";

    // Check usage limit with the correct planType
    const usageCheck = await checkUsageLimit(
      user.id,
      FeatureType.AI_CHAT,
      planType
    );
    if (!usageCheck.allowed) {
      return NextResponse.json(
        {
          error: usageCheck.message,
          usageData: usageCheck,
        },
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      message,
      sourceContent,
      metadata,
      contentType = "youtube",
      previousMessages = [],
    } = body;

    if (!sourceContent || !metadata) {
      return NextResponse.json(
        { error: "Missing source content or metadata" },
        { status: 400 }
      );
    }

    // Get content summary with error handling
    const contentSummaryResult = await db
      .select()
      .from(contentSummary)
      .where(eq(contentSummary.contentId, metadata.contentId))
      .limit(1);

    const contentSummaryData = contentSummaryResult[0];

    if (!contentSummaryData) {
      return NextResponse.json(
        { error: "Content summary not found" },
        { status: 404 }
      );
    }

    // Create chat message with proper error handling
    await db.insert(chatMessage).values({
      id: nanoid(),
      contentSummaryId: contentSummaryData.id,
      userId: user.id,
      role: "user",
      content: message,
    });

    // Start performance measurement
    const startTime = performance.now();

    // Check cache first
    const cacheKey = getChatCacheKey(
      metadata.contentId,
      message,
      previousMessages.length
    );

    const cachedResponse = getCachedData(cacheKey);
    if (cachedResponse && typeof cachedResponse === "string") {
      console.log("Using cached chat response");

      // Still store the message in the database for history
      await db.insert(chatMessage).values({
        id: nanoid(),
        contentSummaryId: contentSummaryData.id,
        userId: user.id,
        role: "assistant",
        content: cachedResponse,
      });

      // Log performance for cached response
      const endTime = performance.now();
      console.log(
        `Chat response time (cached): ${(endTime - startTime).toFixed(2)}ms`
      );

      return NextResponse.json({ content: cachedResponse });
    }

    // Generate AI response with optimized settings
    const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

    // Prepare conversation history for context
    const conversationHistory = previousMessages
      .slice(-6) // Only use the last 6 messages for context
      .map((msg: any) => ({
        role: msg.role,
        parts: [{ text: msg.content }],
      }));

    // Customize content description based on content type
    let contentDescription = "";
    if (contentType === "youtube") {
      contentDescription = `Video: "${metadata.title}"`;
      if (metadata.channelTitle) {
        contentDescription += ` by ${metadata.channelTitle}`;
      }
    } else if (contentType === "text") {
      contentDescription = `Text: "${metadata.title || "Untitled Text"}"`;
    } else if (contentType === "pdf") {
      contentDescription = `PDF: "${metadata.title || "Untitled PDF"}"`;
    } else if (contentType === "webpage") {
      contentDescription = `Webpage: "${
        metadata.title || metadata.sourceId || "Untitled Webpage"
      }"`;
    }

    try {
      // Prepare the content for the AI
      // For shorter content, include the full source
      // For longer content, use the summary and relevant excerpts
      let contentForAI = "";
      const MAX_CONTENT_LENGTH = 25000;

      const contentText = Array.isArray(sourceContent)
        ? sourceContent.map((p) => p.text).join("\n")
        : sourceContent;

      if (contentText.length <= MAX_CONTENT_LENGTH) {
        contentForAI = contentText;
      } else {
        // For longer content ...
        // For longer content, use the summary and search for relevant parts
        contentForAI = `Summary:
${contentSummaryData.content}

Relevant excerpts:
`;

        // Enhanced keyword search to find relevant parts of the content
        // Extract important keywords from the message
        const keywords = message
          .toLowerCase()
          .split(/\s+/)
          .filter((word: string) => word.length > 3)
          // Remove common stop words
          .filter(
            (word: string) =>
              ![
                "what",
                "when",
                "where",
                "which",
                "who",
                "whom",
                "whose",
                "why",
                "how",
                "this",
                "that",
                "these",
                "those",
                "there",
                "their",
                "about",
                "with",
              ].includes(word)
          );

        if (keywords.length > 0) {
          // Split content into paragraphs for better context
          const contentParagraphs = Array.isArray(sourceContent)
            ? sourceContent.map((s) => s.text)
            : sourceContent.split(/\n\s*\n/); // Split by empty lines to get paragraphs

          // Score each paragraph based on keyword matches
          const scoredParagraphs = contentParagraphs.map(
            (paragraph: string) => {
              const paragraphLower = paragraph.toLowerCase();
              // Count matches for each keyword
              const score = keywords.reduce(
                (total: number, keyword: string) => {
                  // Count occurrences of the keyword
                  const matches = (
                    paragraphLower.match(new RegExp(keyword, "g")) || []
                  ).length;
                  return total + matches;
                },
                0
              );

              return { paragraph, score };
            }
          );

          // Define the type for scored paragraphs
          interface ScoredParagraph {
            paragraph: string;
            score: number;
          }

          // Sort by score (highest first) and take top results
          const relevantParagraphs = scoredParagraphs
            .filter((item: ScoredParagraph) => item.score > 0) // Only include paragraphs with matches
            .sort((a: ScoredParagraph, b: ScoredParagraph) => b.score - a.score) // Sort by score descending
            .slice(0, 10) // Take top 10 most relevant paragraphs
            .map((item: ScoredParagraph) => item.paragraph);

          contentForAI += relevantParagraphs.join("\n\n");
        } else {
          // If no keywords found, use the beginning and end of the content
          const contentText = Array.isArray(sourceContent)
            ? sourceContent.map((s) => s.text).join("\n")
            : sourceContent;

          contentForAI +=
            contentText.slice(0, 5000) + "\n...\n" + contentText.slice(-5000);
        }
      }

      // Generate the response
      const result = await model.generateContent({
        contents: [
          {
            role: "user",
            parts: [{ text: CHAT_SYSTEM_PROMPT.content }],
          },
          // Add conversation history for context
          ...conversationHistory,
          {
            role: "user",
            parts: [
              {
                text: `Content Type: ${contentType}
Content Title: ${metadata.title || "Untitled Content"}
${
  contentType === "youtube" && metadata.channelTitle
    ? `Channel: ${metadata.channelTitle}`
    : ""
}

${contentDescription}

${contentForAI}

User Question: ${message}

Respond directly to the user's question using information from the provided content. Be concise and focused.`,
              },
            ],
          },
        ],
        generationConfig: {
          temperature: 0.3, // Lower temperature for more focused responses
          maxOutputTokens: 1024,
          topP: 0.8,
          topK: 40,
        },
      });

      const content = result.response.text();

      // Store AI response
      await db.insert(chatMessage).values({
        id: nanoid(),
        contentSummaryId: contentSummaryData.id,
        userId: user.id,
        role: "assistant",
        content: content,
      });

      // Cache the response for future similar questions
      cacheData(cacheKey, content);

      // Record usage after successful chat response
      await recordUsage(user.id, FeatureType.AI_CHAT);

      // Log performance for generated response
      const endTime = performance.now();
      console.log(
        `Chat response time (generated): ${(endTime - startTime).toFixed(2)}ms`
      );

      return NextResponse.json({ content });
    } catch (aiError) {
      console.error("AI generation error:", aiError);
      throw new Error("Failed to generate AI response");
    }
  } catch (error) {
    console.error("Chat API error:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}

export async function GET(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const contentId = searchParams.get("contentId");

    if (!contentId) {
      return NextResponse.json(
        { error: "Content ID is required" },
        { status: 400 }
      );
    }

    const messages = await db
      .select({
        id: chatMessage.id,
        role: chatMessage.role,
        content: chatMessage.content,
        createdAt: chatMessage.createdAt,
      })
      .from(chatMessage)
      .innerJoin(
        contentSummary,
        eq(chatMessage.contentSummaryId, contentSummary.id)
      )
      .where(
        and(
          eq(contentSummary.userId, user.id),
          eq(contentSummary.contentId, contentId)
        )
      )
      .orderBy(chatMessage.createdAt);

    return NextResponse.json({ messages });
  } catch (error) {
    console.error("Error retrieving chat history:", error);
    return NextResponse.json(
      { error: "Failed to retrieve chat history" },
      { status: 500 }
    );
  }
}
