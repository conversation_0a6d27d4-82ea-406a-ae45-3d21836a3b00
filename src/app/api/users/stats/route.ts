import { NextResponse } from "next/server";
import prisma from "@/utils/prisma";
import { createClient } from "@/utils/supabase/server";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const totalContent = await prisma.contentSummary.count({
      where: { userId: user.id },
    });

    return NextResponse.json({ totalContent });
  } catch (error) {
    console.error("Error fetching user stats (totalContent):", error);
    return NextResponse.json(
      { error: "Failed to fetch user stats" },
      { status: 500 }
    );
  }
}
