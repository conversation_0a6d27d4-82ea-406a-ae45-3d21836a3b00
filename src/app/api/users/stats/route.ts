import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { eq, count } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const totalContentResult = await db
      .select({ count: count() })
      .from(contentSummary)
      .where(eq(contentSummary.userId, user.id));

    const totalContent = totalContentResult[0]?.count || 0;

    return NextResponse.json({ totalContent });
  } catch (error) {
    console.error("Error fetching user stats (totalContent):", error);
    return NextResponse.json(
      { error: "Failed to fetch user stats" },
      { status: 500 }
    );
  }
}
