import { NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai";
import prisma from "@/utils/prisma";
import { createClient } from "@/utils/supabase/server";
import { RateLimiter } from "limiter";
import {
  cacheData,
  getCachedData,
  getTransformCacheKey,
  CachePriority,
} from "@/utils/server-cache";
import { startTiming, endTiming } from "@/utils/performance";
import { estimateTokens } from "@/utils/chunking";
import {
  formatContent,
  truncateContent,
  extractTitle,
} from "@/utils/contentProcessing";
import {
  generateWithRateLimit,
  processContentWithChunking,
} from "@/utils/aiService";
import { ContentMetadata, ContentSegment } from "@/types/contentTypes";
import { ContentType } from "@prisma/client";
import { parseJsonSafely } from "@/utils/jsonParser";
import { checkUsageLimit, recordUsage } from "@/lib/usage-utils";
import { FeatureType } from "@/types/subscription";
import { getUserSubscription } from "@/lib/subscription-config"; // Added

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 60;

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

const limiter = new RateLimiter({
  tokensPerInterval: 15,
  interval: "minute",
  fireImmediately: true,
});

// Add OPTIONS method to handle preflight requests
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-Requested-With",
      "Access-Control-Max-Age": "86400", // 24 hours cache for preflight requests
    },
  });
}

/**
 * Parse and validate the request body
 *
 * @param req The request object
 * @returns The parsed request body or null if parsing fails
 */
async function parseRequestBody(req: Request) {
  try {
    // Clone the request to avoid consuming the body stream
    const clonedReq = req.clone();

    // Try to parse as JSON using standard method first
    try {
      return await clonedReq.json();
    } catch (jsonError) {
      console.error("Standard JSON parsing error:", jsonError);

      // If standard JSON parsing fails, try robust parsing
      try {
        const textContent = await req.text();
        console.log(
          `[Transform API] Attempting robust JSON parsing on ${textContent.length} characters`
        );

        const parseResult = parseJsonSafely(textContent, true);
        if (parseResult.success) {
          console.log(
            `[Transform API] Robust JSON parsing successful using method: ${parseResult.method}`
          );
          return parseResult.data;
        } else {
          console.error(
            "[Transform API] Robust JSON parsing also failed:",
            parseResult.error
          );
          console.error(
            "Failed to parse request body. Raw content:",
            textContent.length > 500
              ? textContent.substring(0, 500) + "..."
              : textContent
          );
        }
      } catch (textError) {
        console.error("Failed to read request body as text:", textError);
      }

      return null;
    }
  } catch (parseError) {
    console.error("Error processing request body:", parseError);
    return null;
  }
}

/**
 * Store the transformation result in the database
 *
 * @param userId The user ID
 * @param metadata The content metadata
 * @param aiResponse The AI-generated response
 * @param sourceContent The original source content
 * @param outputType The output type
 * @param title The content title
 */
async function storeInDatabase(
  userId: string,
  metadata: ContentMetadata,
  aiResponse: string,
  sourceContent: string | ContentSegment[] | Record<string, unknown>,
  outputType: string,
  title: string
): Promise<void> {
  try {
    // Prepare the original content with proper error handling
    let originalContentToStore;
    try {
      originalContentToStore =
        typeof sourceContent === "string"
          ? sourceContent
          : JSON.stringify(sourceContent);
    } catch (jsonError) {
      console.error(
        "Error converting source content to JSON for database:",
        jsonError
      );
      // Fallback to a simpler representation
      originalContentToStore =
        "Content could not be stored due to invalid format";
    }

    // First check if the content exists to avoid unnecessary upsert operations
    const existingContent = await prisma.contentSummary.findUnique({
      where: { contentId: metadata.contentId },
      select: { id: true }, // Only select the ID to minimize data transfer
    });

    if (existingContent) {
      // If content exists, just update it
      await prisma.contentSummary.update({
        where: { contentId: metadata.contentId },
        data: {
          content: aiResponse,
          originalContent: originalContentToStore,
          outputType,
          title,
          generatedAt: new Date(),
        },
      });
    } else {
      // If content doesn't exist, create it
      await prisma.contentSummary.create({
        data: {
          userId,
          contentId: metadata.contentId,
          contentType: metadata.contentType.toUpperCase() as ContentType,
          sourceId: metadata.sourceId || "",
          title,
          content: aiResponse,
          originalContent: originalContentToStore,
          outputType,
          generatedAt: new Date(),
        },
      });
    }
  } catch (dbError) {
    console.error("Database error in transform route:", dbError);
    throw dbError;
  }
}

/**
 * Handle errors from the transformation process
 *
 * @param error The error object
 * @returns A NextResponse with the appropriate error message
 */
function handleError(error: unknown): NextResponse {
  // Handle specific error types
  if (error instanceof Error) {
    // Rate limit errors
    if (error.message === "Rate limit exceeded") {
      return NextResponse.json(
        {
          error: "API rate limit reached. Please try again later.",
          isRateLimit: true,
        },
        { status: 429 }
      );
    }

    // Timeout errors
    if (error.message === "API timeout" || error.message.includes("timeout")) {
      return NextResponse.json(
        {
          error:
            "Request timed out. Please try again with shorter content or try breaking it into smaller parts.",
          isTimeout: true,
        },
        { status: 504 }
      );
    }

    // JSON parsing errors
    if (
      error.message.includes("JSON") ||
      error.message.includes("Unexpected end")
    ) {
      return NextResponse.json(
        {
          error:
            "There was a problem processing your content. Please try again or try with a smaller document.",
          isJsonError: true,
        },
        { status: 400 }
      );
    }

    // Content formatting errors
    if (error.message.includes("format")) {
      return NextResponse.json(
        {
          error:
            "There was a problem with the content format. Please try again with a different file.",
          isFormatError: true,
        },
        { status: 400 }
      );
    }

    // Invalid array length errors
    if (error.message.includes("Invalid array length")) {
      return NextResponse.json(
        {
          error:
            "The PDF content contains invalid data that couldn't be processed. Please try a different PDF file.",
          isInvalidArrayError: true,
        },
        { status: 400 }
      );
    }
  }

  // Generic error handler for all other cases
  return NextResponse.json(
    {
      error:
        error instanceof Error
          ? error.message
          : "An unexpected error occurred while processing your content. Please try again.",
      isUnexpected: true,
    },
    { status: 500 }
  );
}

export async function POST(req: Request) {
  // Start timing the entire transformation process
  const startTime = Date.now();
  startTiming("transform:total");

  // Add memory usage tracking
  const initialMemoryUsage = process.memoryUsage();
  console.log(
    `Initial memory usage: ${Math.round(
      initialMemoryUsage.heapUsed / 1024 / 1024
    )}MB`
  );

  // No progress tracking variables needed

  try {
    // Authenticate user
    startTiming("transform:auth");
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    const authTime = endTiming("transform:auth");
    console.log(`Authentication completed in ${authTime?.toFixed(2)}ms`);

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscription to determine plan type - ADDED
    const subscription = await getUserSubscription(user.id);
    const planType = subscription?.planType || "Free";

    // Check usage limit, now with the correct planType - MODIFIED
    const usageCheck = await checkUsageLimit(
      user.id,
      FeatureType.TRANSFORMATIONS,
      planType // Pass the determined planType
    );
    if (!usageCheck.allowed) {
      return NextResponse.json(
        {
          error: usageCheck.message,
          usageData: usageCheck,
        },
        { status: 403 }
      );
    }

    // Parse request body
    const body = await parseRequestBody(req);
    if (!body) {
      return NextResponse.json(
        { error: "Invalid request format. Please check your request body." },
        { status: 400 }
      );
    }

    const {
      sourceContent,
      metadata,
      outputType,
      contentType = "youtube",
    } = body;

    // Check cache first
    startTiming("transform:cache-check");
    const cacheKey = getTransformCacheKey(
      metadata.contentId,
      contentType,
      outputType,
      "v2" // Updated version for new caching system
    );
    const cacheExpiryType =
      process.env.NODE_ENV === "production" ? "MEDIUM" : "SHORT";
    const cachedResult = getCachedData(cacheKey, cacheExpiryType);
    endTiming("transform:cache-check");

    if (cachedResult) {
      console.log(
        "Using cached transformation result for:",
        metadata.contentId
      );
      // No progress tracking needed
      return NextResponse.json(cachedResult);
    }

    // Validate request parameters
    if (
      !sourceContent ||
      (typeof sourceContent === "string" && !sourceContent.trim())
    ) {
      return NextResponse.json(
        { error: "Valid source content is required" },
        { status: 400 }
      );
    }

    if (!outputType || !["summary", "notes"].includes(outputType)) {
      return NextResponse.json(
        { error: "Output type must be 'summary' or 'notes'" },
        { status: 400 }
      );
    }

    if (!metadata) {
      return NextResponse.json(
        { error: "Content metadata is required" },
        { status: 400 }
      );
    }

    // Format content based on type
    let formattedContent = "";
    try {
      formattedContent = formatContent(sourceContent, contentType);
    } catch (formatError) {
      console.error("Error formatting content:", formatError);
      return NextResponse.json(
        { error: "Failed to process content format" },
        { status: 400 }
      );
    }

    if (!formattedContent.trim()) {
      return NextResponse.json(
        { error: "Empty content after formatting" },
        { status: 400 }
      );
    }

    // Start AI processing
    startTiming("transform:ai-processing");

    // Define token threshold and chunking parameters
    const TOKEN_THRESHOLD = 100000;
    const MAX_TOKEN_LIMIT = 200000; // Absolute maximum we can handle

    // Estimate token count
    const tokenCount = estimateTokens(formattedContent);
    console.log(`Estimated token count for ${contentType}: ${tokenCount}`);

    // If content is extremely large, truncate it
    let processedContent = formattedContent;
    if (tokenCount > MAX_TOKEN_LIMIT) {
      processedContent = truncateContent(
        formattedContent,
        contentType,
        tokenCount,
        MAX_TOKEN_LIMIT
      );
      console.log(
        `Content truncated to approximately ${estimateTokens(
          processedContent
        )} tokens`
      );
    }

    // Process content with AI
    let aiResponse = "";

    // For large content, use chunking
    if (tokenCount > TOKEN_THRESHOLD) {
      startTiming("transform:chunked-processing");

      // No progress tracking needed

      console.log(
        `Content size: ${processedContent.length} characters, using chunked processing`
      );

      try {
        // Calculate chunk size based on token threshold
        const chunkSize = Math.floor(TOKEN_THRESHOLD * 4);
        const overlapSize = Math.floor(chunkSize * 0.1); // 10% overlap

        // Process content with chunking
        aiResponse = await processContentWithChunking(
          outputType,
          metadata,
          processedContent,
          limiter,
          model,
          chunkSize,
          overlapSize,
          (chunkIndex: number, progress: number) => {
            // Log progress for debugging
            console.log(
              `Processing chunk ${chunkIndex + 1}, progress: ${Math.round(
                progress * 100
              )}%`
            );
          }
        );
      } catch (error) {
        console.error("Error in chunked processing:", error);
        throw error;
      } finally {
        endTiming("transform:chunked-processing");
      }
    }
    // For smaller content, use a single prompt
    else {
      startTiming("transform:single-prompt");

      // No progress tracking needed for small content

      console.log(
        `Content size: ${processedContent.length} characters, using single prompt`
      );

      try {
        // Import here to avoid circular dependencies
        const { generatePrompt } = await import(
          "@/utils/prompts/contentPrompt"
        );

        // Generate prompt for the content
        const prompt = generatePrompt(outputType, metadata, processedContent);

        // Process with AI
        aiResponse = await generateWithRateLimit(prompt, limiter, model);
      } catch (error) {
        console.error("Error in single prompt processing:", error);
        throw error;
      } finally {
        endTiming("transform:single-prompt");
      }
    }

    // End timing overall AI processing
    endTiming("transform:ai-processing");

    // No progress tracking needed for finalizing

    if (!aiResponse.trim()) {
      throw new Error("Empty response from AI service");
    }

    // MOVED: Record usage immediately after successful AI processing and response validation
    // This ensures usage is recorded even if subsequent database/caching operations fail.
    await recordUsage(user.id, FeatureType.TRANSFORMATIONS, {
      contentId: metadata.contentId,
      outputType: outputType,
      contentType: contentType,
    });

    // Extract title
    const title = extractTitle(aiResponse, metadata);

    // Store in database
    startTiming("transform:database");
    await storeInDatabase(
      user.id,
      metadata,
      aiResponse,
      sourceContent,
      outputType,
      title
    );
    endTiming("transform:database");

    // Prepare the response data
    const responseData = {
      outputContent: aiResponse,
      metadata: {
        title,
        contentId: metadata.contentId,
        contentType: metadata.contentType,
        generatedAt: new Date().toISOString(),
        outputType,
      },
    };

    // Cache the result
    startTiming("transform:cache-store");
    const contentSize = aiResponse.length;
    const cachePriority =
      contentSize > 100000
        ? CachePriority.HIGH // Large content gets high priority (expensive to regenerate)
        : contentSize > 10000
        ? CachePriority.MEDIUM
        : CachePriority.LOW;

    cacheData(cacheKey, responseData, cacheExpiryType, cachePriority);
    endTiming("transform:cache-store");

    // No progress tracking needed for completion

    // End timing the entire transformation process
    const totalTime = endTiming("transform:total");
    const elapsedMs = Date.now() - startTime;

    // Log performance metrics
    console.log(
      `🚀 Total transformation time: ${totalTime?.toFixed(
        2
      )}ms (wall clock: ${elapsedMs}ms)`
    );

    // Log memory usage after processing
    const finalMemoryUsage = process.memoryUsage();
    const memoryDiff = finalMemoryUsage.heapUsed - initialMemoryUsage.heapUsed;
    console.log(
      `Final memory usage: ${Math.round(
        finalMemoryUsage.heapUsed / 1024 / 1024
      )}MB`
    );
    console.log(
      `Memory change: ${memoryDiff > 0 ? "+" : ""}${Math.round(
        memoryDiff / 1024 / 1024
      )}MB`
    );

    // Add performance headers to the response
    const response = NextResponse.json(responseData);
    response.headers.set("X-Processing-Time", elapsedMs.toString());

    return response;
  } catch (error) {
    // End timing even on error
    endTiming("transform:total");
    const elapsedMs = Date.now() - startTime;
    console.error(`Error in transform route (${elapsedMs}ms):`, error);

    // No progress tracking needed for errors

    // Log memory usage after error
    const finalMemoryUsage = process.memoryUsage();
    console.log(
      `Memory usage after error: ${Math.round(
        finalMemoryUsage.heapUsed / 1024 / 1024
      )}MB`
    );

    // Create error response with timing information
    const errorResponse = handleError(error);
    errorResponse.headers.set("X-Processing-Time", elapsedMs.toString());

    return errorResponse;
  }
}
