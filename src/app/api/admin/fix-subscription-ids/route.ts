import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, users, subscription } from "@/lib/db";
import { verifyTransaction } from "@/lib/paystack";
import { eq, not, startsWith, inArray } from "drizzle-orm";

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and is admin
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Get user from database
    const dbUserResult = await db
      .select()
      .from(users)
      .where(eq(users.id, user.id))
      .limit(1);

    const dbUser = dbUserResult[0];

    if (!dbUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // For now, any authenticated user can run this fix
    // In production, you might want to add proper admin role checking

    console.log("🔧 Admin initiated subscription ID fix process...");

    // Find subscriptions that might have transaction references instead of subscription codes
    const suspiciousSubscriptions = await prisma.subscription.findMany({
      where: {
        subscriptionId: {
          not: {
            startsWith: "SUB_",
          },
        },
        status: {
          in: ["ACTIVE", "NON_RENEWING"],
        },
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    console.log(
      `📊 Found ${suspiciousSubscriptions.length} subscriptions with potential transaction reference IDs`
    );

    let fixedCount = 0;
    let errorCount = 0;
    const results = [];

    for (const subscription of suspiciousSubscriptions) {
      console.log(
        `\n🔍 Processing subscription ${subscription.id} with ID: ${subscription.subscriptionId}`
      );

      try {
        // Try to verify the transaction to get subscription details
        const transactionDetails = await verifyTransaction(
          subscription.subscriptionId
        );

        if (transactionDetails?.data?.subscription?.subscription_code) {
          const subscriptionCode =
            transactionDetails.data.subscription.subscription_code;
          const emailToken = transactionDetails.data.subscription.email_token;

          console.log(`✅ Found subscription code: ${subscriptionCode}`);

          // Update the subscription with the correct subscription_code
          await prisma.subscription.update({
            where: { id: subscription.id },
            data: {
              subscriptionId: subscriptionCode,
              emailToken: emailToken,
              metadata: {
                ...((subscription.metadata as any) || {}),
                fixedSubscriptionId: true,
                fixedAt: new Date().toISOString(),
                originalTransactionReference: subscription.subscriptionId,
                fixedByUser: dbUser.email,
              },
            },
          });

          console.log(
            `✅ Updated subscription ${subscription.id} with correct subscription code`
          );
          fixedCount++;

          results.push({
            subscriptionId: subscription.id,
            userEmail: subscription.user.email,
            oldId: subscription.subscriptionId,
            newId: subscriptionCode,
            status: "fixed",
          });
        } else {
          console.log(
            `⚠️ No subscription details found in transaction for ${subscription.subscriptionId}`
          );
          results.push({
            subscriptionId: subscription.id,
            userEmail: subscription.user.email,
            oldId: subscription.subscriptionId,
            status: "no_subscription_found",
          });
        }
      } catch (error) {
        console.error(
          `❌ Error processing subscription ${subscription.id}:`,
          error instanceof Error ? error.message : String(error)
        );
        errorCount++;

        results.push({
          subscriptionId: subscription.id,
          userEmail: subscription.user.email,
          oldId: subscription.subscriptionId,
          status: "error",
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    console.log(`\n📊 Fix process completed:`);
    console.log(`✅ Fixed: ${fixedCount} subscriptions`);
    console.log(`❌ Errors: ${errorCount} subscriptions`);
    console.log(
      `📋 Total processed: ${suspiciousSubscriptions.length} subscriptions`
    );

    return NextResponse.json({
      success: true,
      summary: {
        totalProcessed: suspiciousSubscriptions.length,
        fixed: fixedCount,
        errors: errorCount,
      },
      results,
    });
  } catch (error) {
    console.error("❌ Fatal error in fix process:", error);
    return NextResponse.json(
      {
        error: "Failed to fix subscription IDs",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
