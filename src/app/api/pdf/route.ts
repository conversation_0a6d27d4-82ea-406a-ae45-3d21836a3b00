import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import prisma from "@/utils/prisma";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function POST(req: Request) {
  try {
    // Auth check
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body with additional error handling
    let body;
    try {
      body = await req.json();
    } catch (jsonError) {
      console.error("JSON parsing error in PDF route:", jsonError);
      return NextResponse.json(
        {
          error: "Invalid JSON format in request body",
          details:
            jsonError instanceof Error
              ? jsonError.message
              : "Unknown JSON error",
        },
        { status: 400 }
      );
    }

    const {
      contentId,
      fileName,
      text,
      outputType = "short",
      usedOcr = false,
    } = body;

    if (!contentId || !text) {
      return NextResponse.json(
        { error: "Content ID and text are required" },
        { status: 400 }
      );
    }

    // Validate text is a string
    if (typeof text !== "string") {
      console.error("Invalid text format:", typeof text);
      return NextResponse.json(
        { error: "Text must be a string" },
        { status: 400 }
      );
    }

    // Check text length as a rough approximation of file size
    // A very rough estimate: 1 character ≈ 1 byte
    const estimatedSize = text.length;
    const MAX_SIZE = 25 * 1024 * 1024; // 25MB

    if (estimatedSize > MAX_SIZE) {
      return NextResponse.json(
        { error: "File size must be less than 25MB" },
        { status: 400 }
      );
    }

    // Store the PDF information in the database
    try {
      // Sanitize text to ensure it can be safely stringified
      const sanitizedText = text
        .replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/g, "") // Remove control chars
        .replace(/\uFFFD/g, ""); // Remove replacement character

      // Store OCR information in the originalContent as JSON with error handling
      let originalContentWithMeta;
      try {
        originalContentWithMeta = JSON.stringify({
          text: sanitizedText,
          usedOcr,
        });
      } catch (jsonError) {
        console.error("Error stringifying PDF content:", jsonError);
        // Fallback to a simpler representation if JSON.stringify fails
        originalContentWithMeta = JSON.stringify({
          text: "PDF content could not be processed due to invalid characters",
          usedOcr,
          error: true,
        });
        return NextResponse.json(
          {
            error:
              "Failed to process PDF content - it may contain invalid characters",
          },
          { status: 400 }
        );
      }

      await prisma.contentSummary.create({
        data: {
          userId: user.id,
          contentId: contentId,
          contentType: "PDF",
          sourceId: contentId,
          title: fileName?.replace(/\.pdf$/i, "") || "PDF Document",
          content: "", // Will be filled by the transform API
          originalContent: originalContentWithMeta,
          outputType: outputType,
          generatedAt: new Date(),
        },
      });
    } catch (dbError) {
      console.error("Database error:", dbError);

      // Check for specific database errors
      const errorMessage =
        dbError instanceof Error ? dbError.message : "Unknown database error";

      if (
        errorMessage.includes("Invalid array length") ||
        errorMessage.includes("JSON")
      ) {
        return NextResponse.json(
          {
            error:
              "The PDF content contains invalid data that couldn't be processed",
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: "Failed to store PDF information", details: errorMessage },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      contentId,
      metadata: {
        contentId,
        contentType: "pdf",
        title: fileName?.replace(/\.pdf$/i, "") || "PDF Document",
        sourceId: contentId,
        outputType,
        usedOcr, // Include OCR information in the response
      },
    });
  } catch (error) {
    console.error("Error in PDF route:", error);

    // Provide more specific error messages based on error type
    if (error instanceof Error) {
      const errorMessage = error.message;

      if (errorMessage.includes("JSON") || errorMessage.includes("parse")) {
        return NextResponse.json(
          {
            error: "Failed to process PDF content - invalid format",
            details: errorMessage,
          },
          { status: 400 }
        );
      } else if (errorMessage.includes("Invalid array length")) {
        return NextResponse.json(
          {
            error:
              "The PDF content contains invalid data that couldn't be processed",
            details: errorMessage,
          },
          { status: 400 }
        );
      } else if (
        errorMessage.includes("too large") ||
        errorMessage.includes("size")
      ) {
        return NextResponse.json(
          {
            error: "The PDF file is too large to process",
            details: errorMessage,
          },
          { status: 413 }
        );
      }

      return NextResponse.json(
        {
          error: errorMessage,
          details: "An error occurred while processing the PDF",
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      {
        error: "An unexpected error occurred while processing the PDF",
      },
      { status: 500 }
    );
  }
}
