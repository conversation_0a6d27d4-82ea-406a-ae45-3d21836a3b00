import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, contentSummary } from "@/lib/db";
import { eq } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Auth check
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const contentId = id;

    if (!contentId) {
      return NextResponse.json(
        { error: "Content ID is required" },
        { status: 400 }
      );
    }

    // Try to find the content in the database
    const contentSummaryResult = await db
      .select()
      .from(contentSummary)
      .where(eq(contentSummary.contentId, contentId))
      .limit(1);

    const contentSummaryData = contentSummaryResult[0];

    if (contentSummaryData) {
      // If found, return the original content
      try {
        const originalContent = JSON.parse(contentSummaryData.originalContent);

        // Check if the originalContent has the new format with OCR information
        const text = originalContent.text || originalContent;
        const usedOcr = originalContent.usedOcr || false;

        return NextResponse.json({
          text: typeof text === "string" ? text : JSON.stringify(text),
          metadata: {
            contentId: contentSummaryData.contentId,
            contentType: contentSummaryData.contentType.toLowerCase(),
            title: contentSummaryData.title,
            sourceId: contentSummaryData.sourceId,
            usedOcr, // Include OCR information in the response
          },
        });
      } catch (error) {
        // If parsing fails, return the raw content
        return NextResponse.json({
          text: contentSummaryData.originalContent,
          metadata: {
            contentId: contentSummaryData.contentId,
            contentType: contentSummaryData.contentType.toLowerCase(),
            title: contentSummaryData.title,
            sourceId: contentSummaryData.sourceId,
            usedOcr: false, // Default to false if parsing fails
          },
        });
      }
    }

    // If not found, return an error
    return NextResponse.json({ error: "Content not found" }, { status: 404 });
  } catch (error) {
    console.error("Error in pdf/[id] route:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
