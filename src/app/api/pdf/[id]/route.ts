import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import prisma from "@/utils/prisma";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Auth check
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const contentId = id;

    if (!contentId) {
      return NextResponse.json(
        { error: "Content ID is required" },
        { status: 400 }
      );
    }

    // Try to find the content in the database
    const contentSummary = await prisma.contentSummary.findUnique({
      where: {
        contentId: contentId,
      },
    });

    if (contentSummary) {
      // If found, return the original content
      try {
        const originalContent = JSON.parse(contentSummary.originalContent);

        // Check if the originalContent has the new format with OCR information
        const text = originalContent.text || originalContent;
        const usedOcr = originalContent.usedOcr || false;

        return NextResponse.json({
          text: typeof text === "string" ? text : JSON.stringify(text),
          metadata: {
            contentId: contentSummary.contentId,
            contentType: contentSummary.contentType.toLowerCase(),
            title: contentSummary.title,
            sourceId: contentSummary.sourceId,
            usedOcr, // Include OCR information in the response
          },
        });
      } catch (error) {
        // If parsing fails, return the raw content
        return NextResponse.json({
          text: contentSummary.originalContent,
          metadata: {
            contentId: contentSummary.contentId,
            contentType: contentSummary.contentType.toLowerCase(),
            title: contentSummary.title,
            sourceId: contentSummary.sourceId,
            usedOcr: false, // Default to false if parsing fails
          },
        });
      }
    }

    // If not found, return an error
    return NextResponse.json({ error: "Content not found" }, { status: 404 });
  } catch (error) {
    console.error("Error in pdf/[id] route:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
