import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { eq, desc } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get recent content for dashboard display
    const recentContent = await db
      .select()
      .from(contentSummary)
      .where(eq(contentSummary.userId, user.id))
      .orderBy(desc(contentSummary.generatedAt))
      .limit(7); // Limit to 7 most recent content items

    return NextResponse.json({
      recentContent,
    });
  } catch (error) {
    console.error("Recent Transformations API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch recent transformations" },
      { status: 500 }
    );
  }
}
