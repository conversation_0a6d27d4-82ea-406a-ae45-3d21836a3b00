import { NextResponse } from "next/server";
import prisma from "@/utils/prisma";
import { createClient } from "@/utils/supabase/server";
import { NextRequest } from "next/server";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET(request: NextRequest) {
  try {
    // Get pagination parameters from URL
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1", 10);
    const pageSize = parseInt(searchParams.get("pageSize") || "12", 10);

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use Promise.all to run queries concurrently for better performance
    const [totalItems, content] = await Promise.all([
      // Get total count for pagination
      prisma.contentSummary.count({
        where: { userId: user.id },
      }),
      // Get paginated content with optimized field selection
      prisma.contentSummary.findMany({
        where: { userId: user.id },
        orderBy: { generatedAt: "desc" },
        skip,
        take: pageSize,
        select: {
          id: true,
          contentId: true,
          title: true,
          // Only include content field if specifically requested (for individual view)
          content: searchParams.get("includeContent") === "true",
          contentType: true,
          sourceId: true,
          outputType: true,
          generatedAt: true,
          isShared: true,
          shareToken: true,
          userId: true,
        },
      }),
    ]);

    // Calculate total pages
    const totalPages = Math.ceil(totalItems / pageSize);

    // Log pagination info for debugging (only in development)
    if (process.env.NODE_ENV === "development") {
      console.log("Transformations API pagination:", {
        page,
        pageSize,
        totalItems,
        totalPages,
        contentCount: content.length,
        includeContent: searchParams.get("includeContent") === "true",
      });
    }

    return NextResponse.json({
      content,
      pagination: {
        page,
        pageSize,
        totalItems,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Transformations API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch transformations data" },
      { status: 500 }
    );
  }
}
