import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { NextRequest } from "next/server";
import { eq, desc, count } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function GET(request: NextRequest) {
  try {
    // Get pagination parameters from URL
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1", 10);
    const pageSize = parseInt(searchParams.get("pageSize") || "12", 10);

    // Calculate skip value for pagination
    const skip = (page - 1) * pageSize;

    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use Promise.all to run queries concurrently for better performance
    const [totalItemsResult, content] = await Promise.all([
      // Get total count for pagination
      db
        .select({ count: count() })
        .from(contentSummary)
        .where(eq(contentSummary.userId, user.id)),
      // Get paginated content with optimized field selection
      db
        .select({
          id: contentSummary.id,
          contentId: contentSummary.contentId,
          title: contentSummary.title,
          // Only include content field if specifically requested (for individual view)
          ...(searchParams.get("includeContent") === "true" && {
            content: contentSummary.content,
          }),
          contentType: contentSummary.contentType,
          sourceId: contentSummary.sourceId,
          outputType: contentSummary.outputType,
          generatedAt: contentSummary.generatedAt,
          isShared: contentSummary.isShared,
          shareToken: contentSummary.shareToken,
          userId: contentSummary.userId,
        })
        .from(contentSummary)
        .where(eq(contentSummary.userId, user.id))
        .orderBy(desc(contentSummary.generatedAt))
        .offset(skip)
        .limit(pageSize),
    ]);

    const totalItems = totalItemsResult[0]?.count || 0;

    // Calculate total pages
    const totalPages = Math.ceil(totalItems / pageSize);

    // Log pagination info for debugging (only in development)
    if (process.env.NODE_ENV === "development") {
      console.log("Transformations API pagination:", {
        page,
        pageSize,
        totalItems,
        totalPages,
        contentCount: content.length,
        includeContent: searchParams.get("includeContent") === "true",
      });
    }

    return NextResponse.json({
      content,
      pagination: {
        page,
        pageSize,
        totalItems,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Transformations API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch transformations data" },
      { status: 500 }
    );
  }
}
