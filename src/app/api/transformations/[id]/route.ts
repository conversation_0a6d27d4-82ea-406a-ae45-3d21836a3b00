import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { eq, and } from "drizzle-orm";

export const dynamic = "force-dynamic";

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  const supabase = await createClient();

  try {
    // Use getUser() instead of getSession() for better security
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Find the content to ensure it belongs to the user
    const contentSummaryResult = await db
      .select()
      .from(contentSummary)
      .where(and(eq(contentSummary.id, id), eq(contentSummary.userId, user.id)))
      .limit(1);

    const contentSummaryData = contentSummaryResult[0];

    if (!contentSummaryData) {
      return NextResponse.json(
        { error: "Content not found or access denied" },
        { status: 404 }
      );
    }

    // Delete the content
    await db.delete(contentSummary).where(eq(contentSummary.id, id));

    return NextResponse.json({
      success: true,
      message: "Content and all associated data deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting content:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Unexpected error" },
      { status: 500 }
    );
  }
}
