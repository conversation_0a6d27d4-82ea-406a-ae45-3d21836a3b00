import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import prisma from "@/utils/prisma";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function POST(req: Request) {
  try {
    // Auth check
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const {
      contentId,
      url,
      title,
      content,
      author,
      lengthType = "short",
    } = body;

    if (!contentId || !url || !content) {
      return NextResponse.json(
        { error: "Content ID, URL, and content are required" },
        { status: 400 }
      );
    }

    // First check if content already exists using a single query
    const existingContent = await prisma.contentSummary.findFirst({
      where: {
        userId: user.id,
        sourceId: url,
        contentType: "WEBPAGE",
      },
    });

    if (existingContent) {
      return NextResponse.json({
        contentId: existingContent.contentId,
        metadata: {
          contentId: existingContent.contentId,
          contentType: "webpage",
          title: existingContent.title || "Webpage Content",
          sourceId: url,
          author,
          url,
          lengthType,
        },
      });
    }

    // If no existing content, store the webpage information in the database
    try {
      await prisma.contentSummary.create({
        data: {
          userId: user.id,
          contentId: contentId,
          contentType: "WEBPAGE",
          sourceId: url,
          title: title || "Webpage Content",
          content: "", // Will be filled by the transform API
          originalContent: JSON.stringify({
            content,
            author,
            url,
          }),
          outputType: lengthType,
          generatedAt: new Date(),
        },
      });
    } catch (dbError) {
      // Handle unique constraint errors - this is a race condition where another request
      // might have created the same content between our check and create
      if (
        typeof dbError === "object" &&
        dbError !== null &&
        "code" in dbError &&
        dbError.code === "P2002"
      ) {
        try {
          // Try to find the content that was just created by another request
          const conflictingContent = await prisma.contentSummary.findFirst({
            where: {
              userId: user.id,
              sourceId: url,
              contentType: "WEBPAGE",
            },
          });

          if (conflictingContent) {
            return NextResponse.json({
              contentId: conflictingContent.contentId,
              metadata: {
                contentId: conflictingContent.contentId,
                contentType: "webpage",
                title: conflictingContent.title || "Webpage Content",
                sourceId: url,
                author,
                url,
                lengthType,
              },
            });
          }
        } catch (findError) {
          console.error("Error finding conflicting content:", findError);
        }
      }

      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: "Failed to store webpage information" },
        { status: 500 }
      );
    }

    // Return success response
    return NextResponse.json({
      contentId,
      metadata: {
        contentId,
        contentType: "webpage",
        title: title || "Webpage Content",
        sourceId: url,
        author,
        url,
        lengthType,
      },
    });
  } catch (error) {
    console.error("Error in webpage route:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
      },
      { status: 500 }
    );
  }
}
