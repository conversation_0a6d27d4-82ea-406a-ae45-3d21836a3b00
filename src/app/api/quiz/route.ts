import { NextResponse } from "next/server";
import { GoogleGenerativeAI } from "@google/generative-ai";
import prisma from "@/utils/prisma";
import { createClient } from "@/utils/supabase/server";
import { RateLimiter } from "limiter";
import { parseQuizQuestions } from "@/utils/jsonParser";
import { checkUsageLimit, recordUsage } from "@/lib/usage-utils";
import { FeatureType } from "@/types/subscription";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";
export const maxDuration = 60; // Maximum duration in seconds

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
const model = genAI.getGenerativeModel({ model: "gemini-2.0-flash" });

// Create a rate limiter instance
const limiter = new RateLimiter({
  tokensPerInterval: 15,
  interval: "minute",
  fireImmediately: true,
});

const QUIZ_PROMPT = `You are an AI quiz generator. Generate quiz questions based on the provided content.
Follow these rules:
1. Generate multiple-choice questions with 4 options each
2. Questions should test understanding of key concepts from the content
3. Generate between 5-30 questions depending on content length
4. Each question should have only one correct answer
5. Use markdown formatting for:
   - Code snippets (with triple backticks and language specifier)
   - Mathematical formulas (using LaTeX syntax with $ for inline and $$ for block)
   - Emphasis, bold, or other markdown elements as appropriate
6. For each question, provide a concise explanation that references the specific part of the content
7. Format the response as a JSON array of objects with the following structure:
   {
     "question": "The question text with optional markdown",
     "options": ["option1 with optional markdown", "option2", "option3", "option4"],
     "correctAnswer": 0, // Index of correct answer (0-3)
     "explanation": "Explanation with reference and optional markdown"
   }
8. Questions should increase in difficulty.
9. DO NOT include any timestamps or time references in questions, options, or explanations.
10. Keep explanations concise and focused on the key points.`;

export async function POST(req: Request) {
  try {
    // Check rate limit - tryRemoveTokens returns boolean
    const hasTokens = limiter.tryRemoveTokens(1);
    if (!hasTokens) {
      return NextResponse.json(
        {
          error:
            "Rate limit exceeded. Please wait a minute before trying again.",
          isRateLimit: true,
        },
        { status: 429 }
      );
    }

    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check usage limit
    const usageCheck = await checkUsageLimit(
      user.id,
      FeatureType.QUIZZES
    );
    if (!usageCheck.allowed) {
      return NextResponse.json(
        {
          error: usageCheck.message,
          usageData: usageCheck,
        },
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      sourceContent,
      metadata,
      contentId,
      contentType = "youtube",
    } = body;

    if (!sourceContent || !metadata || !contentId) {
      return NextResponse.json(
        { error: "Missing required data" },
        { status: 400 }
      );
    }

    // Set timeout for Gemini API call
    const timeoutMs = 25000; // 25 seconds
    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), timeoutMs);

    try {
      // Get content summary
      const contentSummary = await prisma.contentSummary.findUnique({
        where: { contentId: contentId },
      });

      if (!contentSummary) {
        return NextResponse.json(
          { error: "Content summary not found" },
          { status: 404 }
        );
      }

      // Customize prompt based on content type
      let contentDescription = "";
      if (contentType === "youtube") {
        contentDescription = `Video Title: ${metadata.title}\nChannel: ${
          metadata.channelTitle || "Unknown"
        }`;
      } else if (contentType === "text") {
        contentDescription = `Text Title: ${metadata.title || "Untitled Text"}`;
      } else if (contentType === "pdf") {
        contentDescription = `PDF Title: ${metadata.title || "Untitled PDF"}`;
      } else if (contentType === "webpage") {
        contentDescription = `Webpage: ${
          metadata.title || metadata.sourceId || "Untitled Webpage"
        }`;
      }

      const prompt = `${QUIZ_PROMPT}\n\n${contentDescription}\n\nContent:\n${sourceContent}\n\nSummary:\n${contentSummary.content}`;

      const result = await Promise.race([
        model.generateContent(prompt),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error("API timeout")), timeoutMs)
        ),
      ]);

      clearTimeout(timeout);

      if (!result?.response) {
        throw new Error("Invalid AI response");
      }

      const response = result.response;
      const content = response.text();

      console.log(`[Quiz API] AI response length: ${content.length}`);
      console.log(
        `[Quiz API] AI response preview: ${content.substring(0, 300)}...`
      );

      // Use robust JSON parser
      const parseResult = parseQuizQuestions(content, true);

      if (!parseResult.success) {
        console.error(
          "[Quiz API] Failed to parse quiz questions:",
          parseResult.error,
          "Method attempted:",
          parseResult.method
        );
        throw new Error(
          `Failed to parse quiz questions from AI response: ${parseResult.error}`
        );
      }

      const questions = parseResult.data;
      if (!questions || questions.length === 0) {
        throw new Error("No valid quiz questions were generated");
      }

      console.log(
        `[Quiz API] Successfully parsed ${questions.length} questions using method: ${parseResult.method}`
      );

      const quiz = await prisma.quiz.create({
        data: {
          contentSummaryId: contentSummary.id,
          userId: user.id,
          questions: questions,
        },
      });

      // Record usage after successful quiz generation
      await recordUsage(user.id, FeatureType.QUIZZES);

      return NextResponse.json({ questions, quizId: quiz.id });
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === "AbortError" || error.message === "API timeout") {
          return NextResponse.json(
            {
              error: "Request timed out. Please try with shorter content.",
              isTimeout: true,
            },
            { status: 504 }
          );
        }

        if (error.message?.includes("quota")) {
          return NextResponse.json(
            {
              error: "API rate limit reached. Please try again later.",
              isRateLimit: true,
            },
            { status: 429 }
          );
        }
      }
      throw error;
    }
  } catch (error) {
    console.error("Error in quiz processing:", error);
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        isUnexpected: true,
      },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const { contentId, contentType, answers, score } = body;

    if (!contentId || !answers || typeof score !== "number") {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Get content summary
    const contentSummary = await prisma.contentSummary.findUnique({
      where: {
        contentId: contentId,
      },
    });

    if (!contentSummary) {
      return NextResponse.json(
        { error: "Content summary not found" },
        { status: 404 }
      );
    }

    // Get the latest quiz for this content
    const quiz = await prisma.quiz.findFirst({
      where: {
        contentSummaryId: contentSummary.id,
        userId: user.id,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    if (!quiz) {
      return NextResponse.json({ error: "Quiz not found" }, { status: 404 });
    }

    const quizAttempt = await prisma.quizAttempt.create({
      data: {
        quizId: quiz.id,
        userId: user.id,
        answers,
        score,
      },
    });

    return NextResponse.json({ quizAttempt });
  } catch (error) {
    console.error("Error saving quiz attempt:", error);
    return NextResponse.json(
      { error: "Failed to save quiz attempt" },
      { status: 500 }
    );
  }
}

export async function GET(req: Request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const contentId = searchParams.get("contentId");

    if (!contentId) {
      return NextResponse.json(
        { error: "Content ID is required" },
        { status: 400 }
      );
    }

    // Get content summary
    const contentSummary = await prisma.contentSummary.findUnique({
      where: {
        contentId: contentId,
      },
      include: {
        quizzes: {
          include: {
            attempts: true,
          },
        },
      },
    });

    if (!contentSummary) {
      return NextResponse.json(
        { error: "Content summary not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ quizzes: contentSummary.quizzes });
  } catch (error) {
    console.error("Error retrieving quiz history:", error);
    return NextResponse.json(
      { error: "Failed to retrieve quiz history" },
      { status: 500 }
    );
  }
}
