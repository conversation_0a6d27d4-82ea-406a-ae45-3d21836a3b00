import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { verifyTransaction } from "@/lib/flutterwave";
import { eq, and, or } from "drizzle-orm";

// Define enum types for subscription status and plan type
type SubscriptionStatus =
  | "ACTIVE"
  | "CANCELLED"
  | "NON_RENEWING"
  | "ATTENTION"
  | "COMPLETED";
type PlanType = "STARTER" | "PRO" | "UNLIMITED";

const SubscriptionStatus = {
  ACTIVE: "ACTIVE" as const,
  CANCELLED: "CANCELLED" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  ATTENTION: "ATTENTION" as const,
  COMPLETED: "COMPLETED" as const,
};

const PlanType = {
  STARTER: "STARTER" as const,
  PRO: "PRO" as const,
  UNLIMITED: "UNLIMITED" as const,
};

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { transactionRef } = await request.json();

    console.log("🔄 Secure subscription activation requested:", {
      userId: user.id,
      userEmail: user.email,
      transactionRef,
    });

    // Find the subscription by transaction reference
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        and(
          eq(subscriptionTable.userId, user.id),
          or(
            eq(subscriptionTable.subscriptionId, transactionRef),
            eq(subscriptionTable.subscriptionId, `pending_${transactionRef}`),
            eq(subscriptionTable.status, "PENDING")
          )
        )
      )
      .limit(1);

    const subscriptionData = subscriptionResult[0];

    if (!subscriptionData) {
      console.log("❌ No subscription found for user:", user.id);
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    console.log("📋 Found subscription:", {
      id: subscriptionData.id,
      status: subscriptionData.status,
      subscriptionId: subscriptionData.subscriptionId,
    });

    // If already active, return success
    if (subscriptionData.status === "ACTIVE") {
      console.log("✅ Subscription already active");
      return NextResponse.json({
        success: true,
        message: "Subscription is already active",
        subscription: {
          id: subscriptionData.id,
          status: subscriptionData.status,
          planType: subscriptionData.planType,
          billingCycle: subscriptionData.billingCycle,
        },
      });
    }

    // Step 1: Verify the transaction first
    let transactionVerified = false;
    let transactionData = null;

    if (transactionRef) {
      try {
        console.log("🔍 Verifying transaction:", transactionRef);
        const verification = await verifyTransaction(transactionRef);

        if (
          verification.status === "success" &&
          verification.data?.status === "successful"
        ) {
          transactionVerified = true;
          transactionData = verification.data;
          console.log("✅ Transaction verified successfully");
        } else {
          console.log("❌ Transaction verification failed:", verification);
          return NextResponse.json(
            { error: "Transaction verification failed" },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error("❌ Transaction verification error:", error);
        return NextResponse.json(
          { error: "Failed to verify transaction" },
          { status: 500 }
        );
      }
    }

    // Step 2: Find the Flutterwave subscription that was automatically created
    let flutterwaveSubscriptionId = null;
    try {
      console.log(
        "🔍 Looking for Flutterwave subscription for customer:",
        user.email
      );

      // Get all subscriptions and find the one for this customer
      const subscriptionsResponse = await fetch(
        "https://api.flutterwave.com/v3/subscriptions",
        {
          method: "GET",
          headers: {
            Authorization: `Bearer ${process.env.FLUTTERWAVE_SECRET_KEY}`,
            "Content-Type": "application/json",
          },
        }
      );

      const subscriptionsData = await subscriptionsResponse.json();

      if (subscriptionsData.status === "success" && subscriptionsData.data) {
        // Find subscription for this customer's email
        const customerSubscriptions = subscriptionsData.data.filter(
          (sub: any) => sub.customer?.email === user.email
        );

        // Get the most recent subscription
        if (customerSubscriptions.length > 0) {
          const latestSubscription = customerSubscriptions.sort(
            (a: any, b: any) =>
              new Date(b.created_at).getTime() -
              new Date(a.created_at).getTime()
          )[0];

          flutterwaveSubscriptionId = latestSubscription.id;
          console.log(
            "✅ Found Flutterwave subscription:",
            flutterwaveSubscriptionId
          );
        }
      }
    } catch (error) {
      console.error("❌ Error finding Flutterwave subscription:", error);
    }

    // Step 3: Activate the subscription in our database
    console.log("🔄 Activating subscription in database...");

    // Prepare metadata update
    const currentMetadata = (
      subscriptionData.metadata && typeof subscriptionData.metadata === "object"
        ? subscriptionData.metadata
        : {}
    ) as Record<string, any>;

    const metadataUpdate: Record<string, any> = {
      ...currentMetadata,
      activatedAt: new Date().toISOString(),
      transactionVerified,
      activationMethod: "secure_api_verification",
      flutterwaveSubscriptionId: flutterwaveSubscriptionId || null,
      transactionRef: transactionRef || null,
      verificationTimestamp: new Date().toISOString(),
    };

    // Update subscription to ACTIVE status
    const dataForUpdate: any = {
      status: SubscriptionStatus.ACTIVE,
      metadata: metadataUpdate,
    };

    // Apply pending plan changes if any
    if (subscriptionData.pendingPlanType) {
      dataForUpdate.planType = subscriptionData.pendingPlanType as PlanType;
      dataForUpdate.pendingPlanType = null;
    }
    if (subscriptionData.pendingPriceId) {
      dataForUpdate.priceId = subscriptionData.pendingPriceId;
      dataForUpdate.pendingPriceId = null;
    }
    if (subscriptionData.pendingPlanEffectiveDate) {
      dataForUpdate.pendingPlanEffectiveDate = null;
    }

    // Update subscription ID to use Flutterwave subscription ID if found
    if (flutterwaveSubscriptionId) {
      dataForUpdate.subscriptionId = flutterwaveSubscriptionId;
    } else if (
      transactionRef &&
      transactionRef !== subscriptionData.subscriptionId
    ) {
      dataForUpdate.subscriptionId = transactionRef;
    }

    // Update subscription in database
    const updatedSubscriptionResult = await db
      .update(subscriptionTable)
      .set(dataForUpdate)
      .where(eq(subscriptionTable.id, subscriptionData.id))
      .returning();

    const updatedSubscription = updatedSubscriptionResult[0];

    console.log("✅ Subscription activated successfully:", {
      id: updatedSubscription.id,
      status: updatedSubscription.status,
      planType: updatedSubscription.planType,
    });

    return NextResponse.json({
      success: true,
      message: "Subscription activated successfully",
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        planType: updatedSubscription.planType,
        billingCycle: updatedSubscription.billingCycle,
      },
    });
  } catch (error) {
    console.error("❌ Manual activation error:", error);
    return NextResponse.json(
      { error: "Failed to activate subscription" },
      { status: 500 }
    );
  }
}

// Local implementation removed - using centralized function from @/lib/usage-reset
