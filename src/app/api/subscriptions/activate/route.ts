import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { prisma } from "@/lib/prisma";
import { resetUsageRecordsInTransaction } from "@/lib/usage-reset";
import { activateSubscription, fetchSubscription } from "@/lib/paystack";
import { Prisma, SubscriptionStatus, PlanType } from "@prisma/client"; // Import Prisma namespace and enums

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { subscriptionId, paymentId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: "Subscription ID is required" },
        { status: 400 }
      );
    }

    // Find the subscription in our database
    const subscription = await prisma.subscription.findFirst({
      where: {
        subscriptionId: subscriptionId,
        userId: user.id,
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Check subscription status with payment provider
    let providerSubscription;
    try {
      const response = await fetchSubscription(subscriptionId) as any;
      providerSubscription = response?.data || null;
    } catch (error) {
      console.error("Error fetching subscription from Paystack:", error);
      // If we can't fetch from provider, we'll still activate manually
      providerSubscription = null;
    }

    // Determine the status based on provider response
    let providerStatusString: string | null = null;
    if (providerSubscription) {
      providerStatusString = providerSubscription.status;
    }

    let newDbStatus: SubscriptionStatus;
    switch (providerStatusString) {
      case "active":
        newDbStatus = SubscriptionStatus.ACTIVE;
        break;
      case "attention":
        newDbStatus = SubscriptionStatus.ATTENTION;
        break;
      case "cancelled":
        newDbStatus = SubscriptionStatus.CANCELLED;
        break;
      case "non-renewing":
        newDbStatus = SubscriptionStatus.NON_RENEWING;
        break;
      case "completed":
        newDbStatus = SubscriptionStatus.COMPLETED;
        break;
      default:
        newDbStatus = SubscriptionStatus.ACTIVE; // Default for manual activation or unhandled statuses
    }

    const dataForUpdate: Prisma.SubscriptionUpdateInput = {};
    dataForUpdate.status = newDbStatus;

    dataForUpdate.currentPeriodStart =
      providerSubscription?.previous_billing_date
        ? new Date(providerSubscription.previous_billing_date)
        : subscription.currentPeriodStart;
    dataForUpdate.currentPeriodEnd = providerSubscription?.next_billing_date
      ? new Date(providerSubscription.next_billing_date)
      : subscription.currentPeriodEnd;

    // Prepare metadata update
    const currentMetadata = (
      subscription.metadata && typeof subscription.metadata === "object"
        ? subscription.metadata
        : {}
    ) as Record<string, any>;
    const metadataUpdate: Record<string, any> = {
      ...currentMetadata,
      manuallyActivated: providerStatusString !== "active",
      paymentId: paymentId || null,
    };

    if (newDbStatus === SubscriptionStatus.ACTIVE) {
      dataForUpdate.planType = (subscription.pendingPlanType ||
        subscription.planType) as PlanType;
      if (subscription.pendingPriceId) {
        dataForUpdate.priceId = subscription.pendingPriceId;
      }
      if (
        subscription.pendingPlanType ||
        subscription.pendingPriceId ||
        subscription.pendingPlanEffectiveDate
      ) {
        dataForUpdate.pendingPlanType = null;
        dataForUpdate.pendingPriceId = null;
        dataForUpdate.pendingPlanEffectiveDate = null;
      }
      metadataUpdate.activatedAt = new Date().toISOString(); // Add activatedAt to metadata
    } else if (
      (
        [
          SubscriptionStatus.CANCELLED,
          SubscriptionStatus.ATTENTION, // Using ATTENTION instead of FAILED/EXPIRED/PAST_DUE
        ] as SubscriptionStatus[]
      ).includes(newDbStatus) // Type assertion for the array to ensure `includes` works with the enum type
    ) {
      if (
        subscription.pendingPlanType ||
        subscription.pendingPriceId ||
        subscription.pendingPlanEffectiveDate
      ) {
        dataForUpdate.pendingPlanType = null;
        dataForUpdate.pendingPriceId = null;
        dataForUpdate.pendingPlanEffectiveDate = null;
      }
    }
    dataForUpdate.metadata = metadataUpdate; // Assign the fully formed metadata object

    // Update subscription and reset usage records in a single transaction
    const updatedSubscription = await prisma.$transaction(async (tx) => {
      const updated = await tx.subscription.update({
        where: { id: subscription.id },
        data: dataForUpdate,
      });

      // If subscription is now active, create/reset usage records
      if (newDbStatus === "ACTIVE") {
        await resetUsageRecordsInTransaction(tx, subscription.id, user.id);
      }

      return updated;
    });

    return NextResponse.json({
      success: true,
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        planType: updatedSubscription.planType,
        billingCycle: updatedSubscription.billingCycle,
      },
    });
  } catch (error) {
    console.error("Manual activation error:", error);
    return NextResponse.json(
      { error: "Failed to activate subscription" },
      { status: 500 }
    );
  }
}

// Local implementation removed - using centralized function from @/lib/usage-reset
