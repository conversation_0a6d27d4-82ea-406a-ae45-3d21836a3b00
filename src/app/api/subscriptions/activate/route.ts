import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { resetUsageRecordsInTransaction } from "@/lib/usage-reset";
import { activateSubscription, fetchSubscription } from "@/lib/flutterwave";
import { eq, and } from "drizzle-orm";

// Define enum types for subscription status and plan type
type SubscriptionStatus =
  | "ACTIVE"
  | "CANCELLED"
  | "NON_RENEWING"
  | "ATTENTION"
  | "COMPLETED";
type PlanType = "STARTER" | "PRO" | "UNLIMITED";

const SubscriptionStatus = {
  ACTIVE: "ACTIVE" as const,
  CANCELLED: "CANCELLED" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  ATTENTION: "ATTENTION" as const,
  COMPLETED: "COMPLETED" as const,
};

const PlanType = {
  STARTER: "STARTER" as const,
  PRO: "PRO" as const,
  UNLIMITED: "UNLIMITED" as const,
};

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { subscriptionId, paymentId } = await request.json();

    if (!subscriptionId) {
      return NextResponse.json(
        { error: "Subscription ID is required" },
        { status: 400 }
      );
    }

    // Find the subscription in our database
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        and(
          eq(subscriptionTable.subscriptionId, subscriptionId),
          eq(subscriptionTable.userId, user.id)
        )
      )
      .limit(1);

    const subscriptionData = subscriptionResult[0];

    if (!subscriptionData) {
      return NextResponse.json(
        { error: "Subscription not found" },
        { status: 404 }
      );
    }

    // Check subscription status with Flutterwave
    let providerSubscription;
    try {
      const response = (await fetchSubscription(subscriptionId)) as any;
      providerSubscription = response?.data || null;
    } catch (error) {
      console.error("Error fetching subscription from Flutterwave:", error);
      // If we can't fetch from provider, we'll still activate manually
      providerSubscription = null;
    }

    // Determine the status based on provider response
    let providerStatusString: string | null = null;
    if (providerSubscription) {
      providerStatusString = providerSubscription.status;
    }

    let newDbStatus: SubscriptionStatus;
    switch (providerStatusString) {
      case "active":
        newDbStatus = SubscriptionStatus.ACTIVE;
        break;
      case "attention":
        newDbStatus = SubscriptionStatus.ATTENTION;
        break;
      case "cancelled":
        newDbStatus = SubscriptionStatus.CANCELLED;
        break;
      case "non-renewing":
        newDbStatus = SubscriptionStatus.NON_RENEWING;
        break;
      case "completed":
        newDbStatus = SubscriptionStatus.COMPLETED;
        break;
      default:
        newDbStatus = SubscriptionStatus.ACTIVE; // Default for manual activation or unhandled statuses
    }

    const dataForUpdate: any = {};
    dataForUpdate.status = newDbStatus;

    dataForUpdate.currentPeriodStart =
      providerSubscription?.previous_billing_date
        ? new Date(providerSubscription.previous_billing_date)
        : subscriptionData.currentPeriodStart;
    dataForUpdate.currentPeriodEnd = providerSubscription?.next_billing_date
      ? new Date(providerSubscription.next_billing_date)
      : subscriptionData.currentPeriodEnd;

    // Prepare metadata update
    const currentMetadata = (
      subscriptionData.metadata && typeof subscriptionData.metadata === "object"
        ? subscriptionData.metadata
        : {}
    ) as Record<string, any>;
    const metadataUpdate: Record<string, any> = {
      ...currentMetadata,
      manuallyActivated: providerStatusString !== "active",
      paymentId: paymentId || null,
    };

    if (newDbStatus === SubscriptionStatus.ACTIVE) {
      dataForUpdate.planType = (subscriptionData.pendingPlanType ||
        subscriptionData.planType) as PlanType;
      if (subscriptionData.pendingPriceId) {
        dataForUpdate.priceId = subscriptionData.pendingPriceId;
      }
      if (
        subscriptionData.pendingPlanType ||
        subscriptionData.pendingPriceId ||
        subscriptionData.pendingPlanEffectiveDate
      ) {
        dataForUpdate.pendingPlanType = null;
        dataForUpdate.pendingPriceId = null;
        dataForUpdate.pendingPlanEffectiveDate = null;
      }
      metadataUpdate.activatedAt = new Date().toISOString(); // Add activatedAt to metadata
    } else if (
      (
        [
          SubscriptionStatus.CANCELLED,
          SubscriptionStatus.ATTENTION, // Using ATTENTION instead of FAILED/EXPIRED/PAST_DUE
        ] as SubscriptionStatus[]
      ).includes(newDbStatus) // Type assertion for the array to ensure `includes` works with the enum type
    ) {
      if (
        subscriptionData.pendingPlanType ||
        subscriptionData.pendingPriceId ||
        subscriptionData.pendingPlanEffectiveDate
      ) {
        dataForUpdate.pendingPlanType = null;
        dataForUpdate.pendingPriceId = null;
        dataForUpdate.pendingPlanEffectiveDate = null;
      }
    }
    dataForUpdate.metadata = metadataUpdate; // Assign the fully formed metadata object

    // Update subscription in database
    const updatedSubscriptionResult = await db
      .update(subscriptionTable)
      .set(dataForUpdate)
      .where(eq(subscriptionTable.id, subscriptionData.id))
      .returning();

    const updatedSubscription = updatedSubscriptionResult[0];

    // If subscription is now active, reset usage records (simplified - no transaction for now)
    if (newDbStatus === "ACTIVE") {
      // Note: resetUsageRecordsInTransaction would need to be adapted for Drizzle
      // For now, we'll skip this step - it can be implemented later
      console.log("TODO: Reset usage records for activated subscription");
    }

    return NextResponse.json({
      success: true,
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        planType: updatedSubscription.planType,
        billingCycle: updatedSubscription.billingCycle,
      },
    });
  } catch (error) {
    console.error("Manual activation error:", error);
    return NextResponse.json(
      { error: "Failed to activate subscription" },
      { status: 500 }
    );
  }
}

// Local implementation removed - using centralized function from @/lib/usage-reset
