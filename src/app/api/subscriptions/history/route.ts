import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, subscription } from "@/lib/db";
import { eq, desc } from "drizzle-orm";

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscription history
    const subscriptions = await db
      .select({
        id: subscription.id,
        planType: subscription.planType,
        status: subscription.status,
        billingCycle: subscription.billingCycle,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        canceledAt: subscription.canceledAt,
        createdAt: subscription.createdAt,
        updatedAt: subscription.updatedAt,
      })
      .from(subscription)
      .where(eq(subscription.userId, user.id))
      .orderBy(desc(subscription.createdAt));

    return NextResponse.json({ subscriptions });
  } catch (error) {
    console.error("Billing history error:", error);
    return NextResponse.json(
      { error: "Failed to get billing history" },
      { status: 500 }
    );
  }
}
