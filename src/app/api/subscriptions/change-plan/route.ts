import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, subscription } from "@/lib/db";
import { createSubscription, cancelSubscription } from "@/lib/flutterwave";
import { getProductId } from "@/lib/subscription-config";
import { PlanType } from "@/data/pricingData";
import { eq, and, inArray } from "drizzle-orm";

// Define SubscriptionStatus locally since we're moving away from Prisma
const SubscriptionStatus = {
  ACTIVE: "ACTIVE" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  CANCELLED: "CANCELLED" as const,
  PENDING: "PENDING" as const,
  ATTENTION: "ATTENTION" as const,
  COMPLETED: "COMPLETED" as const,
};

const PLAN_TIERS: Record<PlanType, number> = {
  Free: 0,
  Starter: 1,
  Pro: 2,
  Unlimited: 3,
};

function isPlanUpgrade(currentPlan: PlanType, newPlan: PlanType): boolean {
  return PLAN_TIERS[newPlan] > PLAN_TIERS[currentPlan];
}

interface ChangePlanRequest {
  newPlanType: PlanType;
  newBillingCycle: "monthly" | "annual";
}

interface SubscriptionMetadata {
  billingCycle: string;
  lastPlanChange?: string;
  previousPlan?: string;
  planChangeType?: "upgrade" | "downgrade";
  metadata?: Record<string, string>;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { newPlanType, newBillingCycle }: ChangePlanRequest =
      await request.json();

    // Get current subscription
    const currentSubscriptionResult = await db
      .select()
      .from(subscription)
      .where(
        and(
          eq(subscription.userId, user.id),
          inArray(subscription.status, ["ACTIVE", "NON_RENEWING"])
        )
      )
      .limit(1);

    const currentSubscription = currentSubscriptionResult[0];

    if (!currentSubscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get new product ID
    const newProductId = getProductId(newPlanType, newBillingCycle);
    if (!newProductId) {
      return NextResponse.json(
        { error: "Invalid plan selected" },
        { status: 400 }
      );
    }

    const planChangeType = isPlanUpgrade(
      currentSubscription.planType,
      newPlanType
    )
      ? "upgrade"
      : "downgrade";

    const metadata: SubscriptionMetadata = {
      billingCycle: newBillingCycle,
      lastPlanChange: new Date().toISOString(),
      previousPlan: currentSubscription.planType,
      planChangeType,
    };

    console.log("🔄 Changing plan:", {
      subscriptionId: currentSubscription.subscriptionId,
      fromPlan: currentSubscription.planType,
      toPlan: newPlanType,
      newBillingCycle,
      changeType: planChangeType,
    });

    try {
      // Get user email for new subscription
      const user_email = user.email;
      if (!user_email) {
        return NextResponse.json(
          { error: "User email not found" },
          { status: 400 }
        );
      }

      // Step 1: Cancel current subscription but keep it active until period end
      console.log(
        "🚫 Cancelling current subscription with cancelAtPeriodEnd=true"
      );
      await cancelSubscription(currentSubscription.subscriptionId);

      // Step 2: Create new subscription checkout URL
      console.log("🔗 Creating checkout URL for new plan");
      const newSubscriptionResponse = await createSubscription(
        {
          email: user_email,
          name: user_email, // Use email as name fallback
        },
        newPlanType,
        newBillingCycle,
        "international" // Default to international, should be detected properly
      );

      if (
        newSubscriptionResponse?.status !== "success" ||
        !newSubscriptionResponse?.data?.link
      ) {
        throw new Error("Failed to create checkout URL");
      }

      // Step 3: Update subscription in database to track the plan change in progress
      const subscriptionUpdateResult = await db
        .update(subscription)
        .set({
          cancelAtPeriodEnd: true, // Mark as cancelled at period end
          pendingPlanType: newPlanType as any, // Store the new plan type
          pendingPriceId: newProductId, // Store the new price ID
          pendingPlanEffectiveDate: new Date(), // When the change was initiated
          metadata: {
            ...(currentSubscription.metadata as Record<string, unknown>),
            planChangeInitiated: new Date().toISOString(),
            previousPlan: currentSubscription.planType,
            planChangeType,
            checkoutUrl: newSubscriptionResponse.data.link,
            newSubscriptionReference: newSubscriptionResponse.data.tx_ref,
          },
        })
        .where(eq(subscription.id, currentSubscription.id))
        .returning();

      const subscriptionUpdate = subscriptionUpdateResult[0];

      console.log("✅ Plan change initiated:", {
        subscriptionId: currentSubscription.subscriptionId,
        fromPlan: currentSubscription.planType,
        toPlan: newPlanType,
        checkoutUrl: newSubscriptionResponse.data.link,
      });

      return NextResponse.json({
        success: true,
        message:
          "Plan change initiated. Complete checkout to activate your new plan.",
        checkoutUrl: newSubscriptionResponse.data.link,
        subscription: subscriptionUpdate,
        planChangeType,
      });
    } catch (error) {
      console.error("Plan change error:", error);
      return NextResponse.json(
        {
          error: "Failed to change plan",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Plan change error:", error);
    return NextResponse.json(
      {
        error: "Failed to process request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
