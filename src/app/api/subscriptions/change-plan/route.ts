import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { prisma } from "@/lib/prisma";
import { createSubscription, cancelSubscription } from "@/lib/paystack";
import { getProductId } from "@/lib/subscription-config";
import { PlanType } from "@/data/pricingData";
import { SubscriptionStatus } from "@prisma/client";

const PLAN_TIERS: Record<PlanType, number> = {
  Free: 0,
  Starter: 1,
  Pro: 2,
  Unlimited: 3,
};

function isPlanUpgrade(currentPlan: PlanType, newPlan: PlanType): boolean {
  return PLAN_TIERS[newPlan] > PLAN_TIERS[currentPlan];
}

interface ChangePlanRequest {
  newPlanType: PlanType;
  newBillingCycle: "monthly" | "annual";
}

interface SubscriptionMetadata {
  billingCycle: string;
  lastPlanChange?: string;
  previousPlan?: string;
  planChangeType?: "upgrade" | "downgrade";
  metadata?: Record<string, string>;
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { newPlanType, newBillingCycle }: ChangePlanRequest =
      await request.json();

    // Get current subscription
    const currentSubscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: { in: ["ACTIVE", "NON_RENEWING"] },
      },
    });

    if (!currentSubscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get new product ID
    const newProductId = getProductId(newPlanType, newBillingCycle);
    if (!newProductId) {
      return NextResponse.json(
        { error: "Invalid plan selected" },
        { status: 400 }
      );
    }

    const planChangeType = isPlanUpgrade(
      currentSubscription.planType,
      newPlanType
    )
      ? "upgrade"
      : "downgrade";

    const metadata: SubscriptionMetadata = {
      billingCycle: newBillingCycle,
      lastPlanChange: new Date().toISOString(),
      previousPlan: currentSubscription.planType,
      planChangeType,
    };

    console.log("🔄 Changing plan:", {
      subscriptionId: currentSubscription.subscriptionId,
      fromPlan: currentSubscription.planType,
      toPlan: newPlanType,
      newBillingCycle,
      changeType: planChangeType,
    });

    try {
      // Get user email for new subscription
      const user_email = user.email;
      if (!user_email) {
        return NextResponse.json(
          { error: "User email not found" },
          { status: 400 }
        );
      }

      // Step 1: Cancel current subscription but keep it active until period end
      console.log("🚫 Cancelling current subscription with cancelAtPeriodEnd=true");
      await cancelSubscription(
        currentSubscription.subscriptionId,
        currentSubscription.customerId || currentSubscription.subscriptionId
      );

      // Step 2: Create new subscription checkout URL
      console.log("🔗 Creating checkout URL for new plan");
      const newSubscriptionResponse = await createSubscription(
        user_email,
        newProductId
      ) as any;

      if (!newSubscriptionResponse?.status || !newSubscriptionResponse?.data?.authorization_url) {
        throw new Error("Failed to create checkout URL");
      }

      // Step 3: Update subscription in database to track the plan change in progress
      const subscriptionUpdate = await prisma.subscription.update({
        where: { id: currentSubscription.id },
        data: {
          cancelAtPeriodEnd: true, // Mark as cancelled at period end
          pendingPlanType: newPlanType, // Store the new plan type
          pendingPriceId: newProductId, // Store the new price ID
          pendingPlanEffectiveDate: new Date(), // When the change was initiated
          metadata: {
            ...(currentSubscription.metadata as Record<string, unknown>),
            planChangeInitiated: new Date().toISOString(),
            previousPlan: currentSubscription.planType,
            planChangeType,
            checkoutUrl: newSubscriptionResponse.data.authorization_url,
            newSubscriptionReference: newSubscriptionResponse.data.reference,
          },
        },
      });

      console.log("✅ Plan change initiated:", {
        subscriptionId: currentSubscription.subscriptionId,
        fromPlan: currentSubscription.planType,
        toPlan: newPlanType,
        checkoutUrl: newSubscriptionResponse.data.authorization_url,
      });

      return NextResponse.json({
        success: true,
        message: "Plan change initiated. Complete checkout to activate your new plan.",
        checkoutUrl: newSubscriptionResponse.data.authorization_url,
        subscription: subscriptionUpdate,
        planChangeType,
      });
    } catch (error) {
      console.error("Plan change error:", error);
      return NextResponse.json(
        {
          error: "Failed to change plan",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Plan change error:", error);
    return NextResponse.json(
      {
        error: "Failed to process request",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
