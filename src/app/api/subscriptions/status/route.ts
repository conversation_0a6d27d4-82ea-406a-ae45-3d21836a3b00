import { createClient } from "@/utils/supabase/server";
import { getUserSubscription } from "@/lib/subscription-config";
import { fetchSubscription, verifyTransaction } from "@/lib/flutterwave";
import { NextResponse } from "next/server";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { eq } from "drizzle-orm";

// Define SubscriptionStatus locally since we're moving away from Prisma
type SubscriptionStatus =
  | "ACTIVE"
  | "NON_RENEWING"
  | "CANCELLED"
  | "PENDING"
  | "ATTENTION"
  | "COMPLETED";

export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscription
    let subscription = await getUserSubscription(user.id);

    if (!subscription) {
      return NextResponse.json({
        planType: "Free",
        status: null,
      });
    }

    // Try to fetch subscription details from Flutterwave for real-time status
    let flutterwaveSubscription = null;

    try {
      flutterwaveSubscription = await fetchSubscription(
        subscription.subscriptionId
      );

      if (flutterwaveSubscription?.status === "success") {
        console.log("✅ Fetched subscription from Flutterwave:", {
          subscription_id: subscription.subscriptionId,
          status: flutterwaveSubscription.data?.status,
        });
      } else {
        console.warn("⚠️ No subscription data returned from Flutterwave");
      }
    } catch (fetchError) {
      console.warn(
        "⚠️ Flutterwave subscription fetch failed, using local data:",
        fetchError instanceof Error ? fetchError.message : String(fetchError)
      );
    }

    // If we have a valid Flutterwave subscription, update our local record if needed
    if (
      flutterwaveSubscription?.status === "success" &&
      flutterwaveSubscription?.data
    ) {
      const flutterwaveData = flutterwaveSubscription.data;
      const flutterwaveStatus = flutterwaveData.status;

      // Map Flutterwave status to our internal status
      let mappedStatus: SubscriptionStatus;
      switch (flutterwaveStatus) {
        case "active":
          mappedStatus = "ACTIVE";
          break;
        case "cancelled":
        case "canceled":
          mappedStatus = "CANCELLED";
          break;
        default:
          mappedStatus = subscription.status; // Keep existing status if unknown
      }

      // Update subscription if status has changed
      const updateData: Record<string, unknown> = {};

      if (mappedStatus !== subscription.status) {
        console.log(
          `🔄 Updating subscription status from ${subscription.status} to ${mappedStatus}`
        );
        updateData.status = mappedStatus;
        updateData.updatedAt = new Date();
      }

      // Apply updates if any
      if (Object.keys(updateData).length > 0) {
        const updatedSubscriptionResult = await db
          .update(subscriptionTable)
          .set(updateData)
          .where(eq(subscriptionTable.id, subscription.id))
          .returning();

        subscription = updatedSubscriptionResult[0];
      }

      return NextResponse.json({
        planType: subscription.planType,
        status: mappedStatus,
        billingCycle: subscription.billingCycle,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        flutterwaveStatus: flutterwaveStatus,
      });
    }

    // Return local subscription data if Flutterwave fetch failed
    return NextResponse.json({
      planType: subscription.planType,
      status: subscription.status,
      billingCycle: subscription.billingCycle,
      currentPeriodStart: subscription.currentPeriodStart,
      currentPeriodEnd: subscription.currentPeriodEnd,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
    });
  } catch (error) {
    console.error("Subscription status error:", error);
    return NextResponse.json(
      { error: "Failed to get subscription status" },
      { status: 500 }
    );
  }
}
