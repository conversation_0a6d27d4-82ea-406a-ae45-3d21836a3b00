
import { createClient } from "@/utils/supabase/server";
import { getUserSubscription } from "@/lib/subscription-config";
import { fetchSubscription, verifyTransaction } from "@/lib/paystack";
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { SubscriptionStatus } from "@prisma/client";


export async function GET() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's subscription
    let subscription = await getUserSubscription(user.id);

    if (!subscription) {
      return NextResponse.json({
        planType: "Free",
        status: null,
      });
    }

    // Fetch subscription details from Paystack for real-time status
    let paystackSubscription = null;
    let subscriptionCode = null;
    
    try {
      paystackSubscription = await fetchSubscription(subscription.subscriptionId);
      
      if (paystackSubscription?.data) {
        const subData = paystackSubscription.data;
        subscriptionCode = subData.subscription_code || subData.code;
        
        console.log("✅ Fetched subscription from Paystack:", {
          subscription_code: subscriptionCode || 'Not found',
          status: subData.status,
          next_payment_date: subData.next_payment_date
        });
      } else {
        console.warn("⚠️ No subscription data returned from Paystack");
      }
    } catch (fetchError) {
      console.warn("⚠️ Direct subscription fetch failed, checking if subscriptionId is a transaction reference:", fetchError instanceof Error ? fetchError.message : String(fetchError));
      
      // If direct fetch fails, try to get subscription details via transaction verification
      try {
        const transactionDetails = await verifyTransaction(subscription.subscriptionId);
        if (transactionDetails?.data?.subscription) {
          const subData = transactionDetails.data.subscription;
          subscriptionCode = subData.subscription_code || subData.code;
          
          // Create a mock paystack subscription object for consistency
          paystackSubscription = {
            status: true,
            data: subData
          };
          
          console.log("✅ Found subscription via transaction verification:", {
            subscription_code: subscriptionCode || 'Not found',
            status: subData.status
          });
        } else {
          console.log("⚠️ No subscription found in transaction details, using local data only");
        }
      } catch (transactionError) {
        console.error("❌ Transaction verification also failed:", transactionError instanceof Error ? transactionError.message : String(transactionError));
        console.log("⚠️ Using local subscription data only");
      }
    }

    // If we have a valid Paystack subscription, update our local record if needed
    if (paystackSubscription?.data) {
      const paystackData = paystackSubscription.data;
      const paystackStatus = paystackData.status;
      
      // Map Paystack status to our internal status
      let mappedStatus: SubscriptionStatus;
      switch (paystackStatus) {
        case 'active':
          mappedStatus = 'ACTIVE';
          break;
        case 'non-renewing':
          mappedStatus = 'NON_RENEWING';
          break;
        case 'cancelled':
        case 'canceled':
          mappedStatus = 'CANCELLED';
          break;
        default:
          mappedStatus = subscription.status; // Keep existing status if unknown
      }
      
      // Update subscription if status has changed or if we have a better subscription code
      const updateData: any = {};
      
      if (mappedStatus !== subscription.status) {
        console.log(`🔄 Updating subscription status from ${subscription.status} to ${mappedStatus}`);
        updateData.status = mappedStatus;
        updateData.updatedAt = new Date();
      }
      
      // Update subscriptionId if we found a better subscription_code
      if (subscriptionCode && subscriptionCode !== subscription.subscriptionId && !subscription.subscriptionId.startsWith('temp_')) {
        console.log(`🔄 Updating subscriptionId from ${subscription.subscriptionId} to ${subscriptionCode}`);
        updateData.subscriptionId = subscriptionCode;
      }
      
      // Apply updates if any
      if (Object.keys(updateData).length > 0) {
        subscription = await prisma.subscription.update({
          where: { id: subscription.id },
          data: updateData
        });
      }
      
      // Extract next payment date
      const nextPaymentDate = paystackData.next_payment_date ? new Date(paystackData.next_payment_date) : null;
      
      return NextResponse.json({
        planType: subscription.planType,
        status: mappedStatus,
        billingCycle: subscription.billingCycle,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd,
        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
        nextPaymentDate,
        paystackStatus: paystackStatus
      });
    }

    // Return local subscription data if Paystack fetch failed
    return NextResponse.json({
      planType: subscription.planType,
      status: subscription.status,
      billingCycle: subscription.billingCycle,
      currentPeriodStart: subscription.currentPeriodStart,
      currentPeriodEnd: subscription.currentPeriodEnd,
      cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,
    });
  } catch (error) {
    console.error("Subscription status error:", error);
    return NextResponse.json(
      { error: "Failed to get subscription status" },
      { status: 500 }
    );
  }
}
