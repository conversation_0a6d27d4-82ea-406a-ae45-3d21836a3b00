import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { cancelSubscription, fetchSubscription } from "@/lib/flutterwave";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { eq, and, inArray } from "drizzle-orm";

// Define SubscriptionStatus locally since we're moving away from Prisma
const SubscriptionStatus = {
  ACTIVE: "ACTIVE" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  CANCELLED: "CANCELLED" as const,
  ATTENTION: "ATTENTION" as const,
};

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { cancelAtPeriodEnd = true } = await request.json();

    // Get user's active subscription
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        and(
          eq(subscriptionTable.userId, user.id),
          inArray(subscriptionTable.status, [
            "ACTIVE",
            "ATTENTION",
            "NON_RENEWING",
          ])
        )
      )
      .limit(1);

    const subscription = subscriptionResult[0];

    if (!subscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Check if subscription is already scheduled for cancellation
    if (subscription.cancelAtPeriodEnd) {
      return NextResponse.json(
        { error: "Subscription is already scheduled for cancellation" },
        { status: 400 }
      );
    }

    console.log("🚫 Cancelling subscription:", {
      subscriptionId: subscription.subscriptionId,
      cancelAtPeriodEnd,
      userId: user.id,
    });

    try {
      // Fetch subscription details from Flutterwave
      console.log("🔍 Fetching subscription details from Flutterwave...");
      console.log("📋 Using subscriptionId:", subscription.subscriptionId);

      let subscriptionDetails;
      try {
        subscriptionDetails = await fetchSubscription(
          subscription.subscriptionId
        );
      } catch (fetchError) {
        console.warn(
          "⚠️ Failed to fetch subscription from Flutterwave:",
          fetchError instanceof Error ? fetchError.message : String(fetchError)
        );

        // For Flutterwave, we'll proceed with local cancellation if remote fetch fails
        subscriptionDetails = null;
      }

      if (subscriptionDetails) {
        console.log(
          "📋 Flutterwave subscription details:",
          JSON.stringify(subscriptionDetails, null, 2)
        );

        // Cancel subscription with Flutterwave
        const cancelledSubscription = await cancelSubscription(
          subscription.subscriptionId
        );

        console.log("✅ Subscription cancellation response:", {
          subscriptionId: subscription.subscriptionId,
          status: cancelledSubscription?.status,
          message: cancelledSubscription?.message,
        });

        // Check if cancellation was successful
        if (cancelledSubscription?.status !== "success") {
          throw new Error(
            cancelledSubscription?.message ||
              "Failed to cancel subscription with Flutterwave"
          );
        }
      } else {
        console.log(
          "⚠️ Proceeding with local cancellation only (no remote subscription found)"
        );
      }
    } catch (providerError) {
      console.error("❌ Error cancelling with Flutterwave:", providerError);

      // If provider cancellation fails, we still update our database
      // This ensures the user sees the cancellation in our UI
      console.log(
        "⚠️ Proceeding with local cancellation due to provider error"
      );
    }

    // Update subscription in our database
    const updatedSubscriptionResult = await db
      .update(subscriptionTable)
      .set({
        status: cancelAtPeriodEnd
          ? SubscriptionStatus.NON_RENEWING
          : SubscriptionStatus.CANCELLED, // NON_RENEWING for cancel at period end
        cancelAtPeriodEnd: cancelAtPeriodEnd,
        canceledAt: cancelAtPeriodEnd ? null : new Date(),
        metadata: {
          ...(subscription.metadata && typeof subscription.metadata === "object"
            ? subscription.metadata
            : {}),
          cancelAtPeriodEnd,
          cancelledBy: "user",
          cancelledAt: new Date().toISOString(),
        },
      })
      .where(eq(subscriptionTable.id, subscription.id))
      .returning();

    const updatedSubscription = updatedSubscriptionResult[0];

    console.log("✅ Subscription updated in database:", {
      id: updatedSubscription.id,
      status: updatedSubscription.status,
      cancelAtPeriodEnd: updatedSubscription.cancelAtPeriodEnd,
    });

    return NextResponse.json({
      success: true,
      message: cancelAtPeriodEnd
        ? "Subscription will be cancelled at the end of the current billing period"
        : "Subscription has been cancelled immediately",
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        cancelAtPeriodEnd: updatedSubscription.cancelAtPeriodEnd,
        canceledAt: updatedSubscription.canceledAt,
        currentPeriodEnd: updatedSubscription.currentPeriodEnd,
      },
    });
  } catch (error) {
    console.error("❌ Subscription cancellation error:", error);
    return NextResponse.json(
      {
        error: "Failed to cancel subscription",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
