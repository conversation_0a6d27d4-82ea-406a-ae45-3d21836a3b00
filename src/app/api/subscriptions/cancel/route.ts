import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import {
  cancelSubscription,
  fetchSubscription,
  verifyTransaction,
} from "@/lib/paystack";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { eq, and, inArray } from "drizzle-orm";

// Define SubscriptionStatus locally since we're moving away from Prisma
const SubscriptionStatus = {
  ACTIVE: "ACTIVE" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  CANCELLED: "CANCELLED" as const,
  ATTENTION: "ATTENTION" as const,
};

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { cancelAtPeriodEnd = true } = await request.json();

    // Get user's active subscription
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        and(
          eq(subscriptionTable.userId, user.id),
          inArray(subscriptionTable.status, [
            "ACTIVE",
            "ATTENTION",
            "NON_RENEWING",
          ])
        )
      )
      .limit(1);

    const subscription = subscriptionResult[0];

    if (!subscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Check if subscription is already scheduled for cancellation
    if (subscription.cancelAtPeriodEnd) {
      return NextResponse.json(
        { error: "Subscription is already scheduled for cancellation" },
        { status: 400 }
      );
    }

    console.log("🚫 Cancelling subscription:", {
      subscriptionId: subscription.subscriptionId,
      cancelAtPeriodEnd,
      userId: user.id,
    });

    try {
      // First, fetch subscription details from Paystack to get the subscription code
      console.log("🔍 Fetching subscription details from Paystack...");
      console.log("📋 Using subscriptionId:", subscription.subscriptionId);

      let subscriptionDetails;
      try {
        subscriptionDetails = await fetchSubscription(
          subscription.subscriptionId
        );
      } catch (fetchError) {
        console.warn(
          "⚠️ Direct subscription fetch failed, checking if subscriptionId is a transaction reference:",
          fetchError instanceof Error ? fetchError.message : String(fetchError)
        );

        // If direct fetch fails, try to get subscription details via transaction verification
        // This handles cases where subscriptionId is actually a transaction reference
        try {
          const transactionDetails = await verifyTransaction(
            subscription.subscriptionId
          );
          if (
            transactionDetails?.status &&
            transactionDetails?.data?.subscription
          ) {
            console.log("✅ Found subscription via transaction verification");
            // Extract subscription details from transaction
            const subData = transactionDetails.data.subscription;
            subscriptionDetails = {
              status: true,
              data: {
                subscription_code: subData.subscription_code || subData.code,
                email_token: subData.email_token,
                status: subData.status,
                ...subData,
              },
            };
          } else {
            throw new Error("No subscription found in transaction details");
          }
        } catch (transactionError) {
          console.error(
            "❌ Transaction verification also failed:",
            transactionError instanceof Error
              ? transactionError.message
              : String(transactionError)
          );
          throw new Error(
            `Failed to fetch subscription details: ${
              fetchError instanceof Error
                ? fetchError.message
                : String(fetchError)
            }`
          );
        }
      }

      console.log(
        "📋 Raw Paystack response:",
        JSON.stringify(subscriptionDetails, null, 2)
      );

      if (!subscriptionDetails?.status || !subscriptionDetails?.data) {
        console.error("❌ Invalid Paystack response structure:", {
          hasStatus: !!subscriptionDetails?.status,
          hasData: !!subscriptionDetails?.data,
          response: subscriptionDetails,
        });
        throw new Error("Failed to fetch subscription details from Paystack");
      }

      console.log(
        "📋 Paystack subscription details:",
        JSON.stringify(subscriptionDetails.data, null, 2)
      );

      // Get the subscription code from Paystack response
      const subscriptionCode =
        subscriptionDetails.data.subscription_code ||
        subscriptionDetails.data.code;
      if (!subscriptionCode) {
        throw new Error("No subscription code found in Paystack response");
      }

      // Get email token from the subscription details or use stored one
      let emailToken =
        subscriptionDetails.data.email_token || subscription.emailToken;

      if (!emailToken) {
        console.warn(
          "⚠️ No email token found, attempting to use customer email or fallback"
        );
        // Try to use customer email as fallback
        emailToken =
          subscriptionDetails.data.customer?.email ||
          subscription.customerId ||
          subscriptionCode;
      }

      // Ensure emailToken is never null
      const finalEmailToken = emailToken || subscriptionCode;

      console.log("🔑 Using subscription code:", subscriptionCode);
      console.log(
        "📧 Using email token:",
        finalEmailToken ? "[PRESENT]" : "[MISSING]"
      );

      // Cancel subscription with Paystack using the correct code and token
      const cancelledSubscription = (await cancelSubscription(
        subscriptionCode,
        finalEmailToken
      )) as any;

      console.log("✅ Subscription cancellation response:", {
        subscription_code: subscriptionCode,
        status: cancelledSubscription?.status,
        message: cancelledSubscription?.message,
      });

      // Verify cancellation by fetching fresh subscription status
      const freshSubscription = await fetchSubscription(
        subscription.subscriptionId
      );
      console.log(
        "🔍 Paystack status after disable:",
        freshSubscription?.data?.status
      );

      // Check if cancellation was successful
      if (!cancelledSubscription?.status) {
        throw new Error(
          cancelledSubscription?.message ||
            "Failed to cancel subscription with Paystack"
        );
      }
    } catch (providerError) {
      console.error("❌ Error cancelling with Paystack:", providerError);

      // If provider cancellation fails, we still update our database
      // This ensures the user sees the cancellation in our UI
      console.log(
        "⚠️ Proceeding with local cancellation due to provider error"
      );
    }

    // Update subscription in our database
    const updatedSubscriptionResult = await db
      .update(subscriptionTable)
      .set({
        status: cancelAtPeriodEnd
          ? SubscriptionStatus.NON_RENEWING
          : SubscriptionStatus.CANCELLED, // NON_RENEWING for cancel at period end
        cancelAtPeriodEnd: cancelAtPeriodEnd,
        canceledAt: cancelAtPeriodEnd ? null : new Date(),
        metadata: {
          ...(subscription.metadata && typeof subscription.metadata === "object"
            ? subscription.metadata
            : {}),
          cancelAtPeriodEnd,
          cancelledBy: "user",
          cancelledAt: new Date().toISOString(),
        },
      })
      .where(eq(subscriptionTable.id, subscription.id))
      .returning();

    const updatedSubscription = updatedSubscriptionResult[0];

    console.log("✅ Subscription updated in database:", {
      id: updatedSubscription.id,
      status: updatedSubscription.status,
      cancelAtPeriodEnd: updatedSubscription.cancelAtPeriodEnd,
    });

    return NextResponse.json({
      success: true,
      message: cancelAtPeriodEnd
        ? "Subscription will be cancelled at the end of the current billing period"
        : "Subscription has been cancelled immediately",
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        cancelAtPeriodEnd: updatedSubscription.cancelAtPeriodEnd,
        canceledAt: updatedSubscription.canceledAt,
        currentPeriodEnd: updatedSubscription.currentPeriodEnd,
      },
    });
  } catch (error) {
    console.error("❌ Subscription cancellation error:", error);
    return NextResponse.json(
      {
        error: "Failed to cancel subscription",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
