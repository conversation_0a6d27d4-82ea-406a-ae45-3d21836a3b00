import { createClient } from "@/utils/supabase/server";
import { verifyTransaction } from "@/lib/flutterwave";
import {
  db,
  subscription as subscriptionTable,
  usageRecord,
  usageAuditLog,
} from "@/lib/db";
import { eq, and, or, inArray, isNull, isNotNull } from "drizzle-orm";
import { nanoid } from "nanoid";
import { NextRequest, NextResponse } from "next/server";

/**
 * Callback route for handling Flutterwave transaction verification.
 *
 * This route handles:
 * 1. Payment completion redirects from Flutterwave
 * 2. Transaction verification and subscription activation
 * 3. Plan change processing
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const txRef = searchParams.get("tx_ref");
    const transactionId = searchParams.get("transaction_id");
    const status = searchParams.get("status");

    // Use tx_ref (Flutterwave's transaction reference)
    const transactionRef = txRef || transactionId;

    if (!transactionRef) {
      return NextResponse.redirect(
        new URL("/upgrade?error=missing_reference", request.url)
      );
    }

    console.log("🔍 Verifying transaction:", transactionRef);

    // Verify transaction with Flutterwave
    const verification = await verifyTransaction(transactionRef);

    console.log("✅ Transaction verification result:", {
      status: verification.status,
      data_status: verification.data?.status,
      amount: verification.data?.amount,
      reference: verification.data?.reference,
    });

    if (
      verification.status !== "success" ||
      verification.data?.status !== "successful"
    ) {
      console.log("❌ Transaction verification failed");
      return NextResponse.redirect(
        new URL("/upgrade?error=payment_failed", request.url)
      );
    }

    // Find the pending subscription by transaction reference
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        or(
          eq(subscriptionTable.subscriptionId, transactionRef),
          // Also check for pending subscriptions with matching metadata
          and(
            eq(subscriptionTable.status, "PENDING"),
            isNotNull(subscriptionTable.metadata)
          )
        )
      )
      .limit(1);

    const subscription = subscriptionResult[0];

    // If no pending subscription found, check for plan change in progress
    if (!subscription) {
      // TODO: Implement JSON metadata search with Drizzle
      // For now, we'll skip the complex JSON path search
      const planChangeSubscriptionResult = await db
        .select()
        .from(subscriptionTable)
        .where(
          and(
            eq(subscriptionTable.cancelAtPeriodEnd, true),
            isNotNull(subscriptionTable.pendingPlanType)
          )
        )
        .limit(1);

      const planChangeSubscription = planChangeSubscriptionResult[0];

      // If this is a plan change, redirect to success immediately
      // Let the webhook handle the actual processing to avoid race conditions
      if (planChangeSubscription) {
        console.log(
          "🔄 Plan change detected, redirecting to success (webhook will process)"
        );
        return NextResponse.redirect(
          new URL("/subscription/success", request.url)
        );
      }
    }

    if (!subscription) {
      console.log(
        "❌ No pending subscription or plan change found for reference:",
        transactionRef
      );
      return NextResponse.redirect(
        new URL("/upgrade?error=subscription_not_found", request.url)
      );
    }

    let updatedSubscription;

    // Check if this is a plan change (has pendingPlanType)
    if (subscription.pendingPlanType && subscription.cancelAtPeriodEnd) {
      console.log("🔄 Processing plan change completion");

      // For Flutterwave, use the transaction reference as subscription ID
      const flutterwaveRef = verification.data?.flw_ref || transactionRef;

      console.log("✅ Using Flutterwave reference:", flutterwaveRef);

      // This is a plan change - activate the new plan
      const updatedSubscriptionResult = await db
        .update(subscriptionTable)
        .set({
          subscriptionId: flutterwaveRef,
          status: "ACTIVE",
          planType: subscription.pendingPlanType, // Activate the pending plan
          priceId: subscription.pendingPriceId || subscription.priceId,
          cancelAtPeriodEnd: false, // No longer cancelled
          canceledAt: null,
          pendingPlanType: null, // Clear pending fields
          pendingPriceId: null,
          pendingPlanEffectiveDate: null,
          metadata: {
            ...(subscription.metadata as Record<string, unknown>),
            paymentVerified: true,
            verificationData: verification.data,
            planChangeCompleted: new Date().toISOString(),
            flutterwaveTransactionId: verification.data?.id,
            flutterwaveReference: flutterwaveRef,
            previousSubscriptionId: subscription.subscriptionId,
          },
        })
        .where(eq(subscriptionTable.id, subscription.id))
        .returning();

      const updatedSubscription = updatedSubscriptionResult[0];

      // Reset usage for plan changes
      // TODO: Implement Drizzle transaction for usage reset
      // For now, we'll do the operations separately
      await db
        .delete(usageRecord)
        .where(eq(usageRecord.userId, subscription.userId));

      await db.insert(usageAuditLog).values({
        id: nanoid(),
        usageRecordId: subscription.id,
        userId: subscription.userId,
        feature: "all",
        action: "plan_change_completed",
        previousCount: 0,
        newCount: 0,
        metadata: {
          type: "plan_change",
          newPlan: subscription.pendingPlanType,
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date(),
      });

      console.log("✅ Plan change completed:", {
        id: updatedSubscription.id,
        newPlan: updatedSubscription.planType,
        newSubscriptionId: transactionRef,
      });
    } else {
      console.log("🆕 Processing new subscription activation");

      // Extract subscription_code from Paystack API response
      let emailToken = null;
      let subscriptionCode = null;

      // Check if the verification data contains subscription information
      if (verification.data?.subscription) {
        subscriptionCode = verification.data.subscription.subscription_code;
        emailToken = verification.data.subscription.email_token;
        console.log(
          "✅ Found subscription details in transaction verification:",
          {
            subscriptionCode: subscriptionCode ? "Found" : "Not found",
            emailToken: emailToken ? "Found" : "Not found",
          }
        );
      } else {
        console.log(
          "⚠️ No subscription details found in transaction verification"
        );
      }

      // This is a new subscription - just activate it
      const updatedSubscriptionResult2 = await db
        .update(subscriptionTable)
        .set({
          subscriptionId: subscriptionCode || transactionRef, // Use subscription_code if available, fallback to reference
          status: "ACTIVE",
          emailToken: emailToken,
          metadata: {
            ...(subscription.metadata as any),
            paymentVerified: true,
            verificationData: verification.data,
            activatedAt: new Date().toISOString(),
          },
        })
        .where(eq(subscriptionTable.id, subscription.id))
        .returning();

      const updatedSubscription = updatedSubscriptionResult2[0];

      console.log("✅ New subscription activated:", {
        id: updatedSubscription.id,
        planType: updatedSubscription.planType,
        status: updatedSubscription.status,
      });
    }

    console.log("✅ Subscription activated:", {
      id: updatedSubscription.id,
      planType: updatedSubscription.planType,
      status: updatedSubscription.status,
    });

    // Redirect to success page
    return NextResponse.redirect(new URL("/subscription/success", request.url));
  } catch (error) {
    console.error("❌ Callback error:", error);
    return NextResponse.redirect(
      new URL("/upgrade?error=callback_error", request.url)
    );
  }
}

// Handle POST requests (webhooks)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("📨 Webhook received:", body.event);

    // Handle different webhook events
    switch (body.event) {
      case "charge.success":
        // Handle successful charge
        const reference = body.data?.reference;
        if (reference) {
          // Find pending subscription or plan change
          let subscriptionResult = await db
            .select()
            .from(subscriptionTable)
            .where(
              and(
                eq(subscriptionTable.subscriptionId, reference),
                eq(subscriptionTable.status, "PENDING")
              )
            )
            .limit(1);

          let subscription = subscriptionResult[0];

          // Check for plan change
          if (!subscription) {
            // TODO: Implement JSON metadata search with Drizzle
            const planChangeResult = await db
              .select()
              .from(subscriptionTable)
              .where(
                and(
                  eq(subscriptionTable.cancelAtPeriodEnd, true),
                  isNotNull(subscriptionTable.pendingPlanType)
                )
              )
              .limit(1);

            subscription = planChangeResult[0];
          }

          if (subscription) {
            // Extract subscription_code from webhook data
            let emailToken = null;
            let subscriptionCode = null;

            // Check if webhook data contains subscription information
            if (body.data?.subscription) {
              subscriptionCode = body.data.subscription.subscription_code;
              emailToken = body.data.subscription.email_token;
              console.log("✅ Found subscription details in webhook data:", {
                subscriptionCode: subscriptionCode ? "Found" : "Not found",
                emailToken: emailToken ? "Found" : "Not found",
              });
            } else {
              console.log("⚠️ No subscription details found in webhook data");
            }

            if (
              subscription.pendingPlanType &&
              subscription.cancelAtPeriodEnd
            ) {
              // Handle plan change
              await db
                .update(subscriptionTable)
                .set({
                  subscriptionId: subscriptionCode || reference, // Store actual subscription_code from Paystack
                  status: "ACTIVE",
                  emailToken: emailToken,
                  planType: subscription.pendingPlanType,
                  priceId: subscription.pendingPriceId || subscription.priceId,
                  cancelAtPeriodEnd: false,
                  canceledAt: null,
                  pendingPlanType: null,
                  pendingPriceId: null,
                  pendingPlanEffectiveDate: null,
                  metadata: {
                    ...(subscription.metadata as any),
                    webhookProcessed: true,
                    webhookData: body.data,
                    planChangeCompleted: new Date().toISOString(),
                  },
                })
                .where(eq(subscriptionTable.id, subscription.id));
              console.log(
                "✅ Plan change completed via webhook:",
                subscription.id
              );
            } else {
              // Handle new subscription
              await db
                .update(subscriptionTable)
                .set({
                  subscriptionId: subscriptionCode || reference, // Store actual subscription_code from Paystack
                  status: "ACTIVE",
                  emailToken: emailToken,
                  metadata: {
                    ...(subscription.metadata as any),
                    webhookProcessed: true,
                    webhookData: body.data,
                  },
                })
                .where(eq(subscriptionTable.id, subscription.id));
              console.log(
                "✅ Subscription activated via webhook:",
                subscription.id
              );
            }
          }
        }
        break;

      case "subscription.create":
        console.log("📝 Subscription created webhook received");
        break;

      default:
        console.log("ℹ️ Unhandled webhook event:", body.event);
    }

    return NextResponse.json({ status: "success" });
  } catch (error) {
    console.error("❌ Webhook error:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}
