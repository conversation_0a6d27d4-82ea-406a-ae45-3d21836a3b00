import { createClient } from "@/utils/supabase/server";
import { verifyTransaction, fetchSubscription } from "@/lib/paystack";
import { prisma } from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";

/**
 * Callback route for handling Paystack transaction verification.
 * 
 * Note: With the new direct subscription creation approach, this route is primarily used for:
 * 1. Legacy transaction-based subscription flows (fallback)
 * 2. Plan change transactions
 * 3. Webhook processing
 * 
 * New subscriptions created via the direct API approach are activated immediately
 * and don't require callback processing.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const reference = searchParams.get('reference');
    const trxref = searchParams.get('trxref');
    
    // Use reference or trxref (Paystack sends both)
    const transactionRef = reference || trxref;
    
    if (!transactionRef) {
      return NextResponse.redirect(
        new URL('/upgrade?error=missing_reference', request.url)
      );
    }

    console.log('🔍 Verifying transaction:', transactionRef);

    // Verify transaction with Paystack
    const verification = await verifyTransaction(transactionRef) as any;
    
    console.log('✅ Transaction verification result:', {
      status: verification.status,
      data_status: verification.data?.status,
      amount: verification.data?.amount,
      reference: verification.data?.reference
    });

    if (!verification.status || verification.data?.status !== 'success') {
      console.log('❌ Transaction verification failed');
      return NextResponse.redirect(
        new URL('/upgrade?error=payment_failed', request.url)
      );
    }

    // Find the pending subscription by transaction reference in metadata
    const subscription = await prisma.subscription.findFirst({
      where: {
        OR: [
          {
            metadata: {
              path: ['transactionRef'],
              equals: transactionRef
            },
            status: 'PENDING'
          },
          {
            subscriptionId: transactionRef,
            status: 'PENDING'
          }
        ]
      }
    });

    // If no pending subscription found, check for plan change in progress
    if (!subscription) {
      const planChangeSubscription = await prisma.subscription.findFirst({
        where: {
          metadata: {
            path: ['newSubscriptionReference'],
            equals: transactionRef
          },
          cancelAtPeriodEnd: true,
          pendingPlanType: { not: null }
        }
      });
      
      // If this is a plan change, redirect to success immediately
      // Let the webhook handle the actual processing to avoid race conditions
      if (planChangeSubscription) {
        console.log('🔄 Plan change detected, redirecting to success (webhook will process)');
        return NextResponse.redirect(
          new URL('/subscription/success', request.url)
        );
      }
    }

    if (!subscription) {
      console.log('❌ No pending subscription or plan change found for reference:', transactionRef);
      return NextResponse.redirect(
        new URL('/upgrade?error=subscription_not_found', request.url)
      );
    }

    let updatedSubscription;
    
    // Check if this is a plan change (has pendingPlanType)
    if (subscription.pendingPlanType && subscription.cancelAtPeriodEnd) {
      console.log('🔄 Processing plan change completion');
      
      // Extract subscription_code from Paystack API response
      let emailToken = null;
      let subscriptionCode = null;
      
      // Check if the verification data contains subscription information
      if (verification.data?.subscription) {
        subscriptionCode = verification.data.subscription.subscription_code;
        emailToken = verification.data.subscription.email_token;
        console.log('✅ Found subscription details in transaction verification:', {
          subscriptionCode: subscriptionCode ? 'Found' : 'Not found',
          emailToken: emailToken ? 'Found' : 'Not found'
        });
      } else {
        console.log('⚠️ No subscription details found in transaction verification');
      }
      
      // This is a plan change - activate the new plan
      updatedSubscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          subscriptionId: subscriptionCode || transactionRef, // Store actual subscription_code from Paystack
          status: 'ACTIVE',
          emailToken: emailToken, // Store the new email token
          planType: subscription.pendingPlanType, // Activate the pending plan
          priceId: subscription.pendingPriceId || subscription.priceId,
          cancelAtPeriodEnd: false, // No longer cancelled
          canceledAt: null,
          pendingPlanType: null, // Clear pending fields
          pendingPriceId: null,
          pendingPlanEffectiveDate: null,
          metadata: {
            ...subscription.metadata as any,
            paymentVerified: true,
            verificationData: verification.data,
            planChangeCompleted: new Date().toISOString(),
            previousSubscriptionId: subscription.subscriptionId // Keep track of old subscription ID
          }
        }
      });
      
      // Reset usage for plan changes
      await prisma.$transaction([
        // Delete existing usage records
        prisma.usageRecord.deleteMany({
          where: { userId: subscription.userId },
        }),
        // Create audit log for the reset
        prisma.usageAuditLog.create({
          data: {
            usageRecordId: subscription.id,
            userId: subscription.userId,
            feature: "all",
            action: "plan_change_completed",
            previousCount: 0,
            newCount: 0,
            metadata: {
              type: "plan_change",
              newPlan: subscription.pendingPlanType,
              timestamp: new Date().toISOString(),
            },
          },
        }),
      ]);
      
      console.log('✅ Plan change completed:', {
        id: updatedSubscription.id,
        newPlan: updatedSubscription.planType,
        newSubscriptionId: transactionRef
      });
    } else {
      console.log('🆕 Processing new subscription activation');
      
      // Extract subscription_code from Paystack API response
      let emailToken = null;
      let subscriptionCode = null;
      
      // Check if the verification data contains subscription information
      if (verification.data?.subscription) {
        subscriptionCode = verification.data.subscription.subscription_code;
        emailToken = verification.data.subscription.email_token;
        console.log('✅ Found subscription details in transaction verification:', {
          subscriptionCode: subscriptionCode ? 'Found' : 'Not found',
          emailToken: emailToken ? 'Found' : 'Not found'
        });
      } else {
        console.log('⚠️ No subscription details found in transaction verification');
      }
      
      // This is a new subscription - just activate it
      updatedSubscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          subscriptionId: subscriptionCode || transactionRef, // Use subscription_code if available, fallback to reference
          status: 'ACTIVE',
          emailToken: emailToken,
          metadata: {
            ...subscription.metadata as any,
            paymentVerified: true,
            verificationData: verification.data,
            activatedAt: new Date().toISOString()
          }
        }
      });
      
      console.log('✅ New subscription activated:', {
        id: updatedSubscription.id,
        planType: updatedSubscription.planType,
        status: updatedSubscription.status
      });
    }

    console.log('✅ Subscription activated:', {
      id: updatedSubscription.id,
      planType: updatedSubscription.planType,
      status: updatedSubscription.status
    });

    // Redirect to success page
    return NextResponse.redirect(
      new URL('/subscription/success', request.url)
    );

  } catch (error) {
    console.error('❌ Callback error:', error);
    return NextResponse.redirect(
      new URL('/upgrade?error=callback_error', request.url)
    );
  }
}

// Handle POST requests (webhooks)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('📨 Webhook received:', body.event);
    
    // Handle different webhook events
    switch (body.event) {
      case 'charge.success':
        // Handle successful charge
        const reference = body.data?.reference;
        if (reference) {
          // Find pending subscription or plan change
          let subscription = await prisma.subscription.findFirst({
            where: {
              subscriptionId: reference,
              status: 'PENDING'
            }
          });
          
          // Check for plan change
          if (!subscription) {
            subscription = await prisma.subscription.findFirst({
              where: {
                metadata: {
                  path: ['newSubscriptionReference'],
                  equals: reference
                },
                cancelAtPeriodEnd: true,
                pendingPlanType: { not: null }
              }
            });
          }
          
          if (subscription) {
            // Extract subscription_code from webhook data
            let emailToken = null;
            let subscriptionCode = null;
            
            // Check if webhook data contains subscription information
            if (body.data?.subscription) {
              subscriptionCode = body.data.subscription.subscription_code;
              emailToken = body.data.subscription.email_token;
              console.log('✅ Found subscription details in webhook data:', {
                subscriptionCode: subscriptionCode ? 'Found' : 'Not found',
                emailToken: emailToken ? 'Found' : 'Not found'
              });
            } else {
              console.log('⚠️ No subscription details found in webhook data');
            }
            
            if (subscription.pendingPlanType && subscription.cancelAtPeriodEnd) {
              // Handle plan change
              await prisma.subscription.update({
                where: { id: subscription.id },
                data: {
                  subscriptionId: subscriptionCode || reference, // Store actual subscription_code from Paystack
                  status: 'ACTIVE',
                  emailToken: emailToken,
                  planType: subscription.pendingPlanType,
                  priceId: subscription.pendingPriceId || subscription.priceId,
                  cancelAtPeriodEnd: false,
                  canceledAt: null,
                  pendingPlanType: null,
                  pendingPriceId: null,
                  pendingPlanEffectiveDate: null,
                  metadata: {
                    ...subscription.metadata as any,
                    webhookProcessed: true,
                    webhookData: body.data,
                    planChangeCompleted: new Date().toISOString()
                  }
                }
              });
              console.log('✅ Plan change completed via webhook:', subscription.id);
            } else {
              // Handle new subscription
              await prisma.subscription.update({
                where: { id: subscription.id },
                data: {
                  subscriptionId: subscriptionCode || reference, // Store actual subscription_code from Paystack
                  status: 'ACTIVE',
                  emailToken: emailToken,
                  metadata: {
                    ...subscription.metadata as any,
                    webhookProcessed: true,
                    webhookData: body.data
                  }
                }
              });
              console.log('✅ Subscription activated via webhook:', subscription.id);
            }
          }
        }
        break;
        
      case 'subscription.create':
        console.log('📝 Subscription created webhook received');
        break;
        
      default:
        console.log('ℹ️ Unhandled webhook event:', body.event);
    }
    
    return NextResponse.json({ status: 'success' });
  } catch (error) {
    console.error('❌ Webhook error:', error);
    return NextResponse.json({ error: 'Webhook processing failed' }, { status: 500 });
  }
}