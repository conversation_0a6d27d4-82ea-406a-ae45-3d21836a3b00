import { NextRequest, NextResponse } from "next/server";

/**
 * Simplified callback route for handling Flutterwave payment completion redirects.
 *
 * Note: Subscription processing is handled via webhooks.
 * This route only handles user redirects after payment completion.
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const txRef = searchParams.get("tx_ref");
    const transactionId = searchParams.get("transaction_id");
    const status = searchParams.get("status");

    const transactionRef = txRef || transactionId;

    console.log("🔍 Processing payment callback:", {
      txRef,
      transactionId,
      status,
      transactionRef,
    });

    // Simple status-based redirect
    // Webhook handles the actual subscription processing
    if (status === "successful") {
      console.log("✅ Payment completed successfully");
      return NextResponse.redirect(
        new URL("/upgrade?success=payment_completed", request.url)
      );
    } else if (status === "cancelled") {
      console.log("⚠️ Payment was cancelled");
      return NextResponse.redirect(
        new URL("/upgrade?error=payment_cancelled", request.url)
      );
    } else {
      console.log("❌ Payment failed or unknown status:", status);
      return NextResponse.redirect(
        new URL("/upgrade?error=payment_failed", request.url)
      );
    }
  } catch (error) {
    console.error("❌ Callback error:", error);
    return NextResponse.redirect(
      new URL("/upgrade?error=callback_error", request.url)
    );
  }
}
