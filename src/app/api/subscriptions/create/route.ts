import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createSubscription } from "@/lib/flutterwave";
import { db, subscription } from "@/lib/db";
import { getFlutterwavePlanCode } from "@/lib/flutterwave-config";
import { getServerSideRegion } from "@/lib/location-pricing";
import { CreateSubscriptionRequest } from "@/types/subscription";
import { nanoid } from "nanoid";
import { eq, and, inArray, gte, lte } from "drizzle-orm";

// Define Flutterwave API response types
interface FlutterwaveInitializeResponse {
  status: "success" | "error";
  message: string;
  data?: {
    link?: string;
    tx_ref?: string;
    id?: number;
  };
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: CreateSubscriptionRequest = await request.json();
    const { planType, billingCycle, customerInfo } = body;

    // Validate plan type
    if (planType === "Free") {
      return NextResponse.json(
        { error: "Cannot create subscription for free plan" },
        { status: 400 }
      );
    }

    // Determine user region and get plan code
    const region = getServerSideRegion(request);
    const planCode = getFlutterwavePlanCode(planType, billingCycle, region);
    if (!planCode) {
      return NextResponse.json(
        { error: "Invalid plan selected" },
        { status: 400 }
      );
    }

    // Check if user already has an active subscription that is not scheduled for cancellation
    const existingSubscriptionResult = await db
      .select()
      .from(subscription)
      .where(
        and(
          eq(subscription.userId, user.id),
          inArray(subscription.status, ["ACTIVE", "NON_RENEWING"]),
          eq(subscription.cancelAtPeriodEnd, false)
        )
      )
      .limit(1);

    const existingSubscription = existingSubscriptionResult[0];

    // If user has an existing active subscription that is not set to cancel at period end,
    // instruct them to cancel it first.
    if (existingSubscription) {
      console.log(
        "🚫 User has an existing active subscription not set to cancel at period end."
      );
      return NextResponse.json(
        {
          error:
            "You already have an active subscription. Please cancel it before creating a new one.",
          errorCode: "ACTIVE_SUBSCRIPTION_EXISTS",
        },
        { status: 400 }
      );
    }

    // Check for recent pending subscriptions (within 15 minutes)
    const recentPendingSubscriptionResult = await db
      .select()
      .from(subscription)
      .where(
        and(
          eq(subscription.userId, user.id),
          eq(subscription.status, "PENDING"),
          gte(subscription.createdAt, new Date(Date.now() - 15 * 60 * 1000))
        )
      )
      .orderBy(subscription.createdAt)
      .limit(1);

    const recentPendingSubscription = recentPendingSubscriptionResult[0];

    if (recentPendingSubscription) {
      const timeElapsed =
        Date.now() - recentPendingSubscription.createdAt.getTime();
      const timeRemaining = Math.max(0, 15 * 60 * 1000 - timeElapsed);
      const minutesRemaining = Math.ceil(timeRemaining / (60 * 1000));
      const secondsRemaining = Math.ceil(timeRemaining / 1000);

      console.log(
        `🚫 User has a pending subscription created ${Math.floor(
          timeElapsed / 1000
        )}s ago. ${secondsRemaining}s remaining.`
      );

      return NextResponse.json(
        {
          error: `You have a pending subscription. Please wait ${minutesRemaining} minute(s) before creating a new one.`,
          errorCode: "PENDING_SUBSCRIPTION_COOLDOWN",
          timeRemainingMs: timeRemaining,
          timeRemainingSeconds: secondsRemaining,
          minutesRemaining: minutesRemaining,
        },
        { status: 429 } // Too Many Requests
      );
    }

    // Clean up any failed/pending subscriptions older than 20 minutes (15 min + 5 min grace)
    await db
      .delete(subscription)
      .where(
        and(
          eq(subscription.userId, user.id),
          inArray(subscription.status, ["PENDING"]),
          lte(subscription.createdAt, new Date(Date.now() - 20 * 60 * 1000))
        )
      );

    console.log("🔄 Creating subscription with Flutterwave...");
    console.log("📧 Customer info:", {
      email: customerInfo.email,
      name: customerInfo.name,
      phone: customerInfo.phone || "not provided",
    });
    console.log("📦 Plan code:", planCode);
    console.log("🌍 Region:", region);

    // Initialize subscription with Flutterwave
    const subscriptionResponse = await createSubscription(
      {
        email: customerInfo.email,
        name: customerInfo.name,
        phone: customerInfo.phone,
      },
      planType,
      billingCycle,
      region
    );

    console.log(
      "📦 Subscription response:",
      JSON.stringify(subscriptionResponse, null, 2)
    );

    if (
      subscriptionResponse.status !== "success" ||
      !subscriptionResponse.data?.link
    ) {
      throw new Error(
        `Failed to create subscription: ${
          subscriptionResponse.message || "Unknown error"
        }`
      );
    }

    // Store subscription in database
    console.log("💾 Storing subscription in database...");
    const currentPeriodEndDate = new Date(
      Date.now() + (billingCycle === "annual" ? 365 : 30) * 24 * 60 * 60 * 1000
    );

    // Transaction initialization - store reference in metadata, subscriptionId will be updated via webhook
    const subscriptionId = `pending_${Date.now()}`; // Temporary placeholder, webhook will update with actual subscription_code
    const subscriptionStatus = "PENDING"; // Will be activated via webhook
    console.log(
      "⏳ Using temporary placeholder, webhook will update with subscription_code"
    );

    const dbSubscriptionResult = await db
      .insert(subscription)
      .values({
        id: nanoid(),
        userId: user.id,
        subscriptionId: subscriptionId,
        emailToken: null,
        customerId: customerInfo.email,
        planType: planType as any,
        status: subscriptionStatus as any,
        pendingPlanType: planType as any,
        pendingPriceId: productId,
        pendingPlanEffectiveDate: currentPeriodEndDate,
        billingCycle,
        currentPeriodStart: new Date(),
        currentPeriodEnd: currentPeriodEndDate,
        priceId: planCode,
        metadata: {
          billingCycle,
          transactionRef: subscriptionResponse.data?.tx_ref,
          planCode: planCode,
          region: region,
          isUpgrade: false,
          previousPlan: null,
          upgradeDate: null,
          creationMethod: "flutterwave_init",
          flutterwaveId: subscriptionResponse.data?.id,
        },
      })
      .returning();

    const dbSubscription = dbSubscriptionResult[0];

    console.log("✅ Subscription stored in database:", {
      id: dbSubscription.id,
      subscriptionId: dbSubscription.subscriptionId,
      status: dbSubscription.status,
      planType: dbSubscription.planType,
    });

    return NextResponse.json({
      success: true,
      subscriptionId: dbSubscription.subscriptionId,
      paymentLink: subscriptionResponse.data?.link,
      transactionRef: subscriptionResponse.data?.tx_ref,
      flutterwaveId: subscriptionResponse.data?.id,
      planType: planType,
      billingCycle: billingCycle,
      region: region,
      isUpgrade: false,
      message: "Subscription initialized. Please complete payment to activate.",
    });
  } catch (error) {
    console.error("Subscription creation error:", error);
    return NextResponse.json(
      { error: "Failed to create subscription" },
      { status: 500 }
    );
  }
}
