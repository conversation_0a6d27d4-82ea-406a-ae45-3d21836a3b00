import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { verifyTransaction } from "@/lib/flutterwave";
import { eq, and } from "drizzle-orm";

// Define enum types for subscription status and plan type
enum SubscriptionStatus {
  PENDING = "PENDING",
  ACTIVE = "ACTIVE",
  CANCELLED = "CANCELLED",
  NON_RENEWING = "NON_RENEWING",
  ATTENTION = "ATTENTION",
  COMPLETED = "COMPLETED",
}

enum PlanType {
  Starter = "Starter",
  Pro = "Pro",
  Unlimited = "Unlimited",
}

/**
 * Simple subscription activation endpoint
 * This bypasses webhooks and activates subscriptions directly after payment verification
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { transactionRef } = await request.json();

    console.log("🚀 Simple activation requested:", {
      userId: user.id,
      userEmail: user.email,
      transactionRef,
    });

    // Step 1: Verify the transaction was successful
    if (!transactionRef) {
      return NextResponse.json(
        { error: "Transaction reference is required" },
        { status: 400 }
      );
    }

    let transactionData = null;
    try {
      console.log("🔍 Verifying transaction:", transactionRef);
      const verification = await verifyTransaction(transactionRef);

      if (
        verification.status === "success" &&
        verification.data?.status === "successful"
      ) {
        transactionData = verification.data;
        console.log("✅ Transaction verified successfully:", {
          amount: transactionData.amount,
          currency: transactionData.currency,
          customer: transactionData.customer?.email,
        });
      } else {
        console.log("❌ Transaction verification failed:", verification);
        return NextResponse.json(
          { error: "Transaction verification failed" },
          { status: 400 }
        );
      }
    } catch (error) {
      console.error("❌ Transaction verification error:", error);
      return NextResponse.json(
        { error: "Failed to verify transaction" },
        { status: 500 }
      );
    }

    // Step 2: Find the user's pending subscription
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        and(
          eq(subscriptionTable.userId, user.id),
          eq(subscriptionTable.status, "PENDING")
        )
      )
      .orderBy(subscriptionTable.createdAt)
      .limit(1);

    const subscriptionData = subscriptionResult[0];

    if (!subscriptionData) {
      console.log("❌ No pending subscription found for user:", user.id);
      return NextResponse.json(
        { error: "No pending subscription found" },
        { status: 404 }
      );
    }

    console.log("📋 Found pending subscription:", {
      id: subscriptionData.id,
      planType: subscriptionData.planType,
      billingCycle: subscriptionData.billingCycle,
    });

    // Step 3: Activate the subscription
    const metadataUpdate = {
      ...(subscriptionData.metadata || {}),
      activatedAt: new Date().toISOString(),
      activationMethod: "simple_api",
      transactionRef: transactionRef,
      transactionAmount: transactionData.amount,
      transactionCurrency: transactionData.currency,
      verifiedAt: new Date().toISOString(),
    };

    const updateData: any = {
      status: SubscriptionStatus.ACTIVE,
      metadata: metadataUpdate,
      subscriptionId: transactionRef, // Use transaction ref as subscription ID
    };

    // Apply pending plan changes if any
    if (subscriptionData.pendingPlanType) {
      updateData.planType = subscriptionData.pendingPlanType as PlanType;
      updateData.pendingPlanType = null;
    }

    // Update subscription in database
    const updatedSubscriptionResult = await db
      .update(subscriptionTable)
      .set(updateData)
      .where(eq(subscriptionTable.id, subscriptionData.id))
      .returning();

    const updatedSubscription = updatedSubscriptionResult[0];

    console.log("✅ Subscription activated successfully:", {
      id: updatedSubscription.id,
      status: updatedSubscription.status,
      planType: updatedSubscription.planType,
    });

    return NextResponse.json({
      success: true,
      message: "Subscription activated successfully",
      subscription: {
        id: updatedSubscription.id,
        status: updatedSubscription.status,
        planType: updatedSubscription.planType,
        billingCycle: updatedSubscription.billingCycle,
      },
    });
  } catch (error) {
    console.error("❌ Simple activation error:", error);
    return NextResponse.json(
      { error: "Failed to activate subscription" },
      { status: 500 }
    );
  }
}
