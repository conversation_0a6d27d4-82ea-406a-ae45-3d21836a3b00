
import { createClient } from "@/utils/supabase/server";
import { activateSubscription, fetchSubscription } from "@/lib/paystack";
import { prisma } from "@/lib/prisma";
import { SubscriptionStatus } from "@prisma/client";
import { NextResponse } from "next/server";

export async function POST() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's cancelled subscription or subscription with pending plan change
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        OR: [
          {
            status: "CANCELLED",
            currentPeriodEnd: { gt: new Date() },
          },
          {
            status: "ACTIVE",
            cancelAtPeriodEnd: true,
            currentPeriodEnd: { gt: new Date() },
          },
          {
            status: "NON_RENEWING",
            currentPeriodEnd: { gt: new Date() },
          },
        ],
      },
    });

    if (!subscription) {
      return NextResponse.json(
        { error: "No cancelled subscription found" },
        { status: 404 }
      );
    }

    // Check if this is a plan change reactivation
    const isPlanChangeReactivation = subscription.pendingPlanType && subscription.cancelAtPeriodEnd;
    
    console.log("🔄 Reactivating subscription:", {
      subscriptionId: subscription.subscriptionId,
      userId: user.id,
      isPlanChangeReactivation,
      pendingPlan: subscription.pendingPlanType
    });

    try {
      // Reactivate subscription with Paystack
      const emailToken = subscription.emailToken;
      
      if (!emailToken) {
        console.warn("⚠️ No email token found for subscription. Attempting to fetch from Paystack...");
        try {
          const paystackSubscription = await fetchSubscription(subscription.subscriptionId);
          const subscriptionCode = paystackSubscription?.data?.subscription_code || paystackSubscription?.data?.code;
          const fetchedEmailToken = paystackSubscription?.data?.email_token;
          
          console.log('📧 Retrieved subscription details from Paystack:', {
            subscriptionCode: subscriptionCode ? 'Found' : 'Not found',
            emailToken: fetchedEmailToken ? 'Found' : 'Not found'
          });
          
          if (fetchedEmailToken) {
            // Update the subscription with the email token for future use
            await prisma.subscription.update({
              where: { id: subscription.id },
              data: { emailToken: fetchedEmailToken }
            });
            console.log("📧 Retrieved and stored email token from Paystack");
          }
          
          const reactivatedProviderSub = await activateSubscription(
            subscriptionCode || subscription.subscriptionId,
            fetchedEmailToken || subscription.customerId || subscription.subscriptionId
          ) as any;
          
          console.log("✅ Subscription reactivated with Paystack:", {
            subscription_code: reactivatedProviderSub?.data?.subscription_code,
            status: reactivatedProviderSub?.data?.status,
            used_token: fetchedEmailToken ? 'email_token' : 'fallback'
          });
        } catch (fetchError) {
          console.error("❌ Failed to fetch email token from Paystack:", fetchError);
          console.log("⚠️ Proceeding with local reactivation due to fetch error");
        }
      } else {
        // Fetch subscription details to get the correct subscription_code
        let subscriptionCode = subscription.subscriptionId;
        try {
          const paystackSubscription = await fetchSubscription(subscription.subscriptionId);
          subscriptionCode = paystackSubscription?.data?.subscription_code || paystackSubscription?.data?.code || subscription.subscriptionId;
          console.log('📧 Retrieved subscription_code from Paystack:', subscriptionCode ? 'Found' : 'Using fallback');
        } catch (error) {
          console.warn('⚠️ Failed to fetch subscription_code, using stored subscriptionId:', error);
        }
        
        const reactivatedProviderSub = await activateSubscription(
          subscriptionCode,
          emailToken
        ) as any;
        
        console.log("✅ Subscription reactivated with Paystack:", {
          subscription_code: reactivatedProviderSub?.data?.subscription_code,
          status: reactivatedProviderSub?.data?.status,
          used_token: 'stored_email_token'
        });
      }
    } catch (providerError) {
      console.error(
        "❌ Error reactivating with Paystack:",
        providerError
      );
      console.log(
        "⚠️ Proceeding with local reactivation due to provider error"
      );
    }

    // Update subscription in our database
    const updateData: any = {
      status: SubscriptionStatus.ACTIVE,
      cancelAtPeriodEnd: false,
      canceledAt: null,
      metadata: {
        ...(subscription.metadata && typeof subscription.metadata === "object"
          ? subscription.metadata
          : {}),
        reactivatedBy: "user",
        reactivatedAt: new Date().toISOString(),
      },
    };
    
    // If this was a plan change reactivation, clear the pending plan fields
    if (isPlanChangeReactivation) {
      updateData.pendingPlanType = null;
      updateData.pendingPriceId = null;
      updateData.pendingPlanEffectiveDate = null;
      updateData.metadata.planChangeAbandoned = new Date().toISOString();
      updateData.metadata.abandonedPendingPlan = subscription.pendingPlanType;
      
      console.log("🚫 Clearing pending plan change:", {
        abandonedPlan: subscription.pendingPlanType,
        currentPlan: subscription.planType
      });
    }
    
    const reactivatedSubscription = await prisma.subscription.update({
      where: { id: subscription.id },
      data: updateData,
    });

    console.log("✅ Subscription reactivated in database:", {
      id: reactivatedSubscription.id,
      status: reactivatedSubscription.status,
      planChangeCleared: isPlanChangeReactivation,
    });

    return NextResponse.json({
      success: true,
      message: "Subscription reactivated successfully",
      subscription: {
        id: reactivatedSubscription.id,
        status: reactivatedSubscription.status,
        cancelAtPeriodEnd: reactivatedSubscription.cancelAtPeriodEnd,
        canceledAt: reactivatedSubscription.canceledAt,
        currentPeriodEnd: reactivatedSubscription.currentPeriodEnd,
      },
    });
  } catch (error) {
    console.error("❌ Subscription reactivation error:", error);
    return NextResponse.json(
      {
        error: "Failed to reactivate subscription",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
