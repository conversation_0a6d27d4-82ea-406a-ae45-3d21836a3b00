import { createClient } from "@/utils/supabase/server";
import { activateSubscription, fetchSubscription } from "@/lib/flutterwave";
import { db, subscription as subscriptionTable } from "@/lib/db";
import { NextResponse } from "next/server";
import { eq, and, or, gt } from "drizzle-orm";

// Define SubscriptionStatus locally since we're moving away from
const SubscriptionStatus = {
  ACTIVE: "ACTIVE" as const,
  CANCELLED: "CANCELLED" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  ATTENTION: "ATTENTION" as const,
  COMPLETED: "COMPLETED" as const,
};

export async function POST() {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's cancelled subscription or subscription with pending plan change
    const subscriptionResult = await db
      .select()
      .from(subscriptionTable)
      .where(
        and(
          eq(subscriptionTable.userId, user.id),
          or(
            and(
              eq(subscriptionTable.status, "CANCELLED"),
              gt(subscriptionTable.currentPeriodEnd, new Date())
            ),
            and(
              eq(subscriptionTable.status, "ACTIVE"),
              eq(subscriptionTable.cancelAtPeriodEnd, true),
              gt(subscriptionTable.currentPeriodEnd, new Date())
            ),
            and(
              eq(subscriptionTable.status, "NON_RENEWING"),
              gt(subscriptionTable.currentPeriodEnd, new Date())
            )
          )
        )
      )
      .limit(1);

    const subscription = subscriptionResult[0];

    if (!subscription) {
      return NextResponse.json(
        { error: "No cancelled subscription found" },
        { status: 404 }
      );
    }

    // Check if this is a plan change reactivation
    const isPlanChangeReactivation =
      subscription.pendingPlanType && subscription.cancelAtPeriodEnd;

    console.log("🔄 Reactivating subscription:", {
      subscriptionId: subscription.subscriptionId,
      userId: user.id,
      isPlanChangeReactivation,
      pendingPlan: subscription.pendingPlanType,
    });

    try {
      // Reactivate subscription with Paystack
      const emailToken = subscription.emailToken;

      if (!emailToken) {
        console.warn(
          "⚠️ No subscription details found. Attempting to fetch from Flutterwave..."
        );
        try {
          const flutterwaveSubscription = await fetchSubscription(
            subscription.subscriptionId
          );

          console.log("📧 Retrieved subscription details from Flutterwave:", {
            status: flutterwaveSubscription?.status,
            hasData: !!flutterwaveSubscription?.data,
          });

          const reactivatedProviderSub = await activateSubscription(
            subscriptionCode || subscription.subscriptionId
          );

          console.log("✅ Subscription reactivated with Flutterwave:", {
            subscription_id: reactivatedProviderSub?.data?.id,
            status: reactivatedProviderSub?.status,
          });
        } catch (fetchError) {
          console.error(
            "❌ Failed to fetch subscription from Flutterwave:",
            fetchError
          );
          console.log(
            "⚠️ Proceeding with local reactivation due to fetch error"
          );
        }
      } else {
        // Fetch subscription details to get the correct subscription_code
        let subscriptionCode = subscription.subscriptionId;
        try {
          const paystackSubscription = await fetchSubscription(
            subscription.subscriptionId
          );
          subscriptionCode =
            paystackSubscription?.data?.subscription_code ||
            paystackSubscription?.data?.code ||
            subscription.subscriptionId;
          console.log(
            "📧 Retrieved subscription_code from Paystack:",
            subscriptionCode ? "Found" : "Using fallback"
          );
        } catch (error) {
          console.warn(
            "⚠️ Failed to fetch subscription_code, using stored subscriptionId:",
            error
          );
        }

        const reactivatedProviderSub = await activateSubscription(
          subscriptionCode
        );

        console.log("✅ Subscription reactivated with Flutterwave:", {
          subscription_id: reactivatedProviderSub?.data?.id,
          status: reactivatedProviderSub?.status,
        });
      }
    } catch (providerError) {
      console.error("❌ Error reactivating with Flutterwave:", providerError);
      console.log(
        "⚠️ Proceeding with local reactivation due to provider error"
      );
    }

    // Update subscription in our database
    const updateData: any = {
      status: SubscriptionStatus.ACTIVE,
      cancelAtPeriodEnd: false,
      canceledAt: null,
      metadata: {
        ...(subscription.metadata && typeof subscription.metadata === "object"
          ? subscription.metadata
          : {}),
        reactivatedBy: "user",
        reactivatedAt: new Date().toISOString(),
      },
    };

    // If this was a plan change reactivation, clear the pending plan fields
    if (isPlanChangeReactivation) {
      updateData.pendingPlanType = null;
      updateData.pendingPriceId = null;
      updateData.pendingPlanEffectiveDate = null;
      updateData.metadata.planChangeAbandoned = new Date().toISOString();
      updateData.metadata.abandonedPendingPlan = subscription.pendingPlanType;

      console.log("🚫 Clearing pending plan change:", {
        abandonedPlan: subscription.pendingPlanType,
        currentPlan: subscription.planType,
      });
    }

    const reactivatedSubscriptionResult = await db
      .update(subscriptionTable)
      .set(updateData)
      .where(eq(subscriptionTable.id, subscription.id))
      .returning();

    const reactivatedSubscription = reactivatedSubscriptionResult[0];

    console.log("✅ Subscription reactivated in database:", {
      id: reactivatedSubscription.id,
      status: reactivatedSubscription.status,
      planChangeCleared: isPlanChangeReactivation,
    });

    return NextResponse.json({
      success: true,
      message: "Subscription reactivated successfully",
      subscription: {
        id: reactivatedSubscription.id,
        status: reactivatedSubscription.status,
        cancelAtPeriodEnd: reactivatedSubscription.cancelAtPeriodEnd,
        canceledAt: reactivatedSubscription.canceledAt,
        currentPeriodEnd: reactivatedSubscription.currentPeriodEnd,
      },
    });
  } catch (error) {
    console.error("❌ Subscription reactivation error:", error);
    return NextResponse.json(
      {
        error: "Failed to reactivate subscription",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
