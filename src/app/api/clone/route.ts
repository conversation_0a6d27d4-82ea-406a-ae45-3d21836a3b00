import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { nanoid } from "nanoid";
import { eq, and } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function POST(req: Request) {
  try {
    // Authenticate the user
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();
    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();

    // Share Token Branch: If shareToken is provided, follow the shared content logic
    if (body.shareToken && typeof body.shareToken === "string") {
      const { shareToken } = body;

      // Find the original shared content from ContentSummary table.
      const originalSummaryResult = await db
        .select({
          contentId: contentSummary.contentId,
          title: contentSummary.title,
          content: contentSummary.content,
          originalContent: contentSummary.originalContent,
          sourceId: contentSummary.sourceId,
          outputType: contentSummary.outputType,
          contentType: contentSummary.contentType,
          translatedContent: contentSummary.translatedContent,
          translationLanguage: contentSummary.translationLanguage,
        })
        .from(contentSummary)
        .where(
          and(
            eq(contentSummary.shareToken, shareToken),
            eq(contentSummary.isShared, true)
          )
        )
        .limit(1);

      const originalSummary = originalSummaryResult[0];

      if (!originalSummary) {
        return NextResponse.json(
          { error: "Shared content not found or no longer available" },
          { status: 404 }
        );
      }

      // Check if the user already has this summary (using compound unique fields)
      const existingSummaryResult = await db
        .select()
        .from(contentSummary)
        .where(
          and(
            eq(contentSummary.userId, user.id),
            eq(contentSummary.sourceId, originalSummary.sourceId),
            eq(contentSummary.contentType, originalSummary.contentType)
          )
        )
        .limit(1);

      const existingSummary = existingSummaryResult[0];

      if (existingSummary) {
        // Update the existing summary
        const updatedSummaryResult = await db
          .update(contentSummary)
          .set({
            content: originalSummary.content,
            originalContent: originalSummary.originalContent,
            outputType: originalSummary.outputType,
            generatedAt: new Date(),
          })
          .where(eq(contentSummary.id, existingSummary.id))
          .returning();

        const updatedSummary = updatedSummaryResult[0];

        return NextResponse.json({
          success: true,
          message: "Content updated successfully",
          contentId: updatedSummary.contentId,
          contentType: originalSummary.contentType.toLowerCase(),
          outputType: originalSummary.outputType,
        });
      } else {
        // Create a new summary for the user
        // Generate a new contentId to avoid unique constraint violation
        // but preserve the sourceId which is used for content identification
        const newContentId = nanoid(10);
        const newSummaryResult = await db
          .insert(contentSummary)
          .values({
            id: nanoid(),
            userId: user.id,
            contentId: newContentId,
            contentType: originalSummary.contentType,
            title: originalSummary.title,
            content: originalSummary.content,
            originalContent: originalSummary.originalContent,
            sourceId: originalSummary.sourceId,
            outputType: originalSummary.outputType,
            translatedContent: originalSummary.translatedContent,
            translationLanguage: originalSummary.translationLanguage,
            generatedAt: new Date(),
            isShared: false,
            shareToken: null,
          })
          .returning();

        const newSummary = newSummaryResult[0];
        return NextResponse.json({
          success: true,
          message: "Content cloned successfully",
          contentId: newSummary.contentId,
          contentType: originalSummary.contentType.toLowerCase(),
          outputType: originalSummary.outputType,
        });
      }
    }
    // Direct Content Branch: If shareToken is not provided, check for complete content details
    else if (
      body.contentId &&
      body.contentType &&
      body.title &&
      body.summary &&
      body.sourceId &&
      body.originalContent && // Add check for originalContent
      typeof body.contentId === "string" &&
      typeof body.contentType === "string" &&
      typeof body.title === "string" &&
      typeof body.summary === "string" &&
      typeof body.sourceId === "string" &&
      typeof body.originalContent === "string" // Add type check for originalContent
    ) {
      const { contentType, title, summary, sourceId, originalContent } = body;

      // Extract outputType from the request body
      const outputType = body.outputType;

      // Check if the user already has this content using the compound unique index
      const existingSummaryResult = await db
        .select()
        .from(contentSummary)
        .where(
          and(
            eq(contentSummary.userId, user.id),
            eq(contentSummary.sourceId, sourceId),
            eq(contentSummary.contentType, contentType.toUpperCase() as any)
          )
        )
        .limit(1);

      const existingSummary = existingSummaryResult[0];

      if (existingSummary) {
        // Update existing summary
        const updatedSummary = await prisma.contentSummary.update({
          where: { id: existingSummary.id },
          data: {
            content: summary,
            originalContent: originalContent, // Update originalContent too
            outputType: outputType, // Use the provided outputType
            generatedAt: new Date(),
          },
        });

        return NextResponse.json({
          success: true,
          message: "Content updated successfully",
          contentId: updatedSummary.contentId,
          contentType: contentType.toLowerCase(),
          outputType: outputType,
        });
      } else {
        // Generate a new contentId to avoid unique constraint violation
        // but preserve the sourceId which is used for content identification
        const newContentId = nanoid(10);
        const newSummary = await prisma.contentSummary.create({
          data: {
            userId: user.id,
            contentId: newContentId, // Generate a new unique contentId
            contentType: contentType.toUpperCase(),
            title,
            content: summary,
            originalContent: originalContent, // Use provided originalContent
            sourceId,
            outputType: outputType, // Use the outputType from the request
            generatedAt: new Date(),
            isShared: false,
            shareToken: null,
          },
        });

        return NextResponse.json({
          success: true,
          message: "Content cloned successfully",
          contentId: newSummary.contentId,
          contentType: contentType.toLowerCase(),
          outputType: outputType,
        });
      }
    }
    // Fallback for backward compatibility - if originalContent is not provided
    else if (
      body.contentId &&
      body.contentType &&
      body.title &&
      body.summary &&
      body.sourceId &&
      typeof body.contentId === "string" &&
      typeof body.contentType === "string" &&
      typeof body.title === "string" &&
      typeof body.summary === "string" &&
      typeof body.sourceId === "string"
    ) {
      // Extract fields from body, ensuring outputType is always defined
      const { contentType, title, summary, sourceId } = body;
      // Extract outputType from the request body
      const outputType = body.outputType;

      // Check if the user already has this content using the compound unique index
      const existingSummary = await prisma.contentSummary.findFirst({
        where: {
          userId: user.id,
          sourceId,
          contentType: contentType.toUpperCase(),
        },
      });

      if (existingSummary) {
        // Update existing summary
        const updatedSummary = await prisma.contentSummary.update({
          where: { id: existingSummary.id },
          data: {
            content: summary,
            outputType: outputType,
            generatedAt: new Date(),
          },
        });

        return NextResponse.json({
          success: true,
          message: "Content updated successfully",
          contentId: updatedSummary.contentId,
          contentType: contentType.toLowerCase(),
          outputType: outputType,
        });
      } else {
        // Generate a new contentId to avoid unique constraint violation
        // but preserve the sourceId which is used for content identification
        const newContentId = nanoid(10);
        const newSummary = await prisma.contentSummary.create({
          data: {
            userId: user.id,
            contentId: newContentId, // Generate a new unique contentId
            contentType: contentType.toUpperCase(),
            title,
            content: summary,
            originalContent: summary, // For backward compatibility
            sourceId,
            outputType: outputType,
            generatedAt: new Date(),
            isShared: false,
            shareToken: null,
          },
        });

        return NextResponse.json({
          success: true,
          message: "Content cloned successfully",
          contentId: newSummary.contentId,
          contentType: contentType.toLowerCase(),
          outputType: outputType,
        });
      }
    } else {
      return NextResponse.json(
        { error: "Either shareToken or complete content details are required" },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error cloning content:", error);
    return NextResponse.json(
      {
        error: "Failed to clone content",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
