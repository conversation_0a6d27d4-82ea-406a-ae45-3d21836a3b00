import { NextResponse } from "next/server";
import { db, users } from "@/lib/db";
import { syncUserWithDatabase } from "@/utils/user-management";
import { createClient } from "@/utils/supabase/server";
import { eq } from "drizzle-orm";

// Cache control headers
const cacheHeaders = {
  "Cache-Control": "private, max-age=60", // Cache for 60 seconds for authenticated users
  Vary: "Authorization", // Vary cache by auth status
};

export async function GET() {
  try {
    // Get the current user from Supabase auth
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error) {
      console.error("Error fetching session:", error);
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    }

    if (!user) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Check if user exists in database first before syncing
    const dbUserResult = await db
      .select()
      .from(users)
      .where(eq(users.id, user.id))
      .limit(1);

    let dbUser = dbUserResult[0];

    // Only sync if user doesn't exist or data needs updating
    if (
      !dbUser ||
      dbUser.email !== user.email ||
      dbUser.displayName !==
        (user.user_metadata?.full_name || user.user_metadata?.name) ||
      dbUser.avatarUrl !== user.user_metadata?.avatar_url
    ) {
      dbUser = await syncUserWithDatabase(user);
    }

    // Return user data with cache headers
    return NextResponse.json(
      { user: dbUser },
      {
        headers: cacheHeaders,
      }
    );
  } catch (error) {
    console.error("Error in user profile API:", error);
    return NextResponse.json(
      { error: "Failed to retrieve user profile" },
      { status: 500 }
    );
  }
}

// Update user profile
export async function PATCH(request: Request) {
  try {
    // Get the current user from Supabase auth
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json(
        { error: "Authentication error" },
        { status: 401 }
      );
    }

    // Parse the request body
    const { displayName } = await request.json();

    // Update user in database
    const updatedUserResult = await db
      .update(users)
      .set({
        displayName,
        updatedAt: new Date(),
      })
      .where(eq(users.id, user.id))
      .returning();

    const updatedUser = updatedUserResult[0];

    return NextResponse.json({ user: updatedUser });
  } catch (error) {
    console.error("Error updating user profile:", error);
    return NextResponse.json(
      { error: "Failed to update user profile" },
      { status: 500 }
    );
  }
}
