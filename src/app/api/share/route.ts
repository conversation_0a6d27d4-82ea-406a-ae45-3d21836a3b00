import { NextResponse } from "next/server";
import { db, contentSummary } from "@/lib/db";
import { createClient } from "@/utils/supabase/server";
import { v4 as uuidv4 } from "uuid";
import { eq } from "drizzle-orm";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";

export async function PUT(req: Request) {
  try {
    // Log the entire request for debugging
    console.log("Incoming request headers:", Object.fromEntries(req.headers));

    // Authenticate the user with the new SSR package
    const supabase = await createClient();
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Robust parsing of request body
    let body;
    try {
      body = await req.json();
      console.log("Parsed request body:", body);
    } catch (parseError) {
      console.error("Error parsing request body:", parseError);
      return NextResponse.json(
        { error: "Invalid request body format", details: parseError },
        { status: 400 }
      );
    }

    // Validate body more thoroughly
    if (!body || typeof body !== "object") {
      console.error("Invalid body type:", typeof body, body);
      return NextResponse.json(
        { error: "Invalid request body: must be an object" },
        { status: 400 }
      );
    }

    const { contentId, share, forceRegenerate } = body;

    // Enhanced validation
    if (!contentId || typeof contentId !== "string") {
      console.error("Invalid contentId:", contentId);
      return NextResponse.json(
        { error: "contentId is required and must be a string" },
        { status: 400 }
      );
    }
    if (typeof share !== "boolean") {
      console.error("Invalid share flag:", share);
      return NextResponse.json(
        { error: "share flag is required and must be a boolean" },
        { status: 400 }
      );
    }

    // Find the content summary belonging to the user.
    const contentSummaryResult = await db
      .select()
      .from(contentSummary)
      .where(eq(contentSummary.contentId, contentId))
      .limit(1);

    const contentSummaryData = contentSummaryResult[0];

    if (!contentSummaryData) {
      return NextResponse.json(
        { error: "Content summary not found" },
        { status: 404 }
      );
    }

    // Verify that the content belongs to the user
    if (contentSummaryData.userId !== user.id) {
      return NextResponse.json(
        {
          error:
            "Unauthorized: You don't have permission to share this content",
        },
        { status: 403 }
      );
    }

    const updatedData: { isShared?: boolean; shareToken?: string | null } = {};
    if (share) {
      updatedData.isShared = true;
      // Generate a token if not already available or if force regeneration is requested
      if (!contentSummary.shareToken || forceRegenerate) {
        updatedData.shareToken = uuidv4();
      } else {
        updatedData.shareToken = contentSummary.shareToken;
      }
    } else {
      updatedData.isShared = false;
      updatedData.shareToken = null;
    }

    const updatedSummaryResult = await db
      .update(contentSummary)
      .set(updatedData)
      .where(eq(contentSummary.id, contentSummaryData.id))
      .returning();

    const updatedSummary = updatedSummaryResult[0];

    return NextResponse.json({
      success: true,
      isShared: updatedSummary.isShared,
      shareToken: updatedSummary.shareToken,
    });
  } catch (error) {
    console.error("Comprehensive error toggling share:", error);
    return NextResponse.json(
      {
        error: "Failed to toggle sharing",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
