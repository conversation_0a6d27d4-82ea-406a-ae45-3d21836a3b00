import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { db, subscription } from "@/lib/db";
import { eq, or } from "drizzle-orm";
import crypto from "crypto";
import { verifyTransaction } from "@/lib/flutterwave";

// Validate webhook secret exists
if (!process.env.FLUTTERWAVE_SECRET_KEY) {
  throw new Error("FLUTTERWAVE_SECRET_KEY environment variable is required");
}

// Define SubscriptionStatus locally
const SubscriptionStatus = {
  PENDING: "PENDING" as const,
  ACTIVE: "ACTIVE" as const,
  NON_RENEWING: "NON_RENEWING" as const,
  CANCELLED: "CANCELLED" as const,
  ATTENTION: "ATTENTION" as const,
  COMPLETED: "COMPLETED" as const,
};

interface FlutterwaveWebhookEvent {
  event: string;
  data: {
    id: number;
    tx_ref: string;
    flw_ref: string;
    device_fingerprint: string;
    amount: number;
    currency: string;
    charged_amount: number;
    app_fee: number;
    merchant_fee: number;
    processor_response: string;
    auth_model: string;
    ip: string;
    narration: string;
    status: string;
    payment_type: string;
    created_at: string;
    account_id: number;
    customer: {
      id: number;
      name: string;
      phone_number: string;
      email: string;
      created_at: string;
    };
    card?: {
      first_6digits: string;
      last_4digits: string;
      issuer: string;
      country: string;
      type: string;
      expiry: string;
    };
  };
}

/**
 * Verify Flutterwave webhook signature
 */
function verifyFlutterwaveSignature(
  payload: string,
  signature: string
): boolean {
  const hash = crypto
    .createHmac("sha256", process.env.FLUTTERWAVE_SECRET_KEY!)
    .update(payload)
    .digest("hex");

  return hash === signature;
}

export async function POST(request: NextRequest) {
  console.log("📨 Flutterwave webhook received");

  try {
    const headersList = await headers();
    const rawBody = await request.text();
    const signature = headersList.get("verif-hash");

    console.log("📋 Received Flutterwave webhook headers:", {
      signature: signature ? "present" : "missing",
      bodyLength: rawBody.length,
    });

    if (!signature) {
      console.error("❌ Missing verif-hash header");
      return NextResponse.json(
        { error: "Missing signature header" },
        { status: 401 }
      );
    }

    // Verify webhook signature
    if (!verifyFlutterwaveSignature(rawBody, signature)) {
      console.error("❌ Invalid webhook signature");
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    const body: FlutterwaveWebhookEvent = JSON.parse(rawBody);
    console.log("📨 Webhook event:", body.event);
    console.log("📄 Webhook data:", JSON.stringify(body.data, null, 2));

    // Handle different webhook events
    switch (body.event) {
      case "charge.completed":
        await handleChargeCompleted(body.data);
        break;

      case "subscription.activated":
        await handleSubscriptionActivated(body.data);
        break;

      case "subscription.cancelled":
        await handleSubscriptionCancelled(body.data);
        break;

      default:
        console.log("ℹ️ Unhandled webhook event:", body.event);
    }

    console.log("✅ Flutterwave webhook processed successfully");
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("❌ Flutterwave webhook error:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
    }

    // Return appropriate error response
    if (error instanceof Error && error.message.includes("signature")) {
      return NextResponse.json(
        { error: "Invalid webhook signature" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}

async function handleChargeCompleted(data: FlutterwaveWebhookEvent["data"]) {
  console.log("💰 Processing charge.completed for:", data.tx_ref);

  try {
    // Verify the transaction with Flutterwave
    const verification = await verifyTransaction(data.id.toString());

    if (
      verification.status !== "success" ||
      verification.data?.status !== "successful"
    ) {
      console.log("❌ Transaction verification failed:", verification);
      return;
    }

    // Find the subscription by transaction reference
    const subscriptionResult = await db
      .select()
      .from(subscription)
      .where(
        or(
          eq(subscription.subscriptionId, data.tx_ref),
          // Also check metadata for transaction reference
          eq(subscription.subscriptionId, `pending_${data.tx_ref}`)
        )
      )
      .limit(1);

    const subscriptionRecord = subscriptionResult[0];

    if (!subscriptionRecord) {
      console.log("❌ No subscription found for transaction:", data.tx_ref);
      return;
    }

    // Update subscription status to ACTIVE
    await db
      .update(subscription)
      .set({
        status: SubscriptionStatus.ACTIVE,
        subscriptionId: data.flw_ref, // Use Flutterwave reference
        metadata: {
          ...(subscriptionRecord.metadata as any),
          flutterwaveTransactionId: data.id,
          flutterwaveReference: data.flw_ref,
          transactionRef: data.tx_ref,
          paymentVerified: true,
          verificationData: verification.data,
          activatedViaWebhook: true,
          activatedAt: new Date().toISOString(),
        },
      })
      .where(eq(subscription.id, subscriptionRecord.id));

    console.log(
      "✅ Subscription activated via webhook:",
      subscriptionRecord.id
    );
  } catch (error) {
    console.error("❌ Error handling charge.completed:", error);
    throw error;
  }
}

async function handleSubscriptionActivated(
  data: FlutterwaveWebhookEvent["data"]
) {
  console.log("🆕 Processing subscription.activated for:", data.tx_ref);

  // This event is typically fired when a subscription is activated
  // We can use this to ensure the subscription is properly activated
  await handleChargeCompleted(data);
}

async function handleSubscriptionCancelled(
  data: FlutterwaveWebhookEvent["data"]
) {
  console.log("🚫 Processing subscription.cancelled for:", data.tx_ref);

  try {
    // Find the subscription by Flutterwave reference
    const subscriptionResult = await db
      .select()
      .from(subscription)
      .where(eq(subscription.subscriptionId, data.flw_ref))
      .limit(1);

    const subscriptionRecord = subscriptionResult[0];

    if (!subscriptionRecord) {
      console.log("❌ No subscription found for reference:", data.flw_ref);
      return;
    }

    // Update subscription status to CANCELLED
    await db
      .update(subscription)
      .set({
        status: SubscriptionStatus.CANCELLED,
        canceledAt: new Date(),
        metadata: {
          ...(subscriptionRecord.metadata as any),
          cancelledViaWebhook: true,
          cancelledAt: new Date().toISOString(),
          cancellationData: data,
        },
      })
      .where(eq(subscription.id, subscriptionRecord.id));

    console.log(
      "✅ Subscription cancelled via webhook:",
      subscriptionRecord.id
    );
  } catch (error) {
    console.error("❌ Error handling subscription.cancelled:", error);
    throw error;
  }
}
