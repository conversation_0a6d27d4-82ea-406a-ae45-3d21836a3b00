import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { prisma } from "@/lib/prisma";
import { SubscriptionStatus } from "@prisma/client";
import crypto from "crypto";

// Validate webhook secret exists
if (!process.env.PAYSTACK_SECRET_KEY) {
  throw new Error("PAYSTACK_SECRET_KEY environment variable is required");
}

interface PaystackWebhookEvent {
  event: string;
  data: {
    id?: number;
    domain?: string;
    status?: string;
    reference?: string;
    amount?: number;
    message?: string;
    gateway_response?: string;
    paid_at?: string;
    created_at?: string;
    channel?: string;
    currency?: string;
    ip_address?: string;
    metadata?: any;
    log?: any;
    fees?: number;
    fees_split?: any;
    authorization?: {
      authorization_code: string;
      bin: string;
      last4: string;
      exp_month: string;
      exp_year: string;
      channel: string;
      card_type: string;
      bank: string;
      country_code: string;
      brand: string;
      reusable: boolean;
      signature: string;
      account_name?: string;
    };
    customer?: {
      id: number;
      first_name?: string;
      last_name?: string;
      email: string;
      customer_code: string;
      phone?: string;
      metadata?: any;
      risk_action: string;
      international_format_phone?: string;
    };
    plan?: {
      id: number;
      name: string;
      plan_code: string;
      description?: string;
      amount: number;
      interval: string;
      send_invoices: boolean;
      send_sms: boolean;
      currency: string;
    };
    subscription?: {
      id: number;
      subscription_code: string;
      email_token: string;
      amount: number;
      cron_expression?: string;
      next_payment_date?: string;
      open_invoice?: string;
      createdAt: string;
      updatedAt: string;
    };
    // For subscription-specific events
    subscription_code?: string;
    email_token?: string;
    next_payment_date?: string;
    open_invoice?: string;
  };
}

export async function POST(request: NextRequest) {
  console.log("🔔 Paystack webhook received at:", new Date().toISOString());

  try {
    const headersList = await headers();
    const rawBody = await request.text();
    const signature = headersList.get("x-paystack-signature");

    console.log("📋 Received Paystack webhook headers:", {
      signature: signature ? "present" : "missing",
      bodyLength: rawBody.length,
    });

    if (!signature) {
      console.error("❌ Missing x-paystack-signature header");
      return NextResponse.json(
        { error: "Missing signature header" },
        { status: 401 }
      );
    }

    // Verify webhook signature
    const hash = crypto
      .createHmac("sha512", process.env.PAYSTACK_SECRET_KEY!)
      .update(rawBody)
      .digest("hex");

    if (hash !== signature) {
      console.error("❌ Invalid webhook signature");
      return NextResponse.json(
        { error: "Invalid signature" },
        { status: 401 }
      );
    }

    console.log("✅ Paystack webhook signature verified successfully");

    // Parse and process the event
    const event = JSON.parse(rawBody) as PaystackWebhookEvent;
    console.log("📦 Processing Paystack webhook event:", {
      event: event.event,
      reference: event.data?.reference,
      subscription_code: event.data?.subscription_code,
    });

    switch (event.event) {
      case "charge.success":
        await handleChargeSuccess(event.data);
        break;

      case "subscription.create":
        await handleSubscriptionCreate(event.data);
        break;

      case "subscription.not_renew":
        await handleSubscriptionNotRenew(event.data);
        break;

      case "subscription.disable":
        await handleSubscriptionDisable(event.data);
        break;

      case "invoice.create":
        await handleInvoiceCreate(event.data);
        break;

      case "invoice.payment_failed":
        await handleInvoicePaymentFailed(event.data);
        break;

      case "invoice.update":
        await handleInvoiceUpdate(event.data);
        break;

      default:
        console.log("⚠️ Unhandled Paystack webhook event type:", event.event);
    }

    console.log("✅ Paystack webhook processed successfully");
    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("❌ Paystack webhook error:", error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      });
    }

    // Return appropriate error response
    if (error instanceof Error && error.message.includes("signature")) {
      return NextResponse.json(
        { error: "Invalid webhook signature" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}

async function handleChargeSuccess(data: PaystackWebhookEvent['data']) {
  console.log("💰 Processing charge.success for:", data.reference);

  try {
    // This handles one-time payments and subscription activations
    if (data.plan && data.subscription) {
      // This is a subscription payment
      await handleSubscriptionPayment(data);
    } else {
      // This is a one-time payment - might be handled elsewhere
      console.log("💳 One-time payment processed:", data.reference);
    }
  } catch (error) {
    console.error("❌ Error handling charge.success:", error);
    throw error;
  }
}

async function handleSubscriptionCreate(data: PaystackWebhookEvent['data']) {
  console.log("🆕 Processing subscription.create for:", data.subscription_code);

  try {
    // Find pending subscription by transaction reference stored in metadata
    const subscription = await prisma.subscription.findFirst({
      where: {
        OR: [
          {
            metadata: {
              path: ['transactionRef'],
              equals: data.reference
            },
            status: "PENDING"
          },
          {
            subscriptionId: data.subscription_code,
            status: "PENDING"
          }
        ]
      },
    });

    if (subscription) {
      // Ensure we use the correct subscription_code
      const subscriptionCode = data.subscription_code || data.subscription?.subscription_code;
      
      console.log("📋 Subscription codes found:", {
        subscription_code: data.subscription_code,
        subscription_subscription_code: data.subscription?.subscription_code,
        subscription_code_final: subscriptionCode,
        email_token: data.email_token
      });
      
      if (!subscriptionCode) {
        console.error("❌ No subscription code found in webhook data:", JSON.stringify(data, null, 2));
        throw new Error("No subscription code found in webhook data");
      }
      
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          status: SubscriptionStatus.ACTIVE,
          subscriptionId: subscriptionCode,
          emailToken: data.email_token,
          metadata: {
            ...((subscription.metadata as any) || {}),
            paystackSubscriptionId: data.subscription?.id,
            activatedViaWebhook: true,
            activatedAt: new Date().toISOString(),
            originalReference: data.reference,
          },
        },
      });

      console.log("✅ Subscription activated via webhook:", subscription.id);
    } else {
      console.log("📝 Subscription created webhook received but no pending subscription found");
    }
  } catch (error) {
    console.error("❌ Error handling subscription.create:", error);
    throw error;
  }
}

async function handleSubscriptionPayment(data: PaystackWebhookEvent['data']) {
  console.log("💳 Processing subscription payment for:", data.subscription_code);

  try {
    // First check if this is a new subscription for a plan change
    const pendingSubscription = await prisma.subscription.findFirst({
      where: {
        metadata: {
          path: ["newSubscriptionReference"],
          equals: data.reference,
        },
        cancelAtPeriodEnd: true,
      },
    });

    if (pendingSubscription && pendingSubscription.pendingPlanType) {
      console.log("🔄 Processing plan change completion:", {
        oldSubscription: pendingSubscription.id,
        newPlan: pendingSubscription.pendingPlanType,
        reference: data.reference,
      });

      // Ensure we use the correct subscription_code
      const subscriptionCode = data.subscription_code || data.subscription?.subscription_code;
      
      if (!subscriptionCode) {
        console.error("❌ No subscription code found in payment webhook data:", JSON.stringify(data, null, 2));
        throw new Error("No subscription code found in payment webhook data");
      }
      
      // Update the subscription with the new plan details
      await prisma.subscription.update({
        where: { id: pendingSubscription.id },
        data: {
          planType: pendingSubscription.pendingPlanType,
          priceId: pendingSubscription.pendingPriceId || undefined,
          subscriptionId: subscriptionCode,
          status: SubscriptionStatus.ACTIVE,
          cancelAtPeriodEnd: false,
          emailToken: data.email_token,
          pendingPlanType: null,
          pendingPriceId: null,
          pendingPlanEffectiveDate: null,
          metadata: {
            ...((pendingSubscription.metadata as any) || {}),
            planChangeCompletedAt: new Date().toISOString(),
            lastPaymentAt: new Date().toISOString(),
            lastPaymentAmount: data.amount,
            lastPaymentReference: data.reference,
            paystackSubscriptionId: data.subscription?.id,
          },
        },
      });

      console.log("✅ Plan change completed successfully:", pendingSubscription.id);
      return;
    }

    // Handle regular subscription payment
     // Ensure we use the correct subscription_code
     const subscriptionCode = data.subscription_code || data.subscription?.subscription_code;
     
     if (!subscriptionCode) {
       console.error("❌ No subscription code found in regular payment webhook data:", JSON.stringify(data, null, 2));
       throw new Error("No subscription code found in regular payment webhook data");
     }
    
    const subscription = await prisma.subscription.findFirst({
      where: { subscriptionId: subscriptionCode },
    });

    if (subscription) {
      // Update subscription with latest payment info
      await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          status: SubscriptionStatus.ACTIVE,
          metadata: {
            ...((subscription.metadata as any) || {}),
            lastPaymentAt: new Date().toISOString(),
            lastPaymentAmount: data.amount,
            lastPaymentReference: data.reference,
          },
        },
      });

      console.log("✅ Subscription payment processed:", subscription.id);
    }
  } catch (error) {
    console.error("❌ Error handling subscription payment:", error);
    throw error;
  }
}

async function handleSubscriptionNotRenew(data: PaystackWebhookEvent['data']) {
  console.log("🔄 Processing subscription.not_renew for:", data.subscription_code);

  try {
    await prisma.subscription.update({
      where: { subscriptionId: data.subscription_code },
      data: {
        status: SubscriptionStatus.NON_RENEWING,
        cancelAtPeriodEnd: true,
        metadata: {
          cancelledBy: "paystack_webhook",
          cancelledAt: new Date().toISOString(),
          webhookEvent: "subscription.not_renew",
        },
      },
    });

    console.log("✅ Subscription marked as non-renewing via webhook");
  } catch (error) {
    console.error("❌ Error handling subscription.not_renew:", error);
    throw error;
  }
}

async function handleSubscriptionDisable(data: PaystackWebhookEvent['data']) {
  console.log("🛑 Processing subscription.disable for:", data.subscription_code);

  try {
    await prisma.subscription.update({
      where: { subscriptionId: data.subscription_code },
      data: {
        status: SubscriptionStatus.CANCELLED,
        canceledAt: new Date(),
        cancelAtPeriodEnd: false,
        metadata: {
          cancelledBy: "paystack_webhook",
          cancelledAt: new Date().toISOString(),
          webhookEvent: "subscription.disable",
        },
      },
    });

    console.log("✅ Subscription disabled via webhook");
  } catch (error) {
    console.error("❌ Error handling subscription.disable:", error);
    throw error;
  }
}

async function handleInvoiceCreate(data: PaystackWebhookEvent['data']) {
  console.log("📄 Processing invoice.create for:", data.subscription_code);
  // Handle invoice creation if needed
}

async function handleInvoicePaymentFailed(data: PaystackWebhookEvent['data']) {
  console.log("❌ Processing invoice.payment_failed for:", data.subscription_code);

  try {
    await prisma.subscription.update({
      where: { subscriptionId: data.subscription_code },
      data: {
        status: SubscriptionStatus.ATTENTION,
        metadata: {
          lastFailedPaymentAt: new Date().toISOString(),
          lastFailedPaymentReason: data.gateway_response || "Payment failed",
        },
      },
    });

    console.log("⚠️ Subscription marked as requiring attention due to failed payment");
  } catch (error) {
    console.error("❌ Error handling invoice.payment_failed:", error);
    throw error;
  }
}

async function handleInvoiceUpdate(data: PaystackWebhookEvent['data']) {
  console.log("📝 Processing invoice.update for:", data.subscription_code);
  // Handle invoice updates if needed
}