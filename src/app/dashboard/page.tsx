"use client";

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import { useDashboardStore } from "@/store/dashboardStore";
import { useRecentTransformationsStore } from "@/store/recentTransformationsStore"; // Added

import { Stats } from "@/components/dashboard/Stats";
import { RecentTransformations } from "@/components/dashboard/RecentTransformations";

import { Loader2, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function DashboardPage() {
  const router = useRouter();
  const { user } = useAuthStore();
  const {
    isLoading: isDashboardLoading,
    error: dashboardError,
    fetchDashboardData,
  } = useDashboardStore();
  const { startPolling: startRecentPolling } = useRecentTransformationsStore();
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (!user) {
      router.push("/auth");
      return;
    }

    fetchDashboardData(user.id, true);

    startRecentPolling();
  }, [user, router, fetchDashboardData, startRecentPolling]);

  if (!user) return null;

  if (dashboardError) {
    return (
      <div className="h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-destructive">
              Error Loading Dashboard Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p>{dashboardError}</p>
            <Button
              onClick={() => user && fetchDashboardData(user.id, true)}
              className="mt-4"
              variant="outline"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isDashboardLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4">
          <div className="bg-primary/10 p-4 rounded-full mx-auto">
            <Loader2 className="w-12 h-12 animate-spin text-primary" />
          </div>
          <p className="text-lg font-semibold">Loading your dashboard...</p>
          <p className="text-sm text-muted-foreground">
            Preparing your learning materials
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col min-h-screen overflow-y-auto bg-background">
      <main className="flex-1 flex flex-col w-[90%] max-w-7xl mx-auto gap-6 py-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-2">
          <div>
            <h1 className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
              Dashboard
            </h1>
            <p className="text-muted-foreground">
              Welcome back to your learning journey
            </p>
          </div>
          <Button
            onClick={() => router.push("/new")}
            className="shadow-md bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            New Transformation
          </Button>
        </div>

        <Stats />

        <Tabs
          defaultValue="overview"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="hidden">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6 mt-6">
            <RecentTransformations />
          </TabsContent>

          <TabsContent value="insights" className="space-y-6 mt-6">
            <RecentTransformations />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
