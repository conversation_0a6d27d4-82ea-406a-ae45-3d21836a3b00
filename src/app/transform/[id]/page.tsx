"use client";

import { useEffect, useRef } from "react";
import { useContentStore } from "@/store/contentStore";
import {
  type ContentType,
  type OutputType,
  type ContentMetadata,
  type ContentSegment,
} from "@/types/contentTypes";
import { useTextStore } from "@/store/textStore";
import { RightSidebar } from "@/components/layout/right-sidebar";
import { MainContent } from "@/components/main-content";
import { useAuthStore } from "@/store/authStore";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";


export default function TransformPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { user } = useAuthStore();
  const { text } = useTextStore();

  const contentId = Array.isArray(params?.id) ? params.id[0] : params?.id || "";
  const contentType = searchParams.get("type") || "youtube";
  const outputType = searchParams.get("output") || "summary";

  const {
    setSourceContent,
    setOutputContent,
    setError,
    setMetadata,
    setSourceLoading,
    setOutputLoading,
    resetState,
    setContentType,
    setContentId,
    setOutputType,
    setIsNewTransformation,
  } = useContentStore();

  const loadedContentRef = useRef<string | null>(null);


  useEffect(() => {
    if (loadedContentRef.current === contentId) {
      return;
    }
    loadedContentRef.current = contentId;
    resetState();
    setContentId(contentId);
    setContentType(contentType as ContentType);
    setOutputType(outputType as OutputType);

    // Don't set isNewTransformation yet - we'll determine this after checking
    // if the content already exists in the database

    const fetchData = async () => {
      if (!user) {
        router.push("/auth");
        return;
      }

      const checkExistingOutput = async () => {
        try {
          // Use the efficient content lookup API to check if content exists
          const lookupResponse = await fetch(`/api/content-lookup`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              contentId,
              contentType,
              includeOriginalContent: true,
            }),
          });

          const lookupData = await lookupResponse.json();

          if (lookupData.found && lookupData.content) {
            console.log(
              "Found existing content:",
              lookupData.content.contentId
            );

            // Use updateState to batch state updates
            const store = useContentStore.getState();
            const { updateState } = store;

            updateState({
              outputContent: lookupData.content.content,
              metadata: {
                contentId: lookupData.content.contentId,
                contentType: lookupData.content.contentType.toLowerCase(),
                title: lookupData.content.title,
                sourceId: lookupData.content.sourceId || "",
              } as ContentMetadata,
              isNewTransformation: false, // This is existing content
            });

            try {
              // Process the original content
              const originalContent = lookupData.content.originalContent;
              let parsedSource;

              if (lookupData.content.parsedOriginalContent) {
                // If the API already parsed it for us
                parsedSource = lookupData.content.parsedOriginalContent;
              } else {
                try {
                  parsedSource = JSON.parse(originalContent);
                } catch (parseError) {
                  const firstChar = originalContent.trim()[0];
                  if (firstChar === "[" || firstChar === "{") {
                    console.warn(
                      "Malformed JSON in originalContent, using as text:",
                      parseError
                    );
                  } else {
                    console.info(
                      "originalContent is not JSON, using as plain text"
                    );
                  }
                  setSourceContent(originalContent);
                  return true;
                }
              }

              if (Array.isArray(parsedSource)) {
                setSourceContent(parsedSource);
              } else if (
                typeof parsedSource === "object" &&
                parsedSource.content
              ) {
                setSourceContent(parsedSource.content);
                setMetadata({
                  contentId: contentId,
                  contentType: lookupData.content.contentType.toLowerCase(),
                  title: lookupData.content.title,
                  sourceId: lookupData.content.sourceId || "",
                  author: parsedSource.author,
                  url: parsedSource.url,
                });
              } else if (typeof parsedSource === "string") {
                setSourceContent(parsedSource);
              } else {
                setSourceContent(JSON.stringify(parsedSource));
              }
            } catch (error) {
              console.error("Failed to process source content:", error);
              setSourceContent(lookupData.content.originalContent);
            }
            return true;
          }
          return false;
        } catch (error) {
          console.error("Error checking existing output:", error);
          return false;
        }
      };

      const loadContentData = async () => {
        setSourceLoading(true);
        setOutputLoading(true);
        setOutputContent("");
        setError("");

        // This is definitely new content being processed since we didn't find it in checkExistingOutput
        setIsNewTransformation(true);

        try {
          if (contentType === "youtube") {
            console.log("Processing YouTube transcript for:", contentId);

            // Fetch transcript from API
            console.log("Fetching YouTube transcript from API");
            const transcriptResponse = await fetch("/api/transcript", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                url:
                  contentId.length === 11
                    ? `https://www.youtube.com/watch?v=${contentId}`
                    : contentId,
                contentType: "youtube",
              }),
            });

            if (!transcriptResponse.ok) {
              const data = await transcriptResponse.json();
              throw new Error(data.error || "Failed to fetch transcript");
            }

            const transcriptData = await transcriptResponse.json();

            console.log("Processing transcript data:", {
              hasTranscript: Boolean(transcriptData.transcript),
              transcriptLength: transcriptData.transcript?.length || 0,
              metadata: transcriptData.metadata,
            });

            setMetadata({
              ...transcriptData.metadata,
              contentId,
              contentType: "youtube",
              sourceId: contentId,
            });

            if (
              !transcriptData.transcript ||
              transcriptData.transcript.length === 0
            ) {
              setError("This content does not have a transcript available.");
              setSourceContent([]);
              setOutputLoading(false);
              return;
            }

            const validTranscript = transcriptData.transcript.filter(
              (segment: ContentSegment) =>
                segment &&
                typeof segment === "object" &&
                "text" in segment &&
                segment.text
            );

            console.log(
              "Setting source content with",
              validTranscript.length,
              "transcript segments"
            );
            setSourceContent(validTranscript);

            await new Promise((resolve) => setTimeout(resolve, 100));
          } else if (contentType === "text") {
            if (!text) {
              try {
                const response = await fetch(`/api/text/${contentId}`);
                if (response.ok) {
                  const data = await response.json();
                  if (data.text) {
                    setSourceContent(data.text);
                    setMetadata({
                      contentId,
                      contentType: "text",
                      title: "",
                      sourceId: contentId,
                    });
                  } else {
                    throw new Error("No text content found");
                  }
                } else {
                  throw new Error("Failed to fetch text content");
                }
              } catch (error) {
                console.error("Error fetching text content:", error);
                setError("Please enter text to transform");
                setSourceContent("");
              }
            } else {
              setSourceContent(text);
              setMetadata({
                contentId,
                contentType: "text",
                title: "",
                sourceId: contentId,
              });
            }
          } else if (contentType === "pdf") {
            try {
              console.log("Fetching PDF content for:", contentId);

              // Add timeout handling for PDF content fetching
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

              try {
                const response = await fetch(`/api/pdf/${contentId}`, {
                  signal: controller.signal,
                });

                clearTimeout(timeoutId);

                if (response.ok) {
                  let data;
                  try {
                    data = await response.json();
                  } catch (jsonError) {
                    console.error(
                      "Error parsing PDF response as JSON:",
                      jsonError
                    );
                    throw new Error(
                      "Failed to parse PDF content - the response may be incomplete"
                    );
                  }

                  if (data.text) {
                    console.log(
                      "Successfully loaded PDF content, length:",
                      data.text.length
                    );
                    setSourceContent(data.text);
                    setMetadata({
                      ...data.metadata,
                      contentId,
                      contentType: "pdf",
                      sourceId: contentId,
                    });
                  } else {
                    throw new Error("No PDF content found in the response");
                  }
                } else {
                  // Try to parse error response
                  let errorMessage = "Failed to fetch PDF content";
                  try {
                    const errorData = await response.json();
                    errorMessage = errorData.error || errorMessage;
                  } catch (parseError) {
                    console.error(
                      "Failed to parse error response:",
                      parseError
                    );
                  }

                  throw new Error(errorMessage);
                }
              } catch (fetchError) {
                clearTimeout(timeoutId);

                // Handle timeout specifically
                if (
                  fetchError instanceof Error &&
                  fetchError.name === "AbortError"
                ) {
                  throw new Error(
                    "PDF content request timed out. The file may be too large."
                  );
                }

                throw fetchError;
              }
            } catch (error) {
              console.error("Error fetching PDF content:", error);

              // Provide more specific error messages
              if (error instanceof Error) {
                if (error.message.includes("JSON")) {
                  setError(
                    "There was a problem processing the PDF content. It may be too large or contain unsupported formatting."
                  );
                } else if (error.message.includes("timed out")) {
                  setError(
                    "The PDF content request timed out. The file may be too large to process."
                  );
                } else {
                  setError(error.message);
                }
              } else {
                setError("Failed to load PDF content");
              }

              setSourceContent("");
            }
          } else if (contentType === "webpage") {
            try {
              const response = await fetch(`/api/webpage/${contentId}`);
              if (response.ok) {
                const data = await response.json();
                if (data.text) {
                  setSourceContent(data.text);
                  setMetadata({
                    ...data.metadata,
                    contentId,
                    contentType: "webpage",
                    sourceId: data.metadata.sourceId || contentId,
                    title: data.metadata.title || "Webpage Content",
                    author: data.metadata.author,
                    url: data.metadata.url || data.metadata.sourceId,
                  });
                } else {
                  throw new Error("No webpage content found");
                }
              } else {
                const errorData = await response.json();
                throw new Error(
                  errorData.error || "Failed to fetch webpage content"
                );
              }
            } catch (error) {
              console.error("Error fetching webpage content:", error);
              setError(
                error instanceof Error
                  ? error.message
                  : "Failed to load webpage content"
              );
              setSourceContent("");
            }
          } else {
            setMetadata({
              contentId,
              contentType: contentType as ContentType,
              title: "",
              sourceId: contentId,
            });

            setError(
              `${
                contentType.charAt(0).toUpperCase() + contentType.slice(1)
              } content processing coming soon`
            );
            setSourceContent("");
          }

          setOutputLoading(false);
        } catch (error) {
          console.error("Error loading content data:", error);
          setError(
            error instanceof Error
              ? error.message
              : "An error occurred while loading the content data"
          );
        } finally {
          setSourceLoading(false);
        }
      };

      try {
        const existingOutput = await checkExistingOutput();
        if (!existingOutput) {
          await loadContentData();
        } else {
          setOutputLoading(false);
          setSourceLoading(false);
        }
      } catch (err) {
        console.error("Content loading error:", err);
        setError("Failed to load content");
        setOutputLoading(false);
        setSourceLoading(false);
      }
    };

    fetchData();
  }, [
    contentId,
    contentType,
    outputType,
    user,
    router,
    resetState,
    setOutputContent,
    setMetadata,
    setSourceContent,
    setError,
    setSourceLoading,
    setOutputLoading,
    setContentType,
    setContentId,
    setOutputType,
    setIsNewTransformation,
    text,
  ]);

  if (!user) {
    return null;
  }

  return (
    <div className="flex h-screen overflow-hidden bg-background">
      <ResizablePanelGroup
        direction="horizontal"
        className="flex-1 overflow-hidden"
      >
        <ResizablePanel defaultSize={65} minSize={45}>
          <MainContent contentId={contentId} />
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel
          defaultSize={35}
          minSize={30}
          className="hidden md:block overflow-hidden"
        >
          <div className="flex flex-col h-full">
            <div className="flex-1 overflow-hidden">
              <RightSidebar />
            </div>
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}
