// app/auth/page.tsx
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Turnstile } from "@marsidev/react-turnstile";
import {
  GoogleSignInButton,
  EmailSignInForm,
} from "@/components/auth/AuthButtons";
import { useAuthStore } from "@/store/authStore";

export default function AuthPage() {
  const router = useRouter();
  const { user } = useAuthStore();

  const [captchaToken, setCaptchaToken] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      router.replace("/");
    }
  }, [user, router]);

  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col items-center justify-center">
      <Card className="w-full max-w-md p-8">
        <div className="space-y-6">
          <div className="space-y-2 text-center">
            <h1 className="text-3xl font-bold">Welcome to Qlipify</h1>
            <p className="text-muted-foreground">
              Sign in to start learning smarter.
            </p>
          </div>

          <GoogleSignInButton />

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or continue with
              </span>
            </div>
          </div>
          <Turnstile
            siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY!}
            onSuccess={(token) => setCaptchaToken(token)}
            options={{
              action: "sign-in",
              theme: "light",
              size: "normal",
            }}
            className="flex justify-center w-full py-2"
          />

          <EmailSignInForm captchaToken={captchaToken || undefined} />
        </div>
      </Card>
    </div>
  );
}
