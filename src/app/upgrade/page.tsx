"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2, Globe } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { PlanType } from "@/data/pricingData";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useAuthStore } from "@/store/authStore";
import {
  getLocationBasedPricing,
  formatPrice,
  LocationPricingData,
} from "@/lib/location-pricing";

export default function UpgradePage() {
  const [paymentOption, setPaymentOption] = useState<"monthly" | "annual">(
    "monthly"
  );
  const [loading, setLoading] = useState<string | null>(null);
  const [pricingData, setPricingData] = useState<LocationPricingData | null>(
    null
  );
  const [loadingPricing, setLoadingPricing] = useState(true);
  const { user } = useAuthStore();
  const { createSubscription } = useSubscriptionStore();

  // Load location-based pricing on component mount
  useEffect(() => {
    const loadPricing = async () => {
      try {
        setLoadingPricing(true);
        const locationPricing = await getLocationBasedPricing();
        setPricingData(locationPricing);
      } catch (error) {
        console.error("Failed to load pricing data:", error);
        toast.error("Failed to load pricing. Please refresh the page.");
      } finally {
        setLoadingPricing(false);
      }
    };

    loadPricing();
  }, []);

  const handleProceed = async (plan: PlanType) => {
    if (plan === "Free") return;

    if (!user) {
      toast.error("Please log in to upgrade your plan");
      return;
    }

    setLoading(plan);

    try {
      const customerInfo = {
        email: user.email || "",
        name:
          user.user_metadata?.full_name || user.email?.split("@")[0] || "User",
      };

      console.log("Creating subscription with customer info:", customerInfo);

      const result = await createSubscription(
        plan,
        paymentOption,
        customerInfo
      );

      // Redirect to Paystack checkout page
      if (result.success && result.paymentLink) {
        window.location.href = result.paymentLink;
      } else {
        throw new Error("Failed to get payment link");
      }
    } catch (error) {
      console.error("Subscription creation error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "An error occurred. Please try again."
      );
    } finally {
      setLoading(null);
    }
  };

  // Show loading state while pricing data is being fetched
  if (loadingPricing || !pricingData) {
    return (
      <div className="container w-[90%] mx-auto py-12 md:py-16">
        <div className="text-center mb-5">
          <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-3">
            Upgrade Your Plan
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Loading pricing information...
          </p>
        </div>
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="container w-[90%] mx-auto py-12 md:py-16">
      <div className="text-center mb-5">
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight mb-3">
          Upgrade Your Plan
        </h1>
        <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
          Choose the perfect plan to unlock more features and capabilities
        </p>
        <div className="flex items-center justify-center gap-2 mt-2">
          <Globe className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm text-muted-foreground">
            Pricing in {pricingData.currency} (
            {pricingData.region === "nigeria" ? "Nigeria" : "International"})
          </span>
        </div>
      </div>

      {/* Billing toggle */}
      <div className="flex justify-center mb-12">
        <div className="bg-muted rounded-full p-1 flex items-center w-full max-w-[300px]">
          <button
            onClick={() => setPaymentOption("monthly")}
            className={cn(
              "rounded-full px-6 py-2 text-sm font-medium transition-all flex-1",
              paymentOption === "monthly"
                ? "bg-background shadow-sm text-foreground"
                : "text-muted-foreground hover:bg-muted-foreground/10"
            )}
          >
            Monthly
          </button>
          <button
            onClick={() => setPaymentOption("annual")}
            className={cn(
              "rounded-full px-6 py-2 text-sm font-medium transition-all flex items-center flex-1 justify-center",
              paymentOption === "annual"
                ? "bg-background shadow-sm text-foreground"
                : "text-muted-foreground hover:bg-muted-foreground/10"
            )}
          >
            Annual
            <Badge
              variant="secondary"
              className="ml-2 py-0 h-5 text-xs bg-primary/10 text-primary"
            >
              Save {pricingData.discountPercent}%
            </Badge>
          </button>
        </div>
      </div>

      {/* Pricing cards - in a grid with proper spacing */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full mx-auto">
        {/* Dynamically generate plan cards - filter out Free plan */}
        {(Object.keys(pricingData.plans) as PlanType[])
          .filter((plan) => plan !== "Free")
          .map((planName) => {
            const plan = pricingData.plans[planName];
            return (
              <Card
                key={planName}
                className="border overflow-hidden relative w-full h-full flex flex-col"
              >
                {plan.isPopular && (
                  <div className="absolute top-0 right-0">
                    <Badge className="rounded-tl-none rounded-br-none rounded-tr-md rounded-bl-md bg-primary text-primary-foreground">
                      Popular
                    </Badge>
                  </div>
                )}
                <CardHeader className={cn("p-6", plan.color)}>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold">{planName}</h3>
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold">
                        {formatPrice(
                          plan[paymentOption],
                          pricingData.currency,
                          pricingData.symbol
                        )}
                      </span>
                      <span className="text-sm text-muted-foreground ml-2">
                        /{paymentOption === "monthly" ? "month" : "year"}
                      </span>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-6 space-y-6 flex-grow">
                  <Button
                    variant={plan.buttonVariant}
                    className="w-full"
                    onClick={() => handleProceed(planName)}
                    disabled={loading === planName}
                  >
                    {loading === planName ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      `Upgrade to ${planName}`
                    )}
                  </Button>

                  <div className="space-y-4">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <Check
                          className={cn(
                            "h-5 w-5 mr-3 shrink-0",
                            plan.iconColor
                          )}
                        />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            );
          })}
      </div>
    </div>
  );
}
