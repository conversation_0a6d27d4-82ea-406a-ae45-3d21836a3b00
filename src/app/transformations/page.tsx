"use client";

import { useEffect } from "react";
import { ContentSection } from "@/components/transformations/ContentSection";
import { useTransformationsStore } from "@/store/transformationsStore";
import { Button } from "@/components/ui/button";
import { Sparkles } from "lucide-react";
import { useRouter } from "next/navigation";
import { PageSkeleton } from "@/components/transformations/PageSkeleton";

const TransformationsPage = () => {
  const { isLoading, fetchInitialContent, resetDataFetchedFlag } =
    useTransformationsStore();
  const router = useRouter();

  useEffect(() => {
    // Initial fetch when the page loads
    fetchInitialContent();

    // Set up interval for periodic updates - only after 5 minutes of inactivity
    const intervalId = setInterval(() => {
      resetDataFetchedFlag(); // This will trigger a refetch on next user interaction
    }, 300000); // Check every 5 minutes (300 seconds) - reduced frequency

    // Add focus event listener to refresh data when user returns to tab
    const handleFocus = () => {
      fetchInitialContent();
    };

    window.addEventListener("focus", handleFocus);

    return () => {
      clearInterval(intervalId);
      window.removeEventListener("focus", handleFocus);
    };
  }, [fetchInitialContent, resetDataFetchedFlag]);

  return (
    <div className="flex-1 flex flex-col min-h-screen overflow-y-auto bg-background">
      <main className="flex-1 flex flex-col w-[95%] max-w-7xl mx-auto gap-8 py-8">
        <div className="flex flex-col md:flex-row gap-4 justify-between items-start md:items-center">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
              Your Transformations
            </h1>
            <p className="text-muted-foreground text-base">
              Access summaries, quizzes, and learning materials from your
              content
            </p>
          </div>

          <Button
            onClick={() => router.push("/new")}
            size="lg"
            className="shadow-md bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            New Transformation
          </Button>
        </div>

        {isLoading && !useTransformationsStore.getState().content.length ? (
          <PageSkeleton />
        ) : (
          <ContentSection />
        )}
      </main>
    </div>
  );
};

export default TransformationsPage;
