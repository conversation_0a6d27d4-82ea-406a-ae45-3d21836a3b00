"use client";

import { useState } from "react";
import { Settings } from "lucide-react"; 
import { SignOutModal } from "@/components/common/SignOutModal";
import { SubscriptionCard } from "@/components/subscription/SubscriptionCard";
import { BillingHistory } from "@/components/subscription/BillingHistory";
import AccountDetails from "@/components/auth/AccountDetails";

export default function AccountPage() {
  const [showSignOutModal, setShowSignOutModal] = useState(false);

  const handleSignOut = () => {
    setShowSignOutModal(true);
  };

  return (
    <>
     
      <div className="flex flex-col h-full bg-background">
        {" "}
        {/* Changed to h-full, assuming parent provides height context */}
        <main className="flex-1 w-full overflow-y-auto">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
            {" "}
            {/* Increased max-width and padding */}
            {/* Page Header */}
            <div className="mb-10">
              {" "}
              {/* Increased bottom margin */}
              <h1 className="text-3xl font-bold tracking-tight text-foreground flex items-center gap-3">
                <Settings className="h-7 w-7 text-primary" />{" "}
                {/* Larger icon */}
                Account Settings
              </h1>
              <p className="mt-2 text-base text-muted-foreground">
                Manage your profile information, subscription plan, and billing
                details.
              </p>
            </div>
            {/* Main Content Grid */}
            <div className="space-y-6">
              {/* Account Details and Subscription Cards - Side by side on large screens */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <AccountDetails onSignOut={handleSignOut} />
                <SubscriptionCard  />
              </div>

              {/* Billing History Section - Full width below */}
              <div className="w-full">
                <BillingHistory />
              </div>
            </div>
          </div>
        </main>
      </div>

      <SignOutModal
        open={showSignOutModal}
        onOpenChange={setShowSignOutModal}
      />
    </>
  );
}
