import type { <PERSON><PERSON><PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { Analytics } from "@vercel/analytics/react";
import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "sonner";
import AuthProvider from "@/components/auth/AuthProvider";
import ClientLayout from "@/components/layout/ClientLayout";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { PostHogProvider } from "@/components/PostHogProvider";

// Optimize and preload fonts
const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
  preload: true,
});
const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
  preload: true,
});

// Structured Data JSON-LD for SoftwareApplication
const softwareAppSchema = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  name: "Qlipify",
  url: "https://www.qlipify.com/",
  applicationCategory: "Education",
  description:
    "AI-powered platform to summarize YouTube videos, PDFs, webpages, and text into study materials.",
  operatingSystem: "Web-based",
  offers: {
    "@type": "Offer",
    price: "0.00",
    priceCurrency: "USD",
    url: "https://www.qlipify.com/pricing",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
};

export const metadata: Metadata = {
  metadataBase: new URL("https://www.qlipify.com"),
  title: "Qlipify | Learn  3x Faster with AI-Powered Study Tools",
  description:
    "Instantly summarize YouTube videos, PDFs, webpages, and text into concise notes, flashcards, and quizzes to learn smarter.",
  keywords: [
    "summarize YouTube",
    "AI summaries",
    "study tools",
    "flashcards generator",
    "quiz maker",
    "learning platform",
    "AI-powered learning platforms",
    "AI learning tools",
    "AI study tools",
    "summarize research papers",
  ],

  openGraph: {
    title: "Qlipify | AI Study Assistant",
    description:
      "Transform any content—videos, PDFs, webpages—into effective study materials with AI-powered summaries and quizzes.",
    url: "https://www.qlipify.com",
    siteName: "Qlipify",
    images: [
      {
        url: "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WAGQDLkMS90QiRB58T2GplsWwHdgFahNjc6rD",
        width: 1200,
        height: 630,
        alt: "Qlipify - AI Study Assistant",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  icons: {
    icon: [
      { url: "/favicon.ico" },
      {
        url: "/favicon.ico",
        type: "image/png",
      },
    ],
    apple: [{ url: "/apple-icon.png" }],
  },
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${geistSans.variable} ${geistMono.variable}`}>
      <head>
        {/* Preconnect and Prefetch */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          rel="dns-prefetch"
          href={process.env.NEXT_PUBLIC_SUPABASE_URL || ""}
        />
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          // eslint-disable-next-line react/no-danger
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(softwareAppSchema),
          }}
        />
      </head>
      <body className="antialiased">
        <PostHogProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem={true}
            disableTransitionOnChange
          >
            <AuthProvider>
              <ClientLayout>{children}</ClientLayout>
              <Toaster richColors position="top-center" />
            </AuthProvider>
          </ThemeProvider>
          {/* Analytics and Performance at end */}
          <Analytics />
          <SpeedInsights />
        </PostHogProvider>
      </body>
    </html>
  );
}
