import { getBlogPostBySlug, getRelatedPosts } from "@/data/blog-posts.js";
import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Calendar, Clock, ArrowLeft } from "lucide-react";
import { Metadata } from "next";
import BlogPostCard from "@/components/blog/BlogPostCard";

// Define params type as a Promise
type Params = Promise<{ slug: string }>;

export async function generateMetadata({
  params,
}: {
  params: Params;
}): Promise<Metadata> {
  const { slug } = await params;
  const post = getBlogPostBySlug(slug);

  if (!post) {
    return {
      title: "Blog Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  return {
    title: `${post.title} | Qlipify Blog`,
    description: post.description,
    keywords: post.tags.join(", "),
    openGraph: {
      title: post.title,
      description: post.description,
      type: "article",
      publishedTime: post.date,
      authors: [post.author],
      tags: post.tags,
      images: [
        {
          url: post.coverImage,
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
    },
  };
}

export default async function BlogPost({ params }: { params: Params }) {
  const { slug } = await params;
  const post = getBlogPostBySlug(slug);

  if (!post) {
    notFound();
  }

  const relatedPosts = getRelatedPosts(post.id, 2);

  return (
    <div className="min-h-screen bg-background">
      <div className="container max-w-4xl mx-auto py-16 px-4">
        {/* Back to blog link */}
        <Link
          href="/blog"
          className="inline-flex items-center text-sm text-foreground/70 hover:text-primary mb-8 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to all articles
        </Link>

        {/* Article header */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 mb-4">
            {post.tags.map((tag) => (
              <span
                key={tag}
                className="text-xs px-3 py-1 rounded-full bg-primary/10 text-primary"
              >
                {tag}
              </span>
            ))}
          </div>

          <h1 className="text-3xl md:text-4xl font-bold mb-4">{post.title}</h1>

          <div className="flex items-center justify-between flex-wrap gap-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="relative h-12 w-12 rounded-full overflow-hidden">
                <Image
                  src={post.authorImage}
                  alt={post.author}
                  width={48}
                  height={48}
                  className="object-cover w-full h-full"
                  sizes="48px"
                  unoptimized
                />
              </div>
              <div>
                <p className="font-medium">{post.author}</p>
                <p className="text-xs text-foreground/60">{post.authorRole}</p>
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-foreground/60">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>
                  {new Date(post.date).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </span>
              </div>

              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{post.readTime} min read</span>
              </div>
            </div>
          </div>
        </div>

        {/* Featured image */}
        <div className="relative h-[400px] w-full rounded-xl overflow-hidden mb-10">
          <Image
            src={post.coverImage}
            alt={post.title}
            fill
            className="object-cover"
            priority
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
            quality={90}
            unoptimized
          />
        </div>

        {/* Article content - This would be your actual blog content */}
        <div className="prose prose-lg dark:prose-invert max-w-none mb-16">
          <p>
            In today&apos;s fast-paced digital world, the way we consume and
            learn from content is rapidly evolving. With the rise of online
            education and the explosion of video content, students and
            professionals alike are facing a new challenge: how to efficiently
            extract and retain knowledge from hours of video material.
          </p>

          <p>
            This is where artificial intelligence is making a revolutionary
            impact. AI-powered tools like Qlipify are transforming how we
            interact with educational videos, turning passive watching into
            active learning experiences.
          </p>

          <h2>The Problem with Traditional Video Learning</h2>

          <p>
            Traditional video learning often suffers from several key
            limitations:
          </p>

          <ul>
            <li>
              Time inefficiency - watching hours of content for potentially
              minutes of relevant information
            </li>
            <li>
              Passive consumption - leading to poor retention and understanding
            </li>
            <li>
              Difficulty in revisiting specific information without rewatching
              entire videos
            </li>
            <li>Lack of interactive elements that reinforce learning</li>
          </ul>

          <p>
            These limitations can make video learning frustrating and
            ineffective, despite the wealth of knowledge available in video
            format.
          </p>

          <h2>How AI is Changing the Game</h2>

          <p>
            AI technologies are addressing these challenges through several
            innovative approaches:
          </p>

          <h3>1. Intelligent Content Summarization</h3>

          <p>
            Advanced natural language processing algorithms can now analyze
            video transcripts and extract the most important concepts, facts,
            and ideas. This allows learners to quickly grasp the core content
            without watching the entire video.
          </p>

          <h3>2. Interactive Learning Materials</h3>

          <p>
            AI can transform passive video content into interactive quizzes,
            flashcards, and study guides that promote active recall—a proven
            technique for improving retention and understanding.
          </p>

          <h3>3. Personalized Learning Paths</h3>

          <p>
            By analyzing learning patterns and content, AI can suggest
            personalized learning paths that focus on areas where individual
            learners need the most support.
          </p>

          <h2>Real-World Impact</h2>

          <p>
            The impact of these AI-powered learning tools is already being felt
            across educational institutions and professional development
            programs:
          </p>

          <ul>
            <li>Students report saving 5-10 hours per week on study time</li>
            <li>
              Comprehension and retention rates have improved by up to 70%
            </li>
            <li>
              Educators can create more effective supplementary materials in
              less time
            </li>
            <li>
              Professionals can stay current in their fields without sacrificing
              valuable time
            </li>
          </ul>

          <h2>The Future of AI-Powered Learning</h2>

          <p>
            As AI technology continues to evolve, we can expect even more
            sophisticated learning tools that:
          </p>

          <ul>
            <li>Provide real-time feedback during the learning process</li>
            <li>
              Adapt content presentation based on individual learning styles
            </li>
            <li>
              Integrate with virtual reality for immersive learning experiences
            </li>
            <li>
              Connect learners with similar interests for collaborative study
            </li>
          </ul>

          <p>
            The revolution in video learning is just beginning, and the
            possibilities for enhancing how we learn from video content are
            virtually limitless.
          </p>

          <h2>Conclusion</h2>

          <p>
            AI-powered tools like Qlipify are not just changing how we consume
            video content—they&apos;re transforming how we learn. By making
            learning more efficient, interactive, and personalized, these
            technologies are helping learners make the most of the vast
            educational resources available online.
          </p>

          <p>
            As we continue to navigate an increasingly complex and
            information-rich world, these AI learning assistants will become
            essential tools for students, professionals, and lifelong learners
            alike.
          </p>
        </div>

        {/* Related posts */}
        {relatedPosts.length > 0 && (
          <div className="border-t border-border pt-10">
            <h3 className="text-2xl font-bold mb-6">Related Articles</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {relatedPosts.map((relatedPost) => (
                <BlogPostCard key={relatedPost.id} post={relatedPost} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
