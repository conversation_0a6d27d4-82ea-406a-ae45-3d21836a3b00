# Subscriptions

## In a nutshell

The Subscriptions API lets developers embed recurring billing functionality in their applications, without having to manage the billing cycle themselves. Merchants can easily create plans and charge customers automatically, on a recurring basis.

---

## Here is how to set up a subscription:

1. Create a plan  
2. Create a subscription  
3. Listen for subscription events  

---

## Create a plan

Plans are the foundational building block for subscriptions. A plan represents what you're selling, how much you're selling it for, and how often you're charging for it.

You can create a plan via the Paystack Dashboard, or by calling the create plan API endpoint, passing:

| Param   | Type    | Description |
|---------|---------|-------------|
| name    | string  | The name of the plan |
| interval| string  | The interval at which to charge subscriptions on this plan. Available options are hourly, daily, weekly, monthly, quarterly, biannually (every 6 months), and annually |
| amount  | integer | The amount to charge |

#### Node

```js
const https = require('https')

const params = JSON.stringify({
  "name": "Monthly Retainer",
  "interval": "monthly",
  "amount": 500000
})

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/plan',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
    'Content-Type': 'application/json'
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.write(params)
req.end()
```

```json
{
  "status": true,
  "message": "Plan created",
  "data": {
    "name": "Monthly Retainer",
    "interval": "monthly",
    "amount": 500000,
    "integration": 428626,
    "domain": "test",
    "currency": "NGN",
    "plan_code": "PLN_u4cqud8vabi89hx",
    "invoice_limit": 0,
    "send_invoices": true,
    "send_sms": true,
    "hosted_page": false,
    "migrate": false,
    "id": 49122,
    "createdAt": "2020-05-22T12:36:12.333Z",
    "updatedAt": "2020-05-22T12:36:12.333Z"
  }
}
```

---

## Monthly Subscription Billing

Billing for subscriptions with a monthly interval depends on the day of the month the subscription was created. If the subscription was created on or before the 28th of the month, it gets billed on the same day, every month, for the duration of the plan. Subscriptions created on or between the 29th - 31st will get billed on the 28th of every subsequent month.

You can also pass `invoice_limit`, which lets you set how many times a customer can be charged on this plan.

---

## Create a subscription

Now that we have a plan, we can move on to the next step: subscribing a customer to that plan. There are a couple of ways we can go about creating a new subscription.

- Adding Plan code to a transaction  
- Using the create subscription API endpoint  

### Adding plan code to a transaction

You can create a subscription for a customer using the initialize transaction API endpoint by adding the `plan_code` of a plan to the body of your request.

#### Node

```js
const https = require('https')

const params = JSON.stringify({
  "email": "<EMAIL>",
  "amount": "500000",
  "plan": "PLN_xxxxxxxxxx"
})

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/transaction/initialize',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
    'Content-Type': 'application/json'
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.write(params)
req.end()
```

```json
{
  "status": true,
  "message": "Authorization URL created",
  "data": {
    "authorization_url": "https://checkout.paystack.com/nkdks46nymizns7",
    "access_code": "nkdks46nymizns7",
    "reference": "nms6uvr1pl"
  }
}
```

---

### Using the create subscription endpoint

You can also create a subscription by calling the create subscription API endpoint, passing a `customer` and `plan`.

#### Node

```js
const https = require('https')

const params = JSON.stringify({
  "customer": "CUS_xxxxxxxxxx",
  "plan": "PLN_xxxxxxxxxx"
})

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/transaction/initialize',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
    'Content-Type': 'application/json'
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.write(params)
req.end()
```

```json
{
  "status": true,
  "message": "Subscription successfully created",
  ...
}
```

---

## Listen for subscription events

Creating a subscription will result in Paystack sending the following events:

- `subscription.create`  
- `charge.success` (if using plan code in transaction)  
- `invoice.create`  
- `invoice.payment_failed`  
- `invoice.update`  
- `subscription.not_renew`  
- `subscription.disable`  

```json
{
  "event": "invoice.create",
  "data": {
    "domain": "test",
    ...
  }
}
```

---

## Managing subscriptions

### Understanding subscription statuses

| Status       | Description |
|--------------|-------------|
| active       | Currently active, will be charged on the next payment date |
| non-renewing | Active, but won't be charged again |
| attention    | Active but payment issues occurred |
| completed    | Subscription completed |
| cancelled    | Subscription has been cancelled |

---

### Handling subscription payment issues

If a subscription's status is `attention`, inspect the `most_recent_invoice` object in the subscription data.

```json
{
  "data": {
    "most_recent_invoice": {
      ...
      "status": "attention",
      "description": "Insufficient Funds"
    }
  }
}
```

---

### Expiring cards webhook

```json
{
  "event": "subscription.expiring_cards",
  "data": [
    {
      "expiry_date": "12/2021",
      ...
    }
  ]
}
```

---

## Updating subscriptions

Use the **Update Plan API** endpoint.

- `update_existing_subscriptions: true` – updates apply to all subscriptions on next billing cycle  
- `update_existing_subscriptions: false` – only new subscriptions are affected

---

## Updating the card on a subscription

Generate a hosted management link:

#### Node

```js
const https = require('https')

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/subscription/:code/manage/link',
  method: 'GET',
  headers: {
    Authorization: 'Bearer SECRET_KEY'
  }
}

https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})
```

Trigger management link via email:

```js
const https = require('https')

const options = {
  hostname: 'api.paystack.co',
  port: 443,
  path: '/subscription/:code/manage/email',
  method: 'POST',
  headers: {
    Authorization: 'Bearer SECRET_KEY',
  }
}

const req = https.request(options, res => {
  let data = ''

  res.on('data', (chunk) => {
    data += chunk
  });

  res.on('end', () => {
    console.log(JSON.parse(data))
  })
}).on('error', error => {
  console.error(error)
})

req.end()
```

```json
{
  "status": true,
  "message": "Email successfully sent"
}
```

