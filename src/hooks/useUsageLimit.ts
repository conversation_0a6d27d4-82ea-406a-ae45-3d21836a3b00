"use client";

import { useState, useCallback } from "react";
import { FeatureType } from "@/types/subscription";
import { UsageCheckResult } from "@/lib/usage-utils";
// Define PlanType locally since we're moving away from Prisma
type PlanType = "Free" | "Starter" | "Pro" | "Unlimited";

interface UsageLimitState {
  isModalOpen: boolean;
  feature: FeatureType | null;
  usageData: UsageCheckResult | null;
  planType: PlanType;
  isChecking: boolean;
}

export const useUsageLimit = () => {
  const [state, setState] = useState<UsageLimitState>({
    isModalOpen: false,
    feature: null,
    usageData: null,
    planType: "Free",
    isChecking: false,
  });

  const checkUsageLimit = useCallback(
    async (feature: FeatureType): Promise<boolean> => {
      // Set checking state to true
      setState((prev) => ({ ...prev, isChecking: true }));

      try {
        const response = await fetch("/api/dashboard/usage/check", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ feature }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to check usage limit");
        }

        // Check if usage limit is reached
        if (!data.allowed) {
          // Usage limit reached, show modal
          setState({
            isModalOpen: true,
            feature,
            usageData: data,
            planType: data.planType || "Free",
            isChecking: false,
          });
          return false;
        }

        // Reset checking state on success
        setState((prev) => ({ ...prev, isChecking: false }));
        return data.allowed;
      } catch (error) {
        console.error("Error checking usage limit:", error);
        // Reset checking state on error
        setState((prev) => ({ ...prev, isChecking: false }));
        return false;
      }
    },
    []
  );

  const closeModal = useCallback(() => {
    setState((prev) => ({
      ...prev,
      isModalOpen: false,
      feature: null,
      usageData: null,
    }));
  }, []);

  const showUsageLimitModal = useCallback(
    (
      feature: FeatureType,
      usageData: UsageCheckResult,
      planType: PlanType = "Free"
    ) => {
      setState({
        isModalOpen: true,
        feature,
        usageData,
        planType,
        isChecking: false,
      });
    },
    []
  );

  return {
    isModalOpen: state.isModalOpen,
    feature: state.feature,
    usageData: state.usageData,
    planType: state.planType,
    isChecking: state.isChecking,
    checkUsageLimit,
    closeModal,
    showUsageLimitModal,
  };
};
