/**
 * Content Processing Utilities
 *
 * This module contains utilities for processing different types of content
 * including YouTube transcripts, PDFs, and general text content.
 */

import { ContentMetadata } from "@/types/contentTypes";
import { parseJsonSafely } from "@/utils/jsonParser";

/**
 * Format content based on its type
 *
 * @param sourceContent The raw content to format
 * @param contentType The type of content (youtube, pdf, etc.)
 * @returns Formatted content as a string
 */
export function formatContent(
  sourceContent: string | Array<{ text: string }> | Record<string, unknown>,
  contentType: string
): string {
  // Handle YouTube transcript format
  if (contentType === "youtube" && Array.isArray(sourceContent)) {
    return sourceContent
      .filter((t) => typeof t.text === "string" && t.text.trim().length > 0)
      .map((t) => t.text.trim())
      .join("\n\n");
  }

  // Handle PDF content
  else if (contentType === "pdf" && typeof sourceContent === "string") {
    return sanitizePdfContent(sourceContent);
  }

  // General case for other content types
  else {
    return typeof sourceContent === "string"
      ? sourceContent
      : Array.isArray(sourceContent)
      ? sourceContent.map((item) => item.text || "").join("\n\n")
      : JSON.stringify(sourceContent);
  }
}

/**
 * Sanitize PDF content to remove problematic characters and formatting issues
 *
 * @param content The raw PDF content
 * @returns Sanitized PDF content
 */
export function sanitizePdfContent(content: string): string {
  // Performance optimization: Use console.time to measure sanitization time
  console.time("pdf-sanitization");

  if (!content) {
    console.error("PDF content is empty");
    throw new Error("PDF content is empty");
  }

  console.log(`[PDF Debug] Original content length: ${content.length}`);

  try {
    // Extract text from JSON if needed
    const textContent = extractTextFromJson(content);

    // Combine multiple regex operations into fewer passes for better performance
    // This reduces the number of string traversals

    // First pass: Handle control characters, line endings, and null bytes in one go
    let sanitized = textContent
      .replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F\0]/g, "") // Remove control chars and null bytes
      .replace(/\r\n/g, "\n") // Normalize line endings
      .replace(/\n{3,}/g, "\n\n"); // Normalize consecutive newlines

    // Second pass: Handle Unicode and non-ASCII characters
    sanitized = sanitized
      .replace(/\uFFFD/g, "") // Remove Unicode replacement chars
      .replace(/[^\x20-\x7E\n\t]/g, " ") // Replace non-ASCII with spaces
      .replace(/\\u0000|\\x00/g, ""); // Remove problematic sequences

    console.log(`[PDF Debug] Sanitized content length: ${sanitized.length}`);

    // Check if we have valid content after sanitization
    if (!sanitized.trim()) {
      console.error("PDF content is empty after sanitization");
      throw new Error("PDF content is empty after sanitization");
    }

    // Final validation - try to JSON stringify a small portion to check for issues
    try {
      // Only test a sample to improve performance
      const testSample = sanitized.substring(0, 1000);
      JSON.stringify({ sample: testSample });
    } catch (jsonError) {
      console.error(
        "PDF content still contains invalid characters after sanitization:",
        jsonError
      );
      // Apply more aggressive sanitization if JSON stringify fails
      sanitized = sanitized
        .replace(/[^\x20-\x7E\n\t ]/g, "") // Keep only ASCII printable chars, newlines, tabs and spaces
        .replace(/\\/g, "\\\\"); // Escape backslashes
    }

    console.timeEnd("pdf-sanitization");
    return sanitized;
  } catch (error) {
    console.timeEnd("pdf-sanitization");
    console.error("Error during PDF sanitization:", error);

    // Attempt emergency recovery - return a simplified version of the content
    try {
      // Last resort: strip everything except basic ASCII
      const emergencySanitized = content
        .replace(/[^\x20-\x7E\n\t ]/g, "")
        .replace(/\r\n/g, "\n")
        .replace(/\n{3,}/g, "\n\n");

      if (emergencySanitized.trim().length > 0) {
        console.log("[PDF Debug] Returning emergency sanitized content");
        return emergencySanitized;
      }
    } catch (emergencyError) {
      console.error("Emergency sanitization also failed:", emergencyError);
    }

    throw new Error(
      "PDF content could not be sanitized: " +
        (error instanceof Error ? error.message : String(error))
    );
  }
}

/**
 * Extract text content from JSON if needed
 *
 * @param content The content to extract text from
 * @returns The extracted text or the original content
 */
export function extractTextFromJson(content: string): string {
  try {
    // Check if content starts with { and ends with } - likely JSON
    if (content.trim().startsWith("{") && content.trim().endsWith("}")) {
      console.log(
        `[PDF Debug] Content appears to be JSON, attempting to parse`
      );

      // Try standard JSON parsing first
      try {
        const parsedContent = JSON.parse(content);
        // If it has a text field, use that instead
        if (parsedContent && typeof parsedContent.text === "string") {
          console.log(
            `[PDF Debug] Successfully extracted text field from JSON using standard parsing`
          );
          return parsedContent.text;
        }
      } catch (standardError) {
        console.log(
          `[PDF Debug] Standard JSON parsing failed, trying robust parsing`
        );

        // Try robust JSON parsing
        const parseResult = parseJsonSafely(content, true);
        if (parseResult.success && parseResult.data) {
          const parsedContent = parseResult.data as any;
          if (parsedContent && typeof parsedContent.text === "string") {
            console.log(
              `[PDF Debug] Successfully extracted text field from JSON using robust parsing (method: ${parseResult.method})`
            );
            return parsedContent.text;
          }
        }
      }
    }
    return content;
  } catch (jsonError) {
    console.log(
      `[PDF Debug] Content is not valid JSON, using as-is:`,
      jsonError instanceof Error ? jsonError.message : String(jsonError)
    );
    return content;
  }
}

/**
 * Handle content that exceeds the maximum token limit
 *
 * @param content The content to truncate
 * @param contentType The type of content
 * @param tokenCount The estimated token count
 * @param maxTokenLimit The maximum allowed token limit
 * @returns Truncated content
 */
export function truncateContent(
  content: string,
  contentType: string,
  tokenCount: number,
  maxTokenLimit: number
): string {
  console.time("content-truncation");
  console.warn(
    `Content exceeds maximum token limit (${tokenCount} > ${maxTokenLimit}). Truncating.`
  );

  try {
    const contentLength = content.length;
    const charLimit = maxTokenLimit * 4; // Convert tokens to approximate character count

    // For PDFs, try to keep the beginning and end, removing middle content
    if (contentType === "pdf") {
      // Calculate half of the max length, but ensure we don't exceed content length
      const halfMaxLength = Math.min(
        Math.floor(charLimit / 2),
        Math.floor(contentLength / 2)
      );

      // Try to find paragraph boundaries for cleaner truncation
      const firstHalf = content.substring(0, halfMaxLength);
      const secondHalf = content.substring(contentLength - halfMaxLength);

      // Find the last paragraph break in the first half
      const lastParaBreak = firstHalf.lastIndexOf("\n\n");
      const firstHalfEnd =
        lastParaBreak > halfMaxLength * 0.8 ? lastParaBreak : halfMaxLength;

      // Find the first paragraph break in the second half
      const firstParaBreak = secondHalf.indexOf("\n\n");
      const secondHalfStart =
        firstParaBreak > 0 && firstParaBreak < halfMaxLength * 0.2
          ? firstParaBreak
          : 0;

      const truncatedContent =
        content.substring(0, firstHalfEnd) +
        "\n\n[... Content truncated due to size limitations (approximately " +
        Math.round(
          (contentLength - (firstHalfEnd + halfMaxLength - secondHalfStart)) /
            1000
        ) +
        "KB removed) ...]\n\n" +
        secondHalf.substring(secondHalfStart);

      console.timeEnd("content-truncation");
      return truncatedContent;
    }
    // For other content types, try to truncate at a sentence or paragraph boundary
    else {
      // Find a good breakpoint near the character limit
      const truncatePoint = findTruncationPoint(content, charLimit);

      const truncatedContent =
        content.substring(0, truncatePoint) +
        "\n\n[... Content truncated due to size limitations (approximately " +
        Math.round((contentLength - truncatePoint) / 1000) +
        "KB removed) ...]";

      console.timeEnd("content-truncation");
      return truncatedContent;
    }
  } catch (error) {
    console.timeEnd("content-truncation");
    console.error("Error truncating content:", error);

    // Fallback to simple truncation if something goes wrong
    return (
      content.substring(0, maxTokenLimit * 4) +
      "\n\n[... Content truncated due to size limitations ...]"
    );
  }
}

/**
 * Find a good point to truncate content, preferring paragraph or sentence boundaries
 *
 * @param content The content to truncate
 * @param charLimit The character limit
 * @returns The index at which to truncate
 */
function findTruncationPoint(content: string, charLimit: number): number {
  // First try to find a paragraph break near the limit
  const paraBreakIndex = content.lastIndexOf("\n\n", charLimit);
  if (paraBreakIndex > charLimit * 0.8) {
    return paraBreakIndex;
  }

  // If no good paragraph break, try to find a sentence end
  const nearLimit = content.substring(charLimit - 200, charLimit + 200);
  const sentenceEndMatch = /[.!?]\s+/g;

  let lastMatch: RegExpExecArray | null = null;
  let match: RegExpExecArray | null;

  // Find the last sentence end before the limit
  while ((match = sentenceEndMatch.exec(nearLimit)) !== null) {
    if (charLimit - 200 + match.index <= charLimit) {
      lastMatch = match;
    } else {
      break;
    }
  }

  if (lastMatch) {
    return charLimit - 200 + lastMatch.index + lastMatch[0].length;
  }

  // If no good sentence break, just use the character limit
  return charLimit;
}

/**
 * Extract or generate a title from content or metadata
 *
 * @param aiResponse The AI-generated response
 * @param metadata The content metadata
 * @returns The extracted or generated title
 */
export function extractTitle(
  aiResponse: string,
  metadata: ContentMetadata
): string {
  // If we already have a title in metadata, use it
  if (metadata.title) {
    return metadata.title;
  }

  // Try multiple strategies to extract a title

  // Strategy 1: Look for markdown heading at the beginning
  const headingMatch = aiResponse.match(/^\s*#\s+([^\n]+)/);
  if (headingMatch && headingMatch[1]) {
    return headingMatch[1].trim();
  }

  // Strategy 2: Look for bold text at the beginning
  const boldMatch = aiResponse.match(/^\s*\*\*([^*]+)\*\*/);
  if (boldMatch && boldMatch[1]) {
    return boldMatch[1].trim();
  }

  // Strategy 3: Look for a title pattern with emoji
  const emojiTitleMatch = aiResponse.match(
    /^.*?[🔍📝📚🧠📊📈🔬📱💻⚙️].*?([^\n]+)/
  );
  if (emojiTitleMatch && emojiTitleMatch[1]) {
    // Clean up the title by removing markdown and emoji
    return emojiTitleMatch[1]
      .replace(/[#*]/g, "")
      .replace(/[\u{1F300}-\u{1F6FF}]/gu, "")
      .trim();
  }

  // Strategy 4: Just use the first line, cleaned up
  const firstLine = aiResponse.split("\n")[0];
  if (firstLine && firstLine.length > 3) {
    return firstLine
      .replace(/^(##|\*\*|#)\s*/, "")
      .replace(/(\*\*)$/, "")
      .trim();
  }

  // Fallback: Generate a title based on content type
  return metadata.contentType
    ? `${
        metadata.contentType.charAt(0).toUpperCase() +
        metadata.contentType.slice(1)
      } Content`
    : "Content Output";
}
