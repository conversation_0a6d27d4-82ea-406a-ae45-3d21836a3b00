/**
 * Chunking Utilities
 *
 * This module contains utilities for splitting content into chunks
 * and deduplicating content between chunks.
 */

import { parseJsonSafely } from "@/utils/jsonParser";

// Utility function to estimate token count (1 token ≈ 4 characters)
export function estimateTokens(text: string): number {
  return Math.ceil(text.length / 4);
}

/**
 * Split content into semantic chunks based on natural boundaries
 * like paragraphs and sentences when possible, falling back to character-based
 * chunking for very large content.
 */
export function splitIntoChunks(
  content: string,
  chunkSize: number,
  overlap: number
): string[] {
  console.log("[Chunking] Starting chunking process");

  // Extract text content from JSON if needed
  const textContent = extractTextContent(content);

  // Safety check for empty content
  if (!textContent || textContent.length === 0) {
    console.error("[Chunking] Empty content after processing");
    return ["Empty content"];
  }

  // Validate and normalize parameters
  const { normalizedChunkSize, normalizedOverlap, contentLength } =
    normalizeChunkingParameters(textContent, chunkSize, overlap);

  console.log(
    `[Chunking] Processing content of length ${contentLength} with chunk size ${normalizedChunkSize} and overlap ${normalizedOverlap}`
  );

  try {
    // Choose chunking strategy based on content size and type
    const MAX_CHUNKS = 10;

    // For very large content, use a simpler chunking approach
    if (contentLength > 500000) {
      return chunkLargeContent(
        textContent,
        normalizedChunkSize,
        normalizedOverlap,
        MAX_CHUNKS
      );
    }

    // For medium to small content, use semantic chunking
    return chunkContentSemantically(
      textContent,
      normalizedChunkSize,
      normalizedOverlap,
      MAX_CHUNKS
    );
  } catch (error) {
    console.error("[Chunking] Error splitting content into chunks:", error);
    // Return a single chunk with an error message in case of failure
    return [
      "Error splitting content into chunks: " +
        (error instanceof Error ? error.message : String(error)),
    ];
  }
}

/**
 * Extract text content from JSON if needed
 */
function extractTextContent(content: string): string {
  try {
    // Check if content starts with { and ends with } - likely JSON
    if (content.trim().startsWith("{") && content.trim().endsWith("}")) {
      // Try standard JSON parsing first
      try {
        const parsedContent = JSON.parse(content);
        // If it has a text field, use that instead
        if (parsedContent && typeof parsedContent.text === "string") {
          console.log(
            "[Chunking] Detected JSON content with text field, extracting text using standard parsing"
          );
          return parsedContent.text;
        }
      } catch (standardError) {
        console.log(
          "[Chunking] Standard JSON parsing failed, trying robust parsing"
        );

        // Try robust JSON parsing
        const parseResult = parseJsonSafely(content, true);
        if (parseResult.success && parseResult.data) {
          const parsedContent = parseResult.data as any;
          if (parsedContent && typeof parsedContent.text === "string") {
            console.log(
              `[Chunking] Successfully extracted text field from JSON using robust parsing (method: ${parseResult.method})`
            );
            return parsedContent.text;
          }
        }
      }
    }
    return content;
  } catch (jsonError) {
    // If JSON parsing fails, use the original content
    console.log(
      "[Chunking] Content is not valid JSON, using as-is:",
      jsonError instanceof Error ? jsonError.message : String(jsonError)
    );
    return content;
  }
}

/**
 * Normalize chunking parameters and return content length
 */
function normalizeChunkingParameters(
  content: string,
  chunkSize: number,
  overlap: number
): {
  normalizedChunkSize: number;
  normalizedOverlap: number;
  contentLength: number;
} {
  const contentLength = content.length;

  // Safety check for chunk size
  let normalizedChunkSize = chunkSize;
  if (chunkSize <= 0) {
    console.error("[Chunking] Invalid chunk size, using default");
    normalizedChunkSize = 100000; // Default to a reasonable size
  }

  // Safety check for overlap
  let normalizedOverlap = overlap;
  if (overlap < 0 || overlap >= normalizedChunkSize) {
    console.error("[Chunking] Invalid overlap, using default");
    normalizedOverlap = Math.floor(normalizedChunkSize * 0.1); // Default to 10% of chunk size
  }

  return { normalizedChunkSize, normalizedOverlap, contentLength };
}

/**
 * Chunk large content using a simple character-based approach
 */
function chunkLargeContent(
  content: string,
  chunkSize: number,
  overlap: number,
  maxChunks: number
): string[] {
  console.log(
    `[Chunking] Content is very large (${content.length} chars), using optimized chunking approach`
  );

  const chunks: string[] = [];
  const contentLength = content.length;

  // For large content, create fewer, larger chunks
  const MAX_CHUNK_SIZE = 100000; // 100K characters per chunk is safer
  const safeChunkSize = Math.min(chunkSize, MAX_CHUNK_SIZE);
  const numChunks = Math.min(
    Math.ceil(contentLength / safeChunkSize),
    maxChunks
  );
  const actualChunkSize = Math.ceil(contentLength / numChunks);
  const safeOverlap = Math.min(overlap, Math.floor(actualChunkSize * 0.1));

  console.log(
    `[Chunking] Using ${numChunks} chunks of size ~${actualChunkSize} with overlap ${safeOverlap}`
  );

  // Create chunks with approximately equal size
  for (let i = 0; i < numChunks; i++) {
    const chunkStart = i === 0 ? 0 : i * actualChunkSize - safeOverlap;
    const chunkEnd = Math.min((i + 1) * actualChunkSize, contentLength);

    if (chunkStart < chunkEnd) {
      try {
        // Try to find paragraph or sentence boundaries near the chunk boundaries
        let chunk = content.slice(chunkStart, chunkEnd);

        // If not the first chunk, try to start at a paragraph or sentence boundary
        if (i > 0) {
          const paragraphMatch = chunk.match(/^\s*.*?(\n\n|\r\n\r\n)/);
          if (
            paragraphMatch &&
            paragraphMatch.index !== undefined &&
            paragraphMatch.index < safeOverlap * 0.5
          ) {
            // Start at paragraph boundary
            chunk = chunk.slice(
              paragraphMatch.index + paragraphMatch[0].length
            );
          } else {
            const sentenceMatch = chunk.match(/^\s*.*?[.!?]\s+/);
            if (
              sentenceMatch &&
              sentenceMatch.index !== undefined &&
              sentenceMatch.index < safeOverlap * 0.8
            ) {
              // Start at sentence boundary
              chunk = chunk.slice(
                sentenceMatch.index + sentenceMatch[0].length
              );
            }
          }
        }

        chunks.push(chunk);
      } catch (sliceError) {
        console.error(
          `[Chunking] Error slicing chunk ${i + 1}/${numChunks}:`,
          sliceError
        );
        // Add an error placeholder for this chunk
        chunks.push(`[Error slicing chunk ${i + 1}/${numChunks}]`);
      }
    }
  }

  console.log(
    `[Chunking] Successfully split large content into ${chunks.length} chunks`
  );
  return validateChunks(chunks);
}

/**
 * Chunk content semantically, respecting natural boundaries like paragraphs and sentences
 */
function chunkContentSemantically(
  content: string,
  chunkSize: number,
  overlap: number,
  maxChunks: number
): string[] {
  console.log(
    `[Chunking] Using semantic chunking for content with length ${content.length}`
  );

  const chunks: string[] = [];

  // First, try to split by paragraphs (double newlines)
  const paragraphs = content
    .split(/\n\n|\r\n\r\n/)
    .filter((p) => p.trim().length > 0);

  // If we have very few paragraphs, try sentences instead
  if (paragraphs.length <= 2) {
    console.log(
      `[Chunking] Few paragraphs detected (${paragraphs.length}), splitting by sentences`
    );
    return chunkBySentences(content, chunkSize, overlap, maxChunks);
  }

  console.log(
    `[Chunking] Splitting content into ${paragraphs.length} paragraphs`
  );

  // Group paragraphs into chunks
  let currentChunk = "";
  let currentChunkSize = 0;

  for (const paragraph of paragraphs) {
    const paragraphSize = paragraph.length;

    // If adding this paragraph would exceed chunk size and we already have content,
    // finish the current chunk and start a new one
    if (currentChunkSize + paragraphSize > chunkSize && currentChunkSize > 0) {
      chunks.push(currentChunk);

      // Start new chunk with overlap from previous chunk if possible
      const overlapText = getOverlapText(currentChunk, overlap);
      currentChunk = overlapText + paragraph;
      currentChunkSize = currentChunk.length;
    } else {
      // Add paragraph to current chunk
      currentChunk += (currentChunk ? "\n\n" : "") + paragraph;
      currentChunkSize = currentChunk.length;
    }

    // Safety check to prevent too many chunks
    if (chunks.length >= maxChunks - 1) {
      console.warn(
        `[Chunking] Reached maximum number of chunks (${maxChunks}), combining remaining content`
      );
      currentChunk += paragraphs
        .slice(paragraphs.indexOf(paragraph) + 1)
        .join("\n\n");
      break;
    }
  }

  // Add the last chunk if it has content
  if (currentChunk) {
    chunks.push(currentChunk);
  }

  console.log(
    `[Chunking] Successfully split content into ${chunks.length} semantic chunks`
  );
  return validateChunks(chunks);
}

/**
 * Chunk content by sentences when paragraph chunking isn't appropriate
 */
function chunkBySentences(
  content: string,
  chunkSize: number,
  overlap: number,
  maxChunks: number
): string[] {
  console.log(`[Chunking] Chunking content by sentences`);

  // Split by sentence boundaries (period, question mark, exclamation point followed by space)
  const sentenceRegex = /[.!?]\s+/;
  const sentences = content
    .split(sentenceRegex)
    .filter((s) => s.trim().length > 0);

  console.log(`[Chunking] Split content into ${sentences.length} sentences`);

  const chunks: string[] = [];
  let currentChunk = "";
  let currentChunkSize = 0;

  for (const sentence of sentences) {
    const sentenceSize = sentence.length;

    // If this sentence alone exceeds chunk size, split it further
    if (sentenceSize > chunkSize) {
      if (currentChunk) {
        chunks.push(currentChunk);
        currentChunk = "";
        currentChunkSize = 0;
      }

      // Split long sentence into smaller parts
      const sentenceParts = splitLongSentence(sentence, chunkSize);
      chunks.push(...sentenceParts);
      continue;
    }

    // If adding this sentence would exceed chunk size and we already have content,
    // finish the current chunk and start a new one
    if (currentChunkSize + sentenceSize > chunkSize && currentChunkSize > 0) {
      chunks.push(currentChunk);

      // Start new chunk with overlap from previous chunk if possible
      const overlapText = getOverlapText(currentChunk, overlap);
      currentChunk = overlapText + sentence;
      currentChunkSize = currentChunk.length;
    } else {
      // Add sentence to current chunk
      currentChunk += (currentChunk ? " " : "") + sentence;
      currentChunkSize = currentChunk.length;
    }

    // Safety check to prevent too many chunks
    if (chunks.length >= maxChunks - 1) {
      console.warn(
        `[Chunking] Reached maximum number of chunks (${maxChunks}), combining remaining content`
      );
      currentChunk += sentences
        .slice(sentences.indexOf(sentence) + 1)
        .join(" ");
      break;
    }
  }

  // Add the last chunk if it has content
  if (currentChunk) {
    chunks.push(currentChunk);
  }

  console.log(
    `[Chunking] Successfully split content into ${chunks.length} sentence-based chunks`
  );
  return validateChunks(chunks);
}

/**
 * Split a very long sentence into smaller parts
 */
function splitLongSentence(sentence: string, maxLength: number): string[] {
  console.log(
    `[Chunking] Splitting long sentence of length ${sentence.length}`
  );

  const parts: string[] = [];
  let start = 0;

  while (start < sentence.length) {
    let end = Math.min(start + maxLength, sentence.length);

    // If we're not at the end, try to break at a word boundary
    if (end < sentence.length) {
      const lastSpace = sentence.lastIndexOf(" ", end);
      if (lastSpace > start) {
        end = lastSpace + 1;
      }
    }

    parts.push(sentence.slice(start, end));
    start = end;
  }

  return parts;
}

/**
 * Get overlap text from the end of a chunk
 */
function getOverlapText(text: string, overlapSize: number): string {
  if (!text || overlapSize <= 0) return "";

  // Try to get overlap at paragraph boundary
  const paragraphs = text.split(/\n\n|\r\n\r\n/);
  if (paragraphs.length > 1) {
    let overlap = "";
    let i = paragraphs.length - 1;

    while (i >= 0 && overlap.length < overlapSize) {
      overlap =
        paragraphs[i] +
        (overlap ? "\n\n" + overlap : "") +
        (i < paragraphs.length - 1 ? "\n\n" : "");
      i--;
    }

    if (overlap.length <= overlapSize * 1.5) {
      return overlap;
    }
  }

  // Fall back to character-based overlap
  return text.slice(Math.max(0, text.length - overlapSize));
}

/**
 * Validate chunks and ensure we have at least one valid chunk
 */
function validateChunks(chunks: string[]): string[] {
  // Validate that we have at least one non-empty chunk
  const validChunks = chunks.filter((chunk) => chunk && chunk.length > 0);
  if (validChunks.length === 0) {
    console.error("[Chunking] No valid chunks were created");
    return ["No valid content chunks could be created"];
  }
  return validChunks;
}

/**
 * Enhanced deduplication function to remove overlapping content between chunks
 * This is critical for preventing duplicate content in the final output
 */
export function deduplicateContent(previous: string, current: string): string {
  if (!previous || !current) {
    return current || "";
  }

  console.log(`[Deduplication] Checking for overlaps between content blocks`);

  // First try section-level deduplication for structured content (headings, sections)
  const sectionResult = deduplicateSections(previous, current);
  if (sectionResult !== current) {
    console.log(`[Deduplication] Section-level deduplication applied`);
    return sectionResult;
  }

  // Then try paragraph-level deduplication
  const paragraphResult = deduplicateParagraphs(previous, current);
  if (paragraphResult !== current) {
    console.log(`[Deduplication] Paragraph-level deduplication applied`);
    return paragraphResult;
  }

  // If paragraph deduplication didn't find overlaps, try line-level
  const lineResult = deduplicateLines(previous, current);
  if (lineResult !== current) {
    console.log(`[Deduplication] Line-level deduplication applied`);
    return lineResult;
  }

  // If no other deduplication worked, check for repeated phrases
  return deduplicateRepeatedPhrases(previous, current);
}

/**
 * Deduplicate content at paragraph level
 */
function deduplicateParagraphs(previous: string, current: string): string {
  // Split into paragraphs (text blocks separated by double newlines)
  const prevParagraphs = previous
    .split(/\n\n|\r\n\r\n/)
    .filter((p) => p.trim());
  const currParagraphs = current.split(/\n\n|\r\n\r\n/).filter((p) => p.trim());

  if (prevParagraphs.length === 0 || currParagraphs.length === 0) {
    return current;
  }

  // Look for overlapping paragraphs at the end of previous and start of current
  const maxOverlapToCheck = Math.min(
    5,
    prevParagraphs.length,
    currParagraphs.length
  );

  // Try different overlap sizes, starting from the largest possible
  for (let overlapSize = maxOverlapToCheck; overlapSize > 0; overlapSize--) {
    const prevEnd = prevParagraphs.slice(-overlapSize);
    const currStart = currParagraphs.slice(0, overlapSize);

    // Check if we have a full match of 'overlapSize' paragraphs
    if (prevEnd.every((p, i) => p === currStart[i])) {
      // Found overlap, return current content with overlapping paragraphs removed
      return currParagraphs.slice(overlapSize).join("\n\n");
    }
  }

  return current;
}

/**
 * Deduplicate content at line level
 */
function deduplicateLines(previous: string, current: string): string {
  const prevLines = previous.split("\n").filter((line) => line.trim());
  const currLines = current.split("\n").filter((line) => line.trim());

  if (prevLines.length === 0 || currLines.length === 0) {
    return current;
  }

  // Check for overlapping lines with a larger window
  const overlapWindow = Math.min(10, prevLines.length, currLines.length);
  let maxOverlapFound = 0;
  let overlapStartIndex = -1;

  // Try to find the largest sequence of matching lines
  for (let windowSize = overlapWindow; windowSize > 0; windowSize--) {
    for (let i = 0; i <= overlapWindow - windowSize; i++) {
      const prevStartIdx = prevLines.length - overlapWindow + i;
      const currStartIdx = i;

      // Check if we have a match of 'windowSize' consecutive lines
      let allMatch = true;
      for (let j = 0; j < windowSize; j++) {
        if (prevLines[prevStartIdx + j] !== currLines[currStartIdx + j]) {
          allMatch = false;
          break;
        }
      }

      if (allMatch && windowSize > maxOverlapFound) {
        maxOverlapFound = windowSize;
        overlapStartIndex = currStartIdx;
        break;
      }
    }

    if (maxOverlapFound > 0) {
      break;
    }
  }

  // If we found overlapping lines, remove them from the current content
  if (maxOverlapFound > 0 && overlapStartIndex !== -1) {
    return currLines
      .slice(overlapStartIndex + maxOverlapFound)
      .join("\n")
      .trim();
  }

  // If no significant overlap found, check for duplicate headings or sections
  const headingPattern = /^#+\s+.+$|^[A-Z][\w\s]+:$|^\d+\.\s+.+$/;
  const currHeadings = currLines.filter((line) => headingPattern.test(line));

  if (currHeadings.length > 0) {
    // Check if the first heading in current appears in previous
    const firstHeading = currHeadings[0];
    if (prevLines.includes(firstHeading)) {
      const headingIndex = currLines.indexOf(firstHeading);
      // If the heading is near the beginning, skip content up to the next heading
      if (headingIndex < 5) {
        const nextHeadingIndex = currLines.findIndex(
          (line, idx) => idx > headingIndex && headingPattern.test(line)
        );

        if (nextHeadingIndex !== -1) {
          return currLines.slice(nextHeadingIndex).join("\n").trim();
        }
      }
    }
  }

  return current;
}

/**
 * Deduplicate content at the section level, focusing on structured content with headings
 * This is particularly effective for AI-generated summaries and notes that follow a template
 */
function deduplicateSections(previous: string, current: string): string {
  // Define patterns for common section headers in AI-generated content
  const sectionPatterns = [
    /#+\s+[^\n]+/g, // Markdown headings (e.g., "# Introduction")
    /\*\*[^\*\n]+\*\*/g, // Bold text often used as headings (e.g., "**Key Points**")
    /[🔍📝📚🧠📊📈🔬📱💻⚙️][^\n]+/g, // Emoji + text headers (e.g., "📝 Summary")
    /\d+\.\s+[A-Z][^\n]+/g, // Numbered sections (e.g., "1. Introduction")
    /[A-Z][A-Za-z\s]+:/g, // Title case followed by colon (e.g., "Main Findings:")
  ];

  // Extract all potential section headers from both texts
  const prevSections: string[] = [];
  const currSections: string[] = [];

  for (const pattern of sectionPatterns) {
    const prevMatches = [...previous.matchAll(pattern)].map((m) => m[0].trim());
    const currMatches = [...current.matchAll(pattern)].map((m) => m[0].trim());

    prevSections.push(...prevMatches);
    currSections.push(...currMatches);
  }

  // If no sections found in current content, can't deduplicate at section level
  if (currSections.length === 0) {
    return current;
  }

  // Find duplicate sections
  const duplicateSections = currSections.filter((section) =>
    prevSections.some(
      (prevSection) =>
        // Use fuzzy matching to catch slight variations
        prevSection === section ||
        (prevSection.length > 10 &&
          section.includes(prevSection.substring(2))) ||
        (section.length > 10 && prevSection.includes(section.substring(2)))
    )
  );

  // If no duplicate sections found, return original content
  if (duplicateSections.length === 0) {
    return current;
  }

  console.log(
    `[Deduplication] Found ${duplicateSections.length} potentially duplicate sections`
  );

  // Process the current content to remove duplicate sections and their content
  let processedContent = current;
  let contentRemoved = false;

  for (const section of duplicateSections) {
    // Find the section in the current content
    const sectionIndex = processedContent.indexOf(section);
    if (sectionIndex === -1) continue;

    // Find the next section header after this one
    let nextSectionIndex = processedContent.length;

    for (const pattern of sectionPatterns) {
      pattern.lastIndex = sectionIndex + section.length; // Start search after current section
      const match = pattern.exec(processedContent);
      if (match && match.index < nextSectionIndex) {
        nextSectionIndex = match.index;
      }
    }

    // Remove this section and its content
    const beforeSection = processedContent.substring(0, sectionIndex);
    const afterSection = processedContent.substring(nextSectionIndex);

    processedContent = beforeSection + afterSection;
    contentRemoved = true;

    console.log(
      `[Deduplication] Removed duplicate section: "${section.substring(
        0,
        50
      )}..."`
    );
  }

  return contentRemoved ? processedContent.trim() : current;
}

/**
 * Deduplicate repeated phrases and sentences that might appear in multiple chunks
 * This is a last-resort deduplication for content that wasn't caught by other methods
 */
function deduplicateRepeatedPhrases(previous: string, current: string): string {
  // If either string is too short, skip this check
  if (previous.length < 100 || current.length < 100) {
    return current;
  }

  // Extract significant phrases from the previous content (5+ words)
  const significantPhraseRegex = /\b(\w+\s+){4,}\w+\b/g;
  const prevPhrases = [...previous.matchAll(significantPhraseRegex)]
    .map((m) => m[0])
    .filter((phrase) => phrase.length > 30); // Only consider substantial phrases

  // If no significant phrases found, return original
  if (prevPhrases.length === 0) {
    return current;
  }

  // Look for these phrases in the current content
  let processedContent = current;
  let contentChanged = false;

  // Sort phrases by length (descending) to remove longer duplicates first
  const sortedPhrases = prevPhrases.sort((a, b) => b.length - a.length);

  // Only check the top phrases to avoid excessive processing
  const phrasesToCheck = sortedPhrases.slice(0, 20);

  for (const phrase of phrasesToCheck) {
    if (processedContent.includes(phrase)) {
      // Replace only the first occurrence of each phrase
      processedContent = processedContent.replace(phrase, "[...]");
      contentChanged = true;
      console.log(
        `[Deduplication] Removed repeated phrase: "${phrase.substring(
          0,
          50
        )}..."`
      );
    }
  }

  // If we made changes, clean up any artifacts and return
  if (contentChanged) {
    // Clean up multiple consecutive [...] markers
    processedContent = processedContent.replace(
      /\[\.\.\.\]\s*\[\.\.\.\]/g,
      "[...]"
    );

    // Clean up empty lines created by removals
    processedContent = processedContent.replace(/\n\s*\n\s*\n/g, "\n\n");

    return processedContent.trim();
  }

  return current;
}
