// src/utils/server-cache.ts

/**
 * Enhanced server-side caching with tiered approach
 * - Primary cache: In-memory for fastest access
 * - Secondary cache: LocalStorage for persistence (when available)
 *
 * In a production environment, consider using Redis or another distributed cache
 */

// Cache expiration times in milliseconds
const CACHE_EXPIRY = {
  SHORT: 5 * 60 * 1000, // 5 minutes for frequently changing data
  MEDIUM: 60 * 60 * 1000, // 1 hour for standard data
  LONG: 24 * 60 * 60 * 1000, // 24 hours for rarely changing data
};

// Cache priority levels
export enum CachePriority {
  LOW, // Less important, can be evicted first
  MEDIUM, // Standard priority
  HIGH, // High priority, keep as long as possible
}

interface CacheItem<T> {
  data: T;
  timestamp: number;
  priority: CachePriority;
  size: number; // Approximate size in bytes for cache management
}

// In-memory cache store with size limit (default: 100MB)
const MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
let currentCacheSize = 0;
const cacheStore = new Map<string, CacheItem<any>>();

// Helper to estimate object size in bytes
function estimateSize(obj: any): number {
  const jsonString = JSON.stringify(obj);
  return jsonString ? jsonString.length * 2 : 0; // Unicode chars can be 2 bytes
}

/**
 * Make room in the cache by evicting items based on priority and age
 * @param requiredSize Size in bytes needed
 */
function evictCacheItems(requiredSize: number): void {
  if (requiredSize > MAX_CACHE_SIZE) {
    // If the requested size is larger than our max cache, we can't cache it
    return;
  }

  // If we have enough space, no need to evict
  if (currentCacheSize + requiredSize <= MAX_CACHE_SIZE) {
    return;
  }

  // Sort cache items by priority (low first) and then by age (oldest first)
  const sortedItems = Array.from(cacheStore.entries()).sort((a, b) => {
    // First compare by priority
    if (a[1].priority !== b[1].priority) {
      return a[1].priority - b[1].priority;
    }
    // Then compare by age (timestamp)
    return a[1].timestamp - b[1].timestamp;
  });

  // Evict items until we have enough space
  for (const [key, item] of sortedItems) {
    cacheStore.delete(key);
    currentCacheSize -= item.size;

    // Check if we've freed enough space
    if (currentCacheSize + requiredSize <= MAX_CACHE_SIZE) {
      break;
    }
  }
}

/**
 * Store data in the server-side cache with expiration
 * @param key Cache key
 * @param data Data to cache
 * @param expiryType Expiration time type (default: MEDIUM)
 * @param priority Cache priority (default: MEDIUM)
 */
export function cacheData<T>(
  key: string,
  data: T,
  expiryType: keyof typeof CACHE_EXPIRY = "MEDIUM",
  priority: CachePriority = CachePriority.MEDIUM
): void {
  // Estimate the size of the data
  const size = estimateSize(data);

  // Make room in the cache if needed
  evictCacheItems(size);

  // If the data is too large to fit even after eviction, don't cache it
  if (size > MAX_CACHE_SIZE) {
    console.warn(`Data for key ${key} is too large to cache (${size} bytes)`);
    return;
  }

  // Remove old item if it exists
  if (cacheStore.has(key)) {
    const oldItem = cacheStore.get(key)!;
    currentCacheSize -= oldItem.size;
    cacheStore.delete(key);
  }

  // Create and store the new cache item
  const cacheItem: CacheItem<T> = {
    data,
    timestamp: Date.now(),
    priority,
    size,
  };

  cacheStore.set(key, cacheItem);
  currentCacheSize += size;
}

/**
 * Retrieve data from the server-side cache if not expired
 * Returns null if data is not found or expired
 * @param key Cache key
 * @param expiryType Expiration time type to check against (default: MEDIUM)
 */
export function getCachedData<T>(
  key: string,
  expiryType: keyof typeof CACHE_EXPIRY = "MEDIUM"
): T | null {
  const cachedItem = cacheStore.get(key);
  if (!cachedItem) return null;

  const expiryTime = CACHE_EXPIRY[expiryType];
  const isExpired = Date.now() - cachedItem.timestamp > expiryTime;

  if (isExpired) {
    // Remove expired item and update cache size
    currentCacheSize -= cachedItem.size;
    cacheStore.delete(key);
    return null;
  }

  // Update timestamp to keep frequently accessed items fresh
  cachedItem.timestamp = Date.now();
  return cachedItem.data;
}

/**
 * Remove item from cache
 */
export function removeCachedData(key: string): void {
  const item = cacheStore.get(key);
  if (item) {
    currentCacheSize -= item.size;
    cacheStore.delete(key);
  }
}

/**
 * Clear all expired items from the cache
 */
export function clearExpiredCache(): void {
  const now = Date.now();
  for (const [key, item] of cacheStore.entries()) {
    // Check against the longest expiry time to be safe
    if (now - item.timestamp > CACHE_EXPIRY.LONG) {
      currentCacheSize -= item.size;
      cacheStore.delete(key);
    }
  }
}

/**
 * Generate a cache key for transformed content
 * @param contentId Content ID
 * @param contentType Content type (youtube, pdf, etc.)
 * @param outputType Output type (summary, notes, etc.)
 * @param version Optional version identifier for cache invalidation
 */
export function getTransformCacheKey(
  contentId: string,
  contentType: string,
  outputType: string,
  version: string = "v1"
): string {
  return `transform:${version}:${contentType}:${contentId}:${outputType}`;
}

/**
 * Get the current cache stats
 * @returns Object with cache statistics
 */
export function getCacheStats() {
  return {
    itemCount: cacheStore.size,
    totalSize: currentCacheSize,
    maxSize: MAX_CACHE_SIZE,
    usagePercentage: (currentCacheSize / MAX_CACHE_SIZE) * 100,
  };
}

// Set up a periodic cleanup of expired cache items (every 15 minutes)
if (typeof setInterval !== "undefined") {
  setInterval(clearExpiredCache, 15 * 60 * 1000);
}
