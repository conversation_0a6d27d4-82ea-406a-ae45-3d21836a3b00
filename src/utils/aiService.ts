/**
 * AI Service Utilities
 *
 * This module contains utilities for interacting with AI models,
 * including rate limiting, timeout handling, and parallel processing.
 */

import type { RateLimiter } from "limiter";
import { ContentMetadata } from "@/types/contentTypes";
import { generatePrompt } from "./prompts/contentPrompt";
import { splitIntoChunks, deduplicateContent } from "./chunking";

/**
 * Generate content with rate limiting and timeout handling
 *
 * @param prompt The prompt to send to the AI model
 * @param limiter The rate limiter to use
 * @param model The AI model to use
 * @param onProgress Optional callback for progress updates
 * @returns The generated content
 */
/**
 * Type definition for AI model response
 * This is a simplified type for the Google Generative AI model response
 */
interface AIModelResponse {
  response: {
    text: () => Promise<string> | string;
  };
}

/**
 * Type definition for AI model
 * This is a simplified type for the Google Generative AI model
 *
 * Note: The actual Google Generative AI model has a different structure,
 * but we're using this simplified type for compatibility with different AI providers
 */
interface AIModel {
  generateContent: (prompt: string) => Promise<AIModelResponse>;
}

export async function generateWithRateLimit(
  prompt: string,
  limiter: RateLimiter,
  model: AIModel,
  onProgress?: (progress: number) => void
): Promise<string> {
  const hasTokens = limiter.tryRemoveTokens(1);
  if (!hasTokens) {
    throw new Error("Rate limit exceeded");
  }

  const timeoutMs = 55000;
  const controller = new AbortController();
  const timeout = setTimeout(() => controller.abort(), timeoutMs);

  try {
    // Report progress at the start if callback provided
    if (onProgress) {
      onProgress(0.1); // 10% progress when starting
    }

    try {
      const result = await Promise.race([
        model.generateContent(prompt),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error("API timeout")), timeoutMs)
        ),
      ]);

      clearTimeout(timeout);

      // Report progress at completion if callback provided
      if (onProgress) {
        onProgress(1.0); // 100% progress when complete
      }

      // Handle different response formats (some models return string directly, others return a Promise)
      const responseText = result.response.text();
      return typeof responseText === "string"
        ? responseText.trim()
        : (await responseText).trim();
    } catch (error) {
      console.error("Error generating content:", error);

      // Provide more specific error messages based on the error type
      if (error instanceof Error) {
        if (
          error.message.includes("timeout") ||
          error.message.includes("Timeout")
        ) {
          throw new Error(
            "API timeout: The model took too long to respond. Try with shorter content."
          );
        } else if (
          error.message.includes("rate") ||
          error.message.includes("quota")
        ) {
          throw new Error(
            "Rate limit exceeded: Too many requests to the AI service."
          );
        }
      }

      // Re-throw the original error if we couldn't provide a more specific message
      throw error;
    }
  } catch (error) {
    clearTimeout(timeout);
    throw error;
  }
}

/**
 * Process chunks in parallel with concurrency control
 *
 * @param prompts Array of prompts to process
 * @param limiter Rate limiter to use
 * @param model AI model to use
 * @param maxConcurrency Maximum number of concurrent requests
 * @param onChunkProgress Optional callback for progress updates
 * @returns Array of generated content for each prompt
 */
export async function processChunksInParallel(
  prompts: string[],
  limiter: RateLimiter,
  model: AIModel,
  maxConcurrency: number = 2,
  onChunkProgress?: (chunkIndex: number, progress: number) => void
): Promise<string[]> {
  const results: string[] = new Array(prompts.length).fill("");
  const pendingPromises: Promise<void>[] = [];
  let nextChunkIndex = 0;

  // Process chunks with controlled concurrency and retry logic
  async function processNextChunk(): Promise<void> {
    if (nextChunkIndex >= prompts.length) return;

    const currentChunkIndex = nextChunkIndex++;
    const prompt = prompts[currentChunkIndex];

    // Add retry logic for resilience
    const MAX_RETRIES = 2;
    let retryCount = 0;
    let lastError: Error | null = null;

    while (retryCount <= MAX_RETRIES) {
      try {
        // If this is a retry, add a small delay to avoid overwhelming the API
        if (retryCount > 0) {
          console.log(
            `Retrying chunk ${currentChunkIndex} (attempt ${retryCount} of ${MAX_RETRIES})...`
          );
          // Exponential backoff: wait longer for each retry
          await new Promise((resolve) =>
            setTimeout(resolve, 1000 * Math.pow(2, retryCount - 1))
          );
        }

        // Process this chunk with progress reporting
        const result = await generateWithRateLimit(
          prompt,
          limiter,
          model,
          (progress) => {
            if (onChunkProgress) {
              // Scale progress based on retry attempts
              const scaledProgress =
                (progress + retryCount) / (MAX_RETRIES + 1);
              onChunkProgress(currentChunkIndex, scaledProgress);
            }
          }
        );

        // Store the result and break out of retry loop
        results[currentChunkIndex] = result;

        // Process next chunk when this one is done
        await processNextChunk();
        return;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(
          `Error processing chunk ${currentChunkIndex} (attempt ${
            retryCount + 1
          }):`,
          error
        );

        // Don't retry if it's a rate limit error (that won't resolve quickly)
        if (
          lastError.message.includes("rate limit") ||
          lastError.message.includes("quota")
        ) {
          console.error(
            `Rate limit error, not retrying chunk ${currentChunkIndex}`
          );
          break;
        }

        retryCount++;
      }
    }

    // If we've exhausted retries or hit a non-retryable error
    if (lastError) {
      // For timeout errors, provide a fallback response instead of failing completely
      if (
        lastError.message.includes("timeout") ||
        lastError.message.includes("Timeout")
      ) {
        console.warn(
          `Chunk ${currentChunkIndex} timed out after ${MAX_RETRIES} retries. Using fallback content.`
        );
        results[
          currentChunkIndex
        ] = `[Note: This section could not be processed due to its complexity or length. Key points may be missing.]`;

        // Continue processing next chunks despite this failure
        await processNextChunk();
        return;
      }

      // For other errors, throw to stop processing
      throw lastError;
    }
  }

  // Start initial batch of concurrent requests
  const concurrency = Math.min(maxConcurrency, prompts.length);
  for (let i = 0; i < concurrency; i++) {
    pendingPromises.push(processNextChunk());
  }

  // Wait for all chunks to be processed
  await Promise.all(pendingPromises);

  return results;
}

/**
 * Generate prompts for chunked content
 *
 * @param outputType The type of output to generate (summary or notes)
 * @param metadata Content metadata
 * @param contentText The content text
 * @param chunkSize The size of each chunk
 * @param overlap The overlap between chunks
 * @returns Array of prompts for each chunk
 */
export function generateChunkedPrompts(
  outputType: string,
  metadata: ContentMetadata,
  contentText: string,
  chunkSize: number,
  overlap: number
): string[] {
  console.log(`[Chunking] Generating prompts for ${outputType}`);

  // Use semantic chunking implementation
  const chunks = splitIntoChunks(contentText, chunkSize, overlap);

  console.log(`[Chunking] Generated ${chunks.length} chunks for processing`);

  // Map each chunk to a prompt
  return chunks.map((chunk, index) => {
    if (outputType === "summary" || outputType === "notes") {
      // Generate a prompt for this chunk, indicating its position in the sequence
      return generatePrompt(
        outputType,
        metadata,
        chunk,
        index + 1,
        chunks.length
      );
    } else {
      throw new Error(
        `Invalid outputType: ${outputType}. Must be 'summary' or 'notes'.`
      );
    }
  });
}

/**
 * Process content using chunking for large content
 *
 * @param outputType The type of output to generate
 * @param metadata Content metadata
 * @param content The content to process
 * @param limiter Rate limiter to use
 * @param model AI model to use
 * @param chunkSize The size of each chunk
 * @param overlap The overlap between chunks
 * @param onChunkProgress Optional callback for progress updates
 * @returns The combined AI response
 */
/**
 * Perform a final cleanup pass on the combined AI response to remove any remaining repetitive content
 *
 * @param content The combined AI response
 * @returns The cleaned content
 */
function performFinalCleanup(content: string): string {
  // If content is too short, no need for cleanup
  if (!content || content.length < 500) {
    return content;
  }

  try {
    // 1. Remove duplicate "Facts to Memorize" or "Takeaways" sections
    // These are common in AI-generated summaries and notes
    const specialSectionRegex =
      /(?:## Facts to Memorize|## Takeaways|## Key Points|## Summary)[\s\S]*?(?=##|$)/gi;
    const specialSections = [...content.matchAll(specialSectionRegex)];

    if (specialSections.length > 1) {
      // Keep only the last occurrence of these sections
      const lastSection = specialSections[specialSections.length - 1][0];
      let cleanedContent = content;

      for (let i = 0; i < specialSections.length - 1; i++) {
        cleanedContent = cleanedContent.replace(specialSections[i][0], "");
      }

      // Make sure the last section is still there
      if (!cleanedContent.includes(lastSection)) {
        cleanedContent += "\n\n" + lastSection;
      }

      // Clean up any double newlines created by removals
      content = cleanedContent.replace(/\n{3,}/g, "\n\n");
      console.log(
        `[Cleanup] Removed ${
          specialSections.length - 1
        } duplicate special sections`
      );
    }

    // 2. Check for repeated paragraphs
    const paragraphs = content.split(/\n\n+/);
    const uniqueParagraphs: string[] = [];
    let duplicatesRemoved = 0;

    for (const paragraph of paragraphs) {
      // Skip very short paragraphs
      if (paragraph.trim().length < 20) {
        uniqueParagraphs.push(paragraph);
        continue;
      }

      // Check if this paragraph is similar to any we've already seen
      const isDuplicate = uniqueParagraphs.some((existing) => {
        // Check for exact match or high similarity
        if (existing === paragraph) return true;

        // Check for high similarity (80% of words in common)
        const existingWords = new Set(existing.toLowerCase().split(/\s+/));
        const paragraphWords = paragraph.toLowerCase().split(/\s+/);
        const commonWords = paragraphWords.filter((word) =>
          existingWords.has(word)
        );

        return commonWords.length > 0.8 * paragraphWords.length;
      });

      if (!isDuplicate) {
        uniqueParagraphs.push(paragraph);
      } else {
        duplicatesRemoved++;
      }
    }

    if (duplicatesRemoved > 0) {
      content = uniqueParagraphs.join("\n\n");
      console.log(
        `[Cleanup] Removed ${duplicatesRemoved} duplicate paragraphs`
      );
    }

    // 3. Final formatting cleanup
    content = content
      // Fix multiple consecutive newlines
      .replace(/\n{3,}/g, "\n\n")
      // Fix multiple spaces
      .replace(/[ ]{2,}/g, " ")
      // Fix multiple bullet points
      .replace(/(\* .*\n)\s*\* /g, "$1* ");

    return content;
  } catch (error) {
    console.error("[Cleanup] Error during final cleanup:", error);
    // If cleanup fails, return the original content
    return content;
  }
}

export async function processContentWithChunking(
  outputType: string,
  metadata: ContentMetadata,
  content: string,
  limiter: RateLimiter,
  model: AIModel,
  chunkSize: number,
  overlap: number,
  onChunkProgress?: (chunkIndex: number, progress: number) => void
): Promise<string> {
  try {
    console.time("chunking-generation");
    // Generate prompts using chunking
    const prompts = generateChunkedPrompts(
      outputType,
      metadata,
      content,
      chunkSize,
      overlap
    );
    console.timeEnd("chunking-generation");

    // Log number of chunks for debugging
    console.log(`Generated ${prompts.length} chunks for processing`);

    // Optimize concurrency based on content size and chunk count
    // For many small chunks, we can process more concurrently
    // For fewer large chunks, limit concurrency to avoid timeouts
    let maxConcurrency = 2; // Default

    if (prompts.length <= 2) {
      maxConcurrency = 1; // Sequential for very few chunks
    } else if (prompts.length > 5) {
      // For many chunks, increase concurrency but cap at 3 to avoid rate limits
      maxConcurrency = Math.min(3, Math.ceil(prompts.length / 2));
    }

    console.log(
      `Using concurrency level: ${maxConcurrency} for ${prompts.length} chunks`
    );
    console.time("chunks-processing");

    // Process all chunks in parallel with controlled concurrency
    const chunkResults = await processChunksInParallel(
      prompts,
      limiter,
      model,
      maxConcurrency,
      onChunkProgress
    );
    console.timeEnd("chunks-processing");

    console.time("deduplication");
    // Combine results with deduplication
    let aiResponse = chunkResults[0] || "";

    console.log(
      `[Deduplication] Starting with first chunk (${aiResponse.length} chars)`
    );

    for (let i = 1; i < chunkResults.length; i++) {
      // Check if this chunk has content before trying to deduplicate
      if (!chunkResults[i] || chunkResults[i].trim().length === 0) {
        console.warn(`[Deduplication] Chunk ${i} has no content, skipping`);
        continue;
      }

      console.log(
        `[Deduplication] Processing chunk ${i} (${chunkResults[i].length} chars)`
      );

      // FIXED: Compare against the entire accumulated response so far
      // This prevents repetition across all previously processed chunks
      const dedupedContent = deduplicateContent(
        aiResponse, // Use the entire accumulated response instead of just the previous chunk
        chunkResults[i]
      );

      // Log deduplication results for debugging
      const originalLength = chunkResults[i].length;
      const dedupedLength = dedupedContent.length;
      const reductionPercent = (
        ((originalLength - dedupedLength) / originalLength) *
        100
      ).toFixed(1);

      console.log(
        `[Deduplication] Chunk ${i}: Original ${originalLength} chars → Deduped ${dedupedLength} chars (${reductionPercent}% reduction)`
      );

      // Only add non-empty content with proper spacing
      if (dedupedContent && dedupedContent.trim().length > 0) {
        aiResponse += "\n\n" + dedupedContent.trim();
      } else {
        console.log(
          `[Deduplication] Chunk ${i} was completely deduplicated (100% overlap)`
        );
      }
    }
    // Perform a final cleanup pass to remove any remaining repetitive content
    console.log(`[Deduplication] Performing final cleanup pass`);

    // Check for and remove any duplicate sections that might have been missed
    const finalCleanedResponse = performFinalCleanup(aiResponse);

    console.timeEnd("deduplication");

    // Validate final response
    if (!finalCleanedResponse || finalCleanedResponse.trim().length === 0) {
      throw new Error("Failed to generate content from chunks");
    }

    // Log the final size reduction
    const originalSize = aiResponse.length;
    const finalSize = finalCleanedResponse.length;
    const reductionPercent = (
      ((originalSize - finalSize) / originalSize) *
      100
    ).toFixed(1);

    console.log(
      `[Deduplication] Final result: ${originalSize} chars → ${finalSize} chars (${reductionPercent}% reduction)`
    );

    return finalCleanedResponse;
  } catch (error) {
    console.error("Error in chunked processing:", error);

    // Return a fallback message with error details
    return `# Content Processing Error

Unfortunately, there was an error processing this content. The content may be too complex or too large for the AI model to handle efficiently.

## Error Details
${error instanceof Error ? error.message : String(error)}

## Suggestions
- Try again with a smaller portion of the content
- Break the content into smaller sections
- Try a different output format
- If this is a PDF, ensure it contains actual text and not just images

We apologize for the inconvenience.`;
  }
}
