/**
 * Utility for tracking performance metrics in the application
 */

// Store performance metrics
interface PerformanceMetric {
  start: number;
  end?: number;
  duration?: number;
}

// Global metrics store
const metrics: Record<string, PerformanceMetric> = {};

/**
 * Start timing a specific operation
 * @param name Name of the operation to time
 */
export function startTiming(name: string): void {
  metrics[name] = {
    start: performance.now(),
  };
}

/**
 * End timing a specific operation and calculate duration
 * @param name Name of the operation to end timing
 * @returns Duration in milliseconds
 */
export function endTiming(name: string): number | undefined {
  const metric = metrics[name];
  if (!metric) {
    console.warn(`No timing started for: ${name}`);
    return undefined;
  }

  metric.end = performance.now();
  metric.duration = metric.end - metric.start;
  
  // Log the timing
  console.log(`⏱️ ${name}: ${metric.duration.toFixed(2)}ms`);
  
  return metric.duration;
}

/**
 * Time a function execution
 * @param name Name of the operation
 * @param fn Function to time
 * @returns Result of the function
 */
export async function timeAsync<T>(
  name: string,
  fn: () => Promise<T>
): Promise<T> {
  startTiming(name);
  try {
    return await fn();
  } finally {
    endTiming(name);
  }
}

/**
 * Get all recorded performance metrics
 */
export function getPerformanceMetrics(): Record<string, number> {
  const result: Record<string, number> = {};
  
  for (const [name, metric] of Object.entries(metrics)) {
    if (metric.duration !== undefined) {
      result[name] = metric.duration;
    }
  }
  
  return result;
}

/**
 * Clear all performance metrics
 */
export function clearPerformanceMetrics(): void {
  Object.keys(metrics).forEach(key => {
    delete metrics[key];
  });
}
