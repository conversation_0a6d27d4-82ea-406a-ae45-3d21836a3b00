import { ContentMetadata } from "@/types/contentTypes";

/**
 * Generate a combined prompt for a single chunk of content
 *
 * @param outputType The type of output to generate (summary or notes)
 * @param metadata Content metadata
 * @param contentText The content text
 * @param chunkNumber The current chunk number
 * @param totalChunks The total number of chunks
 * @returns A formatted prompt for the AI model
 */
export function generatePrompt(
  outputType: string,
  metadata: ContentMetadata,
  contentText: string,
  chunkNumber = 1,
  totalChunks = 1
): string {
  // Determine if we need to generate a title or keep the existing one
  const titleInstruction =
    metadata.title &&
    (metadata.contentType === "youtube" ||
      metadata.contentType === "pdf" ||
      metadata.contentType === "webpage")
      ? "- Keep the original title; do not modify."
      : "- Find the title in the content or if the first line looks like a title use it, or generate a concise, descriptive title that captures the main topic.";

  // Add content type specific instructions
  let contentTypeInstructions = "";
  if (metadata.contentType === "pdf") {
    contentTypeInstructions = `

# PDF-Specific Instructions

* Maintain the document's structure when possible
* Preserve important headings and sections
* Include page references when relevant (e.g., "As stated on page 5...")
* For academic papers, include key findings, methodology, and important statistics
* For technical documents, highlight important specifications, procedures, and include relevant code snippets
* Preserve mathematical formulas and technical terminology with proper formatting
  `;
  } else if (metadata.contentType === "webpage") {
    contentTypeInstructions = `

# Webpage-Specific Instructions

* Maintain the article's structure when possible
* Preserve important headings and sections
* Include source attribution when relevant
* For news articles, highlight key facts, quotes, and statistical evidence
* For blog posts, capture the main arguments, takeaways, and practical applications
* For technical content, highlight important specifications, procedures, and include relevant code snippets with proper language notation
  ${
    metadata.author
      ? `- Acknowledge the author (${metadata.author}) when appropriate`
      : ""
  }
  `;
  } else if (metadata.contentType === "youtube") {
    contentTypeInstructions = `

# Video-Specific Instructions

* Focus on the sequential structure of the tutorial/presentation
* Capture hands-on demonstrations and practical examples
* For technical tutorials, extract all code samples with proper language notation
* Highlight practical implementation details and troubleshooting tips
* Preserve demonstrations of tools/technologies in action
* For comparison segments (e.g., technology A vs B), create a clear comparative structure
  `;
  }

  // Determine chunk info based on output type
  const chunkInfo =
    totalChunks > 1
      ? `Note: This is part ${chunkNumber} of ${totalChunks} of the content. ${
          outputType === "summary"
            ? "Summarize this section while maintaining continuity with other parts."
            : "Ensure continuity with previous and next parts where applicable, avoiding redundancy in overlapping sections."
        }`
      : "";

  return `Create a structured, clear ${outputType} following these guidelines:

# Output Type Instructions

* If outputType is "summary":
  * Create a concise, scannable summary that highlights the most important concepts
  * Begin with a brief 1-2 sentence overview of the entire content
  * Use descriptive emoji icons (⚙️, 💾, 🧮, etc.) before main section headings to improve scannability
  * Format main headings in bold with a clear hierarchy
  * Focus on practical, actionable information
  * Keep explanations brief but comprehensive
  * Include only the most essential information
  * Use bullet points for clarity and readability
  * For technical content, maintain precise terminology while simplifying concepts
  * Include a "Facts to Memorize" section at the end with key points in a bulleted list
  * For pdf with multiple part/chapters try to keep each chapter short and concise and not too long

* If outputType is "notes":
  * Create comprehensive, detailed notes that thoroughly explain all concepts
  * Begin with an "Overall summary" section providing a brief overview
  * Use descriptive emoji icons (⚙️, 💾, 🧮, etc.) before main section headings
  * Format main headings in bold with a clear hierarchy
  * Include detailed explanations, examples, and applications
  * Use bullet points extensively for clarity and organization
  * For technical content, include formulas, definitions, and step-by-step explanations
  * For mathematical content, use proper formatting for equations
  * Include tables where appropriate to organize and compare information
  * End with practical takeaways or applications

${chunkInfo}

# Structure

1. **Title** (bold, descriptive title at the top)
   ${titleInstruction}

2. Brief overview (1-2 sentences summarizing the entire content)
   *Avoid using words like this content or this video in the overview 
   *Do not use the title in the overview

3. Main content sections:
   * Each main section should have:
     * A  big bold heading like h2
     * Clear, concise explanations using bullet points
     * Sub-sections with proper indentation when needed
     * Examples of the concept in action when needed especially for technical content
     * Include definitions, formulas, and examples
     * Use proper formatting for code, equations, and technical terms
     * Highlight important concepts with bold text
     * For mathematical content, strictly follow the "🧮 MATH CONTENT" guidelines below.
   * For instructional content:
     * Break down processes into clear steps
     * Highlight practical applications and examples
   *For pdf with multiple part each new part heading should be h2 not h1
   *Use direct address to engage the user, avoiding phrases like "the speaker", "The author", and "The video"

4. For summaries only: Include a "Facts to Memorize" section with key points
   * List the most important facts to remember
   * Keep each point brief and focused on one concept

5. For notes only: Include a "Takeaways" or summary section at the end
   * Highlight the most important concepts
   * Emphasize practical applications

# Special Content Handling

⌨️ CODING CONTENT

* Extract ALL code examples with complete implementation details
* Include setup requirements and dependencies
* Briefly explain each code snippet's purpose and functionality
* Format with \`\`\`[language]
  [code]
  \`\`\`
* Show execution commands and expected outputs where relevant
* Include error handling where demonstrated
* Only include code examples if the content has any code in it or is relevant to the content
* Do not render non code content as code

🧮 MATH CONTENT

* Render equations in $LaTeX$ format
* Provide step-by-step breakdowns of complex derivations
* only include math content if it is relevant to the content or provided in the content
* Include practical interpretations of mathematical concepts
* Include examples and also solve and explain them step by step
* Make sure the formulas and equations are properly formatted and easy to read
* Make equations and formulas big and bold and readable
* **Explanations & Illustrative Examples:**
  * For complex derivations or problem-solving steps, provide **clear, step-by-step breakdowns**. Explain the 'why' behind each step, not just the 'how'.
  * Include **practical interpretations** of mathematical concepts: what they mean in simple terms and why they are used.
  * Provide **concise, illustrative examples for key formulas or problem types**. If showing a solved example, explain each step of the solution clearly and simply.
    Example:
    * **Concept:** The Power Rule
    * **Formula:** $d/dx[x^n] = nx^{n-1}$
    * **Simple Example:** Find the derivative of $f(x) = x^3$.
        * Using the power rule, $n=3$.
        * So, $f'(x) = 3x^{3-1} = 3x^2$.
* **Terminology and Readability:**
  * Define mathematical terms and symbols clearly if they might be unfamiliar to a beginner.
  * Ensure a clear visual distinction between explanatory text and mathematical expressions/equations.

📝 TUTORIALS

* Create detailed step-by-step instructions with clear progression
* Include both what to do AND why it's being done
* Highlight common pitfalls or mistakes and their solutions
* Include testing/validation steps where relevant

🎧 AUDIO CONTENT

* Extract key arguments and their supporting evidence
* Include memorable quotes with proper attribution
* Capture the logical progression of ideas

📊 TABLES & VISUAL DATA

* Organize comparisons or structured information into Markdown tables
* Include headers and alignment for proper formatting
* For visual data described in content, attempt to recreate in text form where possible

${contentTypeInstructions}

Content Data:
Title: "${metadata.title || ""}"
Content Type: "${metadata.contentType || "unknown"}"
${
  metadata.contentType === "pdf" && metadata.pageCount
    ? `Page Count: ${metadata.pageCount}`
    : ""
}
${
  metadata.contentType === "webpage" && metadata.author
    ? `Author: ${metadata.author}`
    : ""
}
${
  metadata.contentType === "webpage" && metadata.url
    ? `Source: ${metadata.url}`
    : ""
}

Content:
${contentText}

Generate a comprehensive ${outputType} following ALL the rules above, focusing on clarity, structure, and practical value. Use emoji icons for main sections, proper formatting for technical content, and maintain a consistent hierarchy throughout.`;
}

/**
 * Generate a summary prompt (for backward compatibility)
 *
 * @param metadata Content metadata
 * @param contentText The content text
 * @param chunkNumber The current chunk number
 * @param totalChunks The total number of chunks
 * @returns A formatted prompt for the AI model
 */
export function generateSummaryPrompt(
  metadata: ContentMetadata,
  contentText: string,
  chunkNumber = 1,
  totalChunks = 1
): string {
  return generatePrompt(
    "summary",
    metadata,
    contentText,
    chunkNumber,
    totalChunks
  );
}

/**
 * Generate a notes prompt (for backward compatibility)
 *
 * @param metadata Content metadata
 * @param contentText The content text
 * @param chunkNumber The current chunk number
 * @param totalChunks The total number of chunks
 * @returns A formatted prompt for the AI model
 */
export function generateNotesPrompt(
  metadata: ContentMetadata,
  contentText: string,
  chunkNumber = 1,
  totalChunks = 1
): string {
  return generatePrompt(
    "notes",
    metadata,
    contentText,
    chunkNumber,
    totalChunks
  );
}
