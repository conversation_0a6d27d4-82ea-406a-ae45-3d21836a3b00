import { toast } from "sonner";

interface ShareOptions {
  title: string;
  text?: string;
  url: string;
  contentId: string;
}

/**
 * Handles sharing content with a server request and native share API
 * @param options Share options
 * @returns Success status
 */
export const handleShare = async ({
  title,
  text,
  url,
  contentId,
}: ShareOptions) => {
  try {
    // Try to get or generate share token first
    const response = await fetch("/api/share", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contentId,
        share: true,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to generate share link");
    }

    const data = await response.json();

    if (data.success && data.shareToken) {
      const shareUrl = `${window.location.origin}/share/${data.shareToken}`;

      // Try native share API
      if (navigator.share) {
        try {
          await navigator.share({
            title,
            text: text || `Check out this summary: ${title}`,
            url: shareUrl,
          });
          return true;
        } catch (error) {
          // Fall back to clipboard if share is cancelled
          console.log("Share failed, falling back to clipboard:", error);
        }
      }

      // Fallback to copying to clipboard
      await navigator.clipboard.writeText(shareUrl);
      return true;
    } else {
      throw new Error(data.error || "Failed to generate share link");
    }
  } catch (error) {
    console.error("Share error:", error);
    toast.error("Failed to share content");
    return false;
  }
};

/**
 * Handles unsharing content with a server request
 * @param contentId The ID of the content to unshare
 * @returns Object with success status
 */
export const handleUnshare = async (contentId: string) => {
  try {
    const response = await fetch("/api/share", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contentId,
        share: false,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to unshare content");
    }

    const data = await response.json();
    return {
      success: data.success,
    };
  } catch (error) {
    console.error("Error unsharing content:", error);
    toast.error("Failed to unshare content");
    return { success: false };
  }
};

/**
 * Gets existing share token or generates a new one if none exists
 * @param contentId The ID of the content to share
 * @param existingToken Optional existing share token to return if valid
 * @returns Object with success status and shareToken
 */
export const getOrGenerateShareToken = async (
  contentId: string,
  existingToken?: string
) => {
  // If we have an existing token, return it
  if (existingToken) {
    return {
      success: true,
      shareToken: existingToken,
    };
  }

  // Otherwise, generate a new token
  return await generateShareToken(contentId);
};

/**
 * Generates a share token for content (for use with ShareModal)
 * @param contentId The ID of the content to share
 * @param forceRegenerate Whether to force generation of a new token
 * @returns Object with success status and shareToken
 */
export const generateShareToken = async (
  contentId: string,
  forceRegenerate = false
) => {
  try {
    const response = await fetch("/api/share", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contentId,
        share: true,
        forceRegenerate, // Add this to force new token generation
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to generate share link");
    }

    const data = await response.json();
    return {
      success: data.success,
      shareToken: data.shareToken,
    };
  } catch (error) {
    console.error("Share token generation error:", error);
    return { success: false };
  }
};

/**
 * Generates a share URL for the given content ID
 * @param contentId The ID of the content to share
 * @param shareToken The share token for the content
 * @returns The share URL
 */
export const getShareUrl = (contentId: string, shareToken?: string) => {
  return shareToken
    ? `${window.location.origin}/share/${shareToken}`
    : `${window.location.origin}/transform/${contentId}`;
};
