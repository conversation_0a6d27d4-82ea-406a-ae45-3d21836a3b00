/**
 * Robust JSON Parser Utility
 *
 * This utility provides comprehensive JSON parsing using the battle-tested `jsonrepair` library
 * with additional validation and error handling for AI-generated content.
 */

import { jsonrepair } from "jsonrepair";

export interface ParseResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  method?: string;
  originalLength?: number;
  parsedLength?: number;
}

export interface JsonParserOptions {
  /** Enable debug logging */
  debug?: boolean;
  /** Expected data type for validation */
  expectedType?: "array" | "object" | "string" | "number" | "boolean";
  /** Custom validation function */
  validator?: (data: unknown) => boolean;
  /** Context for logging (e.g., 'quiz', 'flashcards', 'transform') */
  context?: string;
}

/**
 * Main robust JSON parsing function using jsonrepair library
 */
export function parseRobustJson<T = unknown>(
  input: string,
  options: JsonParserOptions = {}
): ParseResult<T> {
  const {
    debug = false,
    expectedType,
    validator,
    context = "unknown",
  } = options;

  if (debug) {
    console.log(`[JSON Parser] Starting robust parsing for ${context}`);
    console.log(`[JSON Parser] Input length: ${input.length}`);
    console.log(`[JSON Parser] Input preview: ${input.substring(0, 200)}...`);
  }

  const originalLength = input.length;

  // Strategy 1: Try standard JSON.parse first (fastest)
  try {
    const trimmed = input.trim();
    const data = JSON.parse(trimmed) as T;
    
    if (validateResult(data, expectedType, validator, debug)) {
      if (debug) {
        console.log(`[JSON Parser] Standard parse successful for ${context}`);
      }
      return {
        success: true,
        data,
        method: 'standard',
        originalLength,
        parsedLength: trimmed.length
      };
    }
  } catch (standardError) {
    if (debug) {
      console.log(`[JSON Parser] Standard parse failed for ${context}:`, standardError instanceof Error ? standardError.message : String(standardError));
    }
  }

  // Strategy 2: Use jsonrepair library to fix and parse
  try {
    const repairedJson = jsonrepair(input);
    const data = JSON.parse(repairedJson) as T;
    
    if (validateResult(data, expectedType, validator, debug)) {
      if (debug) {
        console.log(`[JSON Parser] jsonrepair successful for ${context}`);
      }
      return {
        success: true,
        data,
        method: 'jsonrepair',
        originalLength,
        parsedLength: repairedJson.length
      };
    }
  } catch (repairError) {
    if (debug) {
      console.log(`[JSON Parser] jsonrepair failed for ${context}:`, repairError instanceof Error ? repairError.message : String(repairError));
    }
  }

  // Strategy 3: Extract JSON from mixed content (for AI responses with explanations)
  try {
    const patterns = [
      /\[[\s\S]*\]/,  // Array pattern
      /\{[\s\S]*\}/,  // Object pattern
    ];

    for (const pattern of patterns) {
      const match = input.match(pattern);
      if (match) {
        try {
          // Try standard parse first
          const data = JSON.parse(match[0]) as T;
          if (validateResult(data, expectedType, validator, debug)) {
            if (debug) {
              console.log(`[JSON Parser] Pattern extraction successful for ${context}`);
            }
            return {
              success: true,
              data,
              method: 'pattern-standard',
              originalLength,
              parsedLength: match[0].length
            };
          }
        } catch {
          // Try jsonrepair on extracted content
          try {
            const repairedJson = jsonrepair(match[0]);
            const data = JSON.parse(repairedJson) as T;
            if (validateResult(data, expectedType, validator, debug)) {
              if (debug) {
                console.log(`[JSON Parser] Pattern extraction with repair successful for ${context}`);
              }
              return {
                success: true,
                data,
                method: 'pattern-repair',
                originalLength,
                parsedLength: repairedJson.length
              };
            }
          } catch {
            continue;
          }
        }
      }
    }
  } catch (extractError) {
    if (debug) {
      console.log(`[JSON Parser] Pattern extraction failed for ${context}:`, extractError instanceof Error ? extractError.message : String(extractError));
    }
  }

  // All strategies failed
  if (debug) {
    console.error(`[JSON Parser] All parsing strategies failed for ${context}`);
  }

  return {
    success: false,
    error: `Failed to parse JSON after trying all strategies. Input length: ${input.length}`,
    originalLength: input.length
  };
}

/**
 * Validate parsing result against expected type and custom validator
 */
function validateResult<T>(
  data: T | undefined,
  expectedType?: string,
  validator?: (data: unknown) => boolean,
  debug?: boolean
): boolean {
  if (data === undefined) return false;

  // Type validation
  if (expectedType) {
    const actualType = Array.isArray(data) ? "array" : typeof data;
    if (actualType !== expectedType) {
      if (debug) {
        console.log(
          `[JSON Parser] Type validation failed. Expected: ${expectedType}, Got: ${actualType}`
        );
      }
      return false;
    }
  }

  // Custom validation
  if (validator && !validator(data)) {
    if (debug) {
      console.log(`[JSON Parser] Custom validation failed`);
    }
    return false;
  }

  return true;
}

// Convenience functions for specific use cases

/**
 * Parse quiz questions with robust error handling
 */
export function parseQuizQuestions(
  input: string,
  debug = false
): ParseResult<
  Array<{
    question: string;
    options: string[];
    correctAnswer: number;
    explanation: string;
  }>
> {
  return parseRobustJson(input, {
    debug,
    expectedType: "array",
    context: "quiz",
    validator: (data) => {
      if (!Array.isArray(data)) return false;
      return data.every(
        (item) =>
          typeof item === "object" &&
          item !== null &&
          typeof item.question === "string" &&
          Array.isArray(item.options) &&
          typeof item.correctAnswer === "number" &&
          typeof item.explanation === "string"
      );
    },
  });
}

/**
 * Parse flashcards with robust error handling
 */
export function parseFlashcards(
  input: string,
  debug = false
): ParseResult<
  Array<{
    front: string;
    back: string;
  }>
> {
  return parseRobustJson(input, {
    debug,
    expectedType: "array",
    context: "flashcards",
    validator: (data) => {
      if (!Array.isArray(data)) return false;
      return data.every(
        (item) =>
          typeof item === "object" &&
          item !== null &&
          typeof item.front === "string" &&
          typeof item.back === "string"
      );
    },
  });
}

/**
 * Parse any JSON with basic error handling
 */
export function parseJsonSafely<T = unknown>(
  input: string,
  debug = false
): ParseResult<T> {
  return parseRobustJson<T>(input, { debug });
}
