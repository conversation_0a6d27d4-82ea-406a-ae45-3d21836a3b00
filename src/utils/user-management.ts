import { db, users } from "@/lib/db";
import { User as SupabaseUser } from "@supabase/supabase-js";
import { createClient } from "@/utils/supabase/server";
import { eq } from "drizzle-orm";

/**
 * Syncs a Supabase user with our database User model
 * Creates or updates the user record in our database
 */
export async function syncUserWithDatabase(supabaseUser: SupabaseUser) {
  if (!supabaseUser?.id) {
    throw new Error("Invalid user data provided for sync");
  }

  try {
    // Try to find the existing user
    const existingUserResult = await db
      .select()
      .from(users)
      .where(eq(users.id, supabaseUser.id))
      .limit(1);

    let user = existingUserResult[0];

    if (user) {
      // Update existing user
      const updatedUserResult = await db
        .update(users)
        .set({
          email: supabaseUser.email,
          displayName:
            supabaseUser.user_metadata?.full_name ||
            supabaseUser.user_metadata?.name,
          avatarUrl: supabaseUser.user_metadata?.avatar_url,
          updatedAt: new Date(),
        })
        .where(eq(users.id, supabaseUser.id))
        .returning();

      user = updatedUserResult[0];
    } else {
      // Create new user
      const newUserResult = await db
        .insert(users)
        .values({
          id: supabaseUser.id,
          email: supabaseUser.email,
          displayName:
            supabaseUser.user_metadata?.full_name ||
            supabaseUser.user_metadata?.name,
          avatarUrl: supabaseUser.user_metadata?.avatar_url,
        })
        .returning();

      user = newUserResult[0];
    }

    return user;
  } catch (error) {
    console.error("Error syncing user with database:", error);
    throw error;
  }
}

/**
 * Gets the current user from Supabase auth and syncs with our database
 * Returns both the Supabase user and our database user
 */
export async function getCurrentUser() {
  const supabase = await createClient();

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      return { supabaseUser: null, dbUser: null };
    }

    // Sync with database and return both
    const dbUser = await syncUserWithDatabase(user);
    return { supabaseUser: user, dbUser };
  } catch (error) {
    console.error("Error getting current user:", error);
    return { supabaseUser: null, dbUser: null };
  }
}
