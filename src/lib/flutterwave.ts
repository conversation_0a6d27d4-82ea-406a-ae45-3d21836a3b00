import Flutterwave from "flutterwave-node-v3";
import {
  getFlutterwavePlanCode,
  getPlanAmount,
  convertToFlutterwaveAmount,
  Region,
} from "./flutterwave-config";
import { PlanType } from "@/data/pricingData";

// Initialize Flutterwave
const flw = new Flutterwave(
  process.env.FLUTTERWAVE_PUBLIC_KEY!,
  process.env.FLUTTERWAVE_SECRET_KEY!
);

if (
  !process.env.FLUTTERWAVE_PUBLIC_KEY ||
  !process.env.FLUTTERWAVE_SECRET_KEY
) {
  throw new Error(
    "FLUTTERWAVE_PUBLIC_KEY and FLUTTERWAVE_SECRET_KEY are required"
  );
}

export interface FlutterwaveCustomer {
  email: string;
  name: string;
  phone?: string;
}

export interface FlutterwaveSubscriptionResponse {
  status: "success" | "error";
  message: string;
  data?: {
    link?: string;
    id?: number;
    tx_ref?: string;
    flw_ref?: string;
  };
}

export interface FlutterwavePaymentData {
  tx_ref: string;
  amount: number;
  currency: string;
  redirect_url: string;
  customer: FlutterwaveCustomer;
  customizations: {
    title: string;
    description: string;
    logo?: string;
  };
  payment_plan?: string;
}

/**
 * Create a subscription payment link using Flutterwave
 */
export async function createSubscription(
  customer: FlutterwaveCustomer,
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): Promise<FlutterwaveSubscriptionResponse> {
  try {
    if (planType === "Free") {
      throw new Error("Cannot create subscription for free plan");
    }

    // Get plan code and amount
    const planCode = getFlutterwavePlanCode(planType, billingCycle, region);
    const amount = getPlanAmount(planType, billingCycle, region);
    const currency = region === "nigeria" ? "NGN" : "USD";

    // Generate unique transaction reference
    const txRef = `qlipify_${planType.toLowerCase()}_${billingCycle}_${Date.now()}_${Math.random()
      .toString(36)
      .substr(2, 9)}`;

    const paymentData: FlutterwavePaymentData = {
      tx_ref: txRef,
      amount: amount,
      currency: currency,
      redirect_url: `${
        process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
      }/api/subscriptions/callback`,
      customer: {
        email: customer.email,
        name: customer.name,
        phone: customer.phone || "",
      },
      customizations: {
        title: "Qlipify Subscription",
        description: `${planType} Plan - ${billingCycle} billing`,
        logo: `${
          process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
        }/logo.png`,
      },
      payment_plan: planCode,
    };

    console.log("🔄 Creating Flutterwave subscription with data:", {
      tx_ref: paymentData.tx_ref,
      amount: paymentData.amount,
      currency: paymentData.currency,
      plan: planCode,
      customer: paymentData.customer.email,
    });

    // Create payment link using Flutterwave Standard
    const response = await flw.Payment.standard(paymentData);

    console.log("📦 Flutterwave response:", JSON.stringify(response, null, 2));

    if (response.status === "success" && response.data?.link) {
      return {
        status: "success",
        message: "Subscription created successfully",
        data: {
          link: response.data.link,
          tx_ref: txRef,
          id: response.data.id,
        },
      };
    } else {
      throw new Error(response.message || "Failed to create subscription");
    }
  } catch (error) {
    console.error("❌ Flutterwave subscription creation error:", error);
    return {
      status: "error",
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Verify a transaction using Flutterwave
 */
export async function verifyTransaction(transactionId: string): Promise<any> {
  try {
    console.log("🔍 Verifying transaction:", transactionId);

    const response = await flw.Transaction.verify({ id: transactionId });

    console.log(
      "✅ Transaction verification response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Transaction verification error:", error);
    throw error;
  }
}

/**
 * Cancel a subscription using Flutterwave
 */
export async function cancelSubscription(subscriptionId: string): Promise<any> {
  try {
    console.log("🚫 Cancelling subscription:", subscriptionId);

    // Flutterwave subscription cancellation
    const response = await flw.Subscription.cancel({ id: subscriptionId });

    console.log(
      "✅ Subscription cancellation response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Subscription cancellation error:", error);
    throw error;
  }
}

/**
 * Fetch subscription details from Flutterwave
 */
export async function fetchSubscription(subscriptionId: string): Promise<any> {
  try {
    console.log("📋 Fetching subscription:", subscriptionId);

    const response = await flw.Subscription.fetch({ id: subscriptionId });

    console.log(
      "📄 Subscription fetch response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Subscription fetch error:", error);
    throw error;
  }
}

/**
 * Activate a subscription using Flutterwave
 */
export async function activateSubscription(
  subscriptionId: string
): Promise<any> {
  try {
    console.log("🔄 Activating subscription:", subscriptionId);

    const response = await flw.Subscription.activate({ id: subscriptionId });

    console.log(
      "✅ Subscription activation response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Subscription activation error:", error);
    throw error;
  }
}

/**
 * Get all subscriptions for a customer
 */
export async function getCustomerSubscriptions(
  customerEmail: string
): Promise<any> {
  try {
    console.log("📋 Fetching subscriptions for customer:", customerEmail);

    // Note: Flutterwave doesn't have a direct method to get subscriptions by customer email
    // This would typically require storing subscription IDs in your database
    // For now, we'll return an empty array and handle this through our database

    return {
      status: "success",
      data: [],
      message: "Customer subscriptions retrieved (handled via database)",
    };
  } catch (error) {
    console.error("❌ Customer subscriptions fetch error:", error);
    throw error;
  }
}

// Export the Flutterwave client for direct access if needed
export { flw as flutterwaveClient };

// Legacy export for backward compatibility during migration
export const paymentsClient = {
  createSubscription,
  verifyTransaction,
  cancelSubscription,
  fetchSubscription,
  activateSubscription,
  getCustomerSubscriptions,
};

export default paymentsClient;
