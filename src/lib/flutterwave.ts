import Flutterwave from "flutterwave-node-v3";
import { getFlutterwavePlanCode, Region } from "./flutterwave-config";
import { PlanType } from "@/data/pricingData";

// Flutterwave keys - using environment variables for security
const FLUTTERWAVE_PUBLIC_KEY = process.env.FLUTTERWAVE_PUBLIC_KEY;
const FLUTTERWAVE_SECRET_KEY = process.env.FLUTTERWAVE_SECRET_KEY;

if (!FLUTTERWAVE_PUBLIC_KEY || !FLUTTERWAVE_SECRET_KEY) {
  throw new Error(
    "FLUTTERWAVE_PUBLIC_KEY and FLUTTERWAVE_SECRET_KEY environment variables are required"
  );
}

// Initialize Flutterwave
const flw = new Flutterwave(FLUTTERWAVE_PUBLIC_KEY, FLUTTERWAVE_SECRET_KEY);

export interface FlutterwaveCustomer {
  email: string;
  name: string;
  phone?: string;
}

export interface FlutterwaveSubscriptionResponse {
  status: string;
  message: string;
  data?: {
    link?: string;
    id?: number;
    tx_ref?: string;
    flw_ref?: string;
  };
}

export interface FlutterwavePaymentData {
  tx_ref: string;
  amount: number;
  currency: string;
  redirect_url: string;
  customer: FlutterwaveCustomer;
  customizations: {
    title: string;
    description: string;
    logo?: string;
  };
  payment_plan?: number;
}

export interface FlutterwaveTransactionResponse {
  status: string;
  message: string;
  data?: {
    id: number;
    tx_ref: string;
    flw_ref: string;
    amount: number;
    currency: string;
    status: string;
    customer: FlutterwaveCustomer;
  };
}

export interface FlutterwaveGenericResponse {
  status: string;
  message: string;
  data?: any;
}

/**
 * Create a subscription payment link using Flutterwave
 */
export async function createSubscription(
  customer: FlutterwaveCustomer,
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): Promise<FlutterwaveSubscriptionResponse> {
  try {
    if (planType === "Free") {
      throw new Error("Cannot create subscription for free plan");
    }

    // Get plan code and verify plan details
    const planCode = getFlutterwavePlanCode(planType, billingCycle, region);

    // Fetch plan details to verify it exists and get correct amount
    console.log("🔍 Verifying plan details for plan ID:", planCode);
    const planDetails = await getPaymentPlan(planCode);

    if (planDetails.status !== "success" || !planDetails.data) {
      throw new Error(
        `Invalid plan ID: ${planCode}. Plan not found in Flutterwave dashboard.`
      );
    }

    // Use the actual amount from Flutterwave plan
    const amount = planDetails.data.amount;
    const currency = planDetails.data.currency;

    console.log("✅ Plan verified:", {
      id: planDetails.data.id,
      name: planDetails.data.name,
      amount: amount,
      currency: currency,
      interval: planDetails.data.interval,
      status: planDetails.data.status,
    });

    // Generate unique transaction reference
    const txRef = `qlipify_${planType.toLowerCase()}_${billingCycle}_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    const paymentData: FlutterwavePaymentData = {
      tx_ref: txRef,
      amount: amount,
      currency: currency,
      redirect_url: `${
        process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
      }/api/subscriptions/callback`,
      customer: {
        email: customer.email,
        name: customer.name,
        phone: customer.phone || "",
      },
      customizations: {
        title: "Qlipify Subscription",
        description: `${planType} Plan - ${billingCycle} billing`,
        logo: `${
          process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
        }/logo.png`,
      },
      payment_plan: parseInt(planCode), // Convert to number as required by Flutterwave
    };

    console.log("🔄 Creating Flutterwave subscription with data:", {
      tx_ref: paymentData.tx_ref,
      amount: paymentData.amount,
      currency: paymentData.currency,
      plan: planCode,
      customer: paymentData.customer.email,
    });

    // Create payment link using Flutterwave Standard API
    const response = await fetch("https://api.flutterwave.com/v3/payments", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(paymentData),
    });

    const responseData = await response.json();
    console.log(
      "📦 Flutterwave response:",
      JSON.stringify(responseData, null, 2)
    );

    if (responseData.status === "success" && responseData.data?.link) {
      return {
        status: "success",
        message: "Subscription created successfully",
        data: {
          link: responseData.data.link,
          tx_ref: txRef,
          id: responseData.data.id,
        },
      };
    } else {
      throw new Error(responseData.message || "Failed to create subscription");
    }
  } catch (error) {
    console.error("❌ Flutterwave subscription creation error:", error);
    return {
      status: "error",
      message:
        error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Get payment plan details from Flutterwave
 */
export async function getPaymentPlan(
  planId: string
): Promise<FlutterwaveGenericResponse> {
  try {
    console.log("📋 Fetching payment plan:", planId);

    const response = await fetch(
      `https://api.flutterwave.com/v3/payment-plans/${planId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const responseData = await response.json();

    console.log(
      "📄 Payment plan details:",
      JSON.stringify(responseData, null, 2)
    );

    return responseData;
  } catch (error) {
    console.error("❌ Payment plan fetch error:", error);
    throw error;
  }
}

/**
 * Verify a transaction using Flutterwave
 */
export async function verifyTransaction(
  transactionId: string
): Promise<FlutterwaveTransactionResponse> {
  try {
    console.log("🔍 Verifying transaction:", transactionId);

    const response = await flw.Transaction.verify({ id: transactionId });

    console.log(
      "✅ Transaction verification response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Transaction verification error:", error);
    throw error;
  }
}

/**
 * Cancel a subscription using Flutterwave SDK
 */
export async function cancelSubscription(
  subscriptionId: string
): Promise<FlutterwaveGenericResponse> {
  try {
    console.log("🚫 Cancelling subscription:", subscriptionId);

    // Use Flutterwave SDK for subscription cancellation
    const response = await flw.Subscription.cancel({ id: subscriptionId });

    console.log(
      "✅ Subscription cancellation response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Subscription cancellation error:", error);
    throw error;
  }
}

/**
 * Fetch subscription details from Flutterwave
 */
export async function fetchSubscription(
  subscriptionId: string
): Promise<FlutterwaveGenericResponse> {
  try {
    console.log("📋 Fetching subscription:", subscriptionId);

    // Use direct API call to get subscription details
    const response = await fetch(
      `https://api.flutterwave.com/v3/subscriptions/${subscriptionId}`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const responseData = await response.json();

    console.log(
      "📄 Subscription fetch response:",
      JSON.stringify(responseData, null, 2)
    );

    return responseData;
  } catch (error) {
    console.error("❌ Subscription fetch error:", error);
    throw error;
  }
}

/**
 * Activate a subscription using Flutterwave SDK
 */
export async function activateSubscription(
  subscriptionId: string
): Promise<FlutterwaveGenericResponse> {
  try {
    console.log("🔄 Activating subscription:", subscriptionId);

    // Use Flutterwave SDK for subscription activation
    const response = await flw.Subscription.activate({ id: subscriptionId });

    console.log(
      "✅ Subscription activation response:",
      JSON.stringify(response, null, 2)
    );

    return response;
  } catch (error) {
    console.error("❌ Subscription activation error:", error);
    throw error;
  }
}

/**
 * Get all subscriptions for a customer using Flutterwave API
 */
export async function getCustomerSubscriptions(
  customerEmail: string
): Promise<FlutterwaveGenericResponse> {
  try {
    console.log("📋 Fetching subscriptions for customer:", customerEmail);

    // Use direct API call to get all subscriptions
    const response = await fetch(
      "https://api.flutterwave.com/v3/subscriptions",
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${FLUTTERWAVE_SECRET_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const responseData = await response.json();

    // Filter subscriptions by customer email if data exists
    if (responseData.status === "success" && responseData.data) {
      responseData.data = responseData.data.filter(
        (sub: any) => sub.customer?.email === customerEmail
      );
    }

    console.log(
      "📄 Customer subscriptions response:",
      JSON.stringify(responseData, null, 2)
    );

    return responseData;
  } catch (error) {
    console.error("❌ Customer subscriptions fetch error:", error);
    throw error;
  }
}

// Export the Flutterwave client for direct access if needed
export { flw as flutterwaveClient };

// Legacy export for backward compatibility during migration
export const paymentsClient = {
  createSubscription,
  getPaymentPlan,
  verifyTransaction,
  cancelSubscription,
  fetchSubscription,
  activateSubscription,
  getCustomerSubscriptions,
};

export default paymentsClient;
