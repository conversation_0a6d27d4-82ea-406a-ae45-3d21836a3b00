import { PlanType } from "@/data/pricingData";

// Flutterwave Plan Codes - Hardcoded as per requirements
export const FLUTTERWAVE_PLAN_CODES = {
  // Nigerian Plans (NGN)
  nigeria: {
    starter_monthly: "PLN_starter_monthly_ngn",
    starter_annual: "PLN_starter_annual_ngn", 
    pro_monthly: "PLN_pro_monthly_ngn",
    pro_annual: "PLN_pro_annual_ngn",
    unlimited_monthly: "PLN_unlimited_monthly_ngn",
    unlimited_annual: "PLN_unlimited_annual_ngn",
  },
  // International Plans (USD)
  international: {
    starter_monthly: "PLN_starter_monthly_usd",
    starter_annual: "PLN_starter_annual_usd",
    pro_monthly: "PLN_pro_monthly_usd", 
    pro_annual: "PLN_pro_annual_usd",
    unlimited_monthly: "PLN_unlimited_monthly_usd",
    unlimited_annual: "PLN_unlimited_annual_usd",
  }
} as const;

// Pricing structure for both regions
export const FLUTTERWAVE_PRICING = {
  nigeria: {
    currency: "NGN",
    symbol: "₦",
    discountPercent: 15,
    plans: {
      Starter: {
        monthly: 4000,
        annual: 40800, // 15% discount
      },
      Pro: {
        monthly: 8000,
        annual: 81600, // 15% discount
      },
      Unlimited: {
        monthly: 20000,
        annual: 204000, // 15% discount
      },
    },
  },
  international: {
    currency: "USD",
    symbol: "$",
    discountPercent: 20,
    plans: {
      Starter: {
        monthly: 10,
        annual: 96, // 20% discount
      },
      Pro: {
        monthly: 20,
        annual: 192, // 20% discount
      },
      Unlimited: {
        monthly: 50,
        annual: 480, // 20% discount
      },
    },
  },
} as const;

export type Region = "nigeria" | "international";

/**
 * Get the appropriate plan code based on user location
 */
export const getFlutterwavePlanCode = (
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): string => {
  if (planType === "Free") {
    throw new Error("Cannot get plan code for free plan");
  }

  const planKey = `${planType.toLowerCase()}_${billingCycle}` as keyof typeof FLUTTERWAVE_PLAN_CODES.nigeria;
  return FLUTTERWAVE_PLAN_CODES[region][planKey];
};

/**
 * Get pricing information based on user location
 */
export const getFlutterwavePricing = (region: Region) => {
  return FLUTTERWAVE_PRICING[region];
};

/**
 * Determine user region based on location data
 * This will be used by the frontend to determine pricing
 */
export const determineUserRegion = (countryCode?: string): Region => {
  // Nigeria uses NGN pricing
  if (countryCode === "NG") {
    return "nigeria";
  }
  // All other countries use USD pricing
  return "international";
};

/**
 * Get plan amount for a specific plan and billing cycle
 */
export const getPlanAmount = (
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): number => {
  if (planType === "Free") {
    return 0;
  }

  const pricing = FLUTTERWAVE_PRICING[region];
  return pricing.plans[planType][billingCycle];
};

/**
 * Convert amount to Flutterwave format (kobo for NGN, cents for USD)
 */
export const convertToFlutterwaveAmount = (amount: number, currency: string): number => {
  // Flutterwave expects amounts in the smallest currency unit
  // NGN: kobo (multiply by 100)
  // USD: cents (multiply by 100)
  return Math.round(amount * 100);
};

/**
 * Convert from Flutterwave format back to main currency unit
 */
export const convertFromFlutterwaveAmount = (amount: number, currency: string): number => {
  return amount / 100;
};
