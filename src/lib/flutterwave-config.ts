import { PlanType } from "@/data/pricingData";

// Flutterwave Plan IDs - Hardcoded from dashboard (these are the actual plan IDs, not secrets)
export const FLUTTERWAVE_PLAN_CODES = {
  // Nigerian Plans (NGN)
  nigeria: {
    starter_monthly: "174408", // starter-monthly (NGN) - NGN 4,000.00
    starter_annual: "174412", // qlipify-starter-annual (NGN) - NGN 40,800.00
    pro_monthly: "174402", // Qlipify-pro-monthly (NGN) - NGN 8,000.00
    pro_annual: "174410", // qlipify-pro-annual (NGN) - NGN 81,600.00
    unlimited_monthly: "174403", // Qlipify-unlimited-monthly (NGN) - NGN 20,000.00
    unlimited_annual: "174411", // qlipify-unlimited-annual (NGN) - NGN 204,000.00
  },
  // International Plans (USD)
  international: {
    starter_monthly: "174405", // Starter Monthly (USD) - USD 5.99
    starter_annual: "174414", // Qlipify Starter Annual (USD) - USD 58.00
    pro_monthly: "174409", // Qlipify Pro Monthly (USD) - USD 11.99
    pro_annual: "174415", // Qlipify Pro Annual (USD) - USD 115.00
    unlimited_monthly: "174413", // Qlipify Unlimited Monthly (USD) - USD 25.00
    unlimited_annual: "174416", // Qlipify Unlimited Annual (USD) - USD 204.00
  },
} as const;

// Pricing structure for both regions
export const FLUTTERWAVE_PRICING = {
  nigeria: {
    currency: "NGN",
    symbol: "₦",
    discountPercent: 15,
    plans: {
      Starter: {
        monthly: 4000,
        annual: 40800, // 15% discount
      },
      Pro: {
        monthly: 8000,
        annual: 81600, // 15% discount
      },
      Unlimited: {
        monthly: 20000,
        annual: 204000, // 15% discount
      },
    },
  },
  international: {
    currency: "USD",
    symbol: "$",
    discountPercent: 20,
    plans: {
      Starter: {
        monthly: 10,
        annual: 96, // 20% discount
      },
      Pro: {
        monthly: 20,
        annual: 192, // 20% discount
      },
      Unlimited: {
        monthly: 50,
        annual: 480, // 20% discount
      },
    },
  },
} as const;

export type Region = "nigeria" | "international";

/**
 * Get the appropriate plan code based on user location
 */
export const getFlutterwavePlanCode = (
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): string => {
  if (planType === "Free") {
    throw new Error("Cannot get plan code for free plan");
  }

  const planKey =
    `${planType.toLowerCase()}_${billingCycle}` as keyof typeof FLUTTERWAVE_PLAN_CODES.nigeria;
  return FLUTTERWAVE_PLAN_CODES[region][planKey];
};

/**
 * Get pricing information based on user location
 */
export const getFlutterwavePricing = (region: Region) => {
  return FLUTTERWAVE_PRICING[region];
};

/**
 * Determine user region based on location data
 * This will be used by the frontend to determine pricing
 */
export const determineUserRegion = (countryCode?: string): Region => {
  // Nigeria uses NGN pricing
  if (countryCode === "NG") {
    return "nigeria";
  }
  // All other countries use USD pricing
  return "international";
};

/**
 * Get plan amount for a specific plan and billing cycle
 */
export const getPlanAmount = (
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): number => {
  if (planType === "Free") {
    return 0;
  }

  const pricing = FLUTTERWAVE_PRICING[region];
  return pricing.plans[planType][billingCycle];
};

/**
 * Convert amount to Flutterwave format (kobo for NGN, cents for USD)
 */
export const convertToFlutterwaveAmount = (
  amount: number,
  currency: string
): number => {
  // Flutterwave expects amounts in the smallest currency unit
  // NGN: kobo (multiply by 100)
  // USD: cents (multiply by 100)
  return Math.round(amount * 100);
};

/**
 * Convert from Flutterwave format back to main currency unit
 */
export const convertFromFlutterwaveAmount = (
  amount: number,
  currency: string
): number => {
  return amount / 100;
};
