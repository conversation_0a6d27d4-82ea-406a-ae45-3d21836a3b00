import { PlanType } from "@/data/pricingData";
import { 
  Region, 
  getFlutterwavePricing, 
  determineUserRegion,
  getPlanAmount 
} from "./flutterwave-config";

export interface LocationPricingData {
  currency: string;
  symbol: string;
  discountPercent: number;
  region: Region;
  plans: Record<Exclude<PlanType, "Free">, {
    monthly: number;
    annual: number;
    features: string[];
    description: string;
    color: string;
    iconColor: string;
    buttonVariant: "outline" | "default";
    isPopular: boolean;
  }>;
}

/**
 * Get user's location using browser geolocation API
 * Returns a promise that resolves to country code or null
 */
export const getUserLocation = async (): Promise<string | null> => {
  try {
    // First try to get location from browser geolocation
    if (navigator.geolocation) {
      return new Promise((resolve) => {
        navigator.geolocation.getCurrentPosition(
          async (position) => {
            try {
              // Use reverse geocoding to get country code
              const response = await fetch(
                `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`
              );
              const data = await response.json();
              resolve(data.countryCode || null);
            } catch {
              resolve(null);
            }
          },
          () => {
            // Fallback to IP-based detection
            resolve(getLocationFromIP());
          },
          { timeout: 5000 }
        );
      });
    } else {
      // Fallback to IP-based detection
      return getLocationFromIP();
    }
  } catch {
    return null;
  }
};

/**
 * Get user's location from IP address
 */
const getLocationFromIP = async (): Promise<string | null> => {
  try {
    const response = await fetch('https://ipapi.co/country_code/');
    const countryCode = await response.text();
    return countryCode.trim() || null;
  } catch {
    return null;
  }
};

/**
 * Get pricing data based on user's location
 */
export const getLocationBasedPricing = async (): Promise<LocationPricingData> => {
  let countryCode: string | null = null;
  
  try {
    countryCode = await getUserLocation();
  } catch {
    // Default to international if location detection fails
    countryCode = null;
  }

  const region = determineUserRegion(countryCode || undefined);
  const pricing = getFlutterwavePricing(region);

  // Base plan features (same for all regions)
  const basePlanFeatures = {
    Starter: {
      description: "Perfect for students who need more study materials.",
      features: [
        "10 content transformations per week",
        "Generate up to 3 sets of flashcards per content",
        "Generate up to 3 sets of quizzes per content",
        "Limited AI chat (20 questions per day)",
        "Share study materials with others",
        "Translate content to multiple languages",
      ],
      color: "bg-blue-100 dark:bg-blue-950/40",
      iconColor: "text-blue-500",
      buttonVariant: "outline" as const,
      isPopular: false,
    },
    Pro: {
      description: "Great for serious students with heavy study loads.",
      features: [
        "20 content transformations per week",
        "Generate up to 7 sets of flashcards per content",
        "Generate up to 7 sets of quizzes per content",
        "Limited AI chat (40 questions per day)",
        "Advanced analytics dashboard",
        "Share study materials with others",
        "Translate content to multiple languages",
      ],
      color: "bg-purple-100 dark:bg-purple-950/40",
      iconColor: "text-purple-500",
      buttonVariant: "default" as const,
      isPopular: true,
    },
    Unlimited: {
      description: "Unlimited everything for students who convert lots of content.",
      features: [
        "Unlimited content transformations",
        "Unlimited quiz and flashcard generations",
        "All Pro features included",
        "First access to new features",
        "Premium support",
        "24/7 dedicated customer service",
      ],
      color: "bg-emerald-100 dark:bg-emerald-950/40",
      iconColor: "text-emerald-500",
      buttonVariant: "outline" as const,
      isPopular: false,
    },
  };

  // Combine pricing with features
  const plans = Object.entries(basePlanFeatures).reduce((acc, [planName, planData]) => {
    const planType = planName as Exclude<PlanType, "Free">;
    acc[planType] = {
      ...planData,
      monthly: pricing.plans[planType].monthly,
      annual: pricing.plans[planType].annual,
    };
    return acc;
  }, {} as LocationPricingData["plans"]);

  return {
    currency: pricing.currency,
    symbol: pricing.symbol,
    discountPercent: pricing.discountPercent,
    region,
    plans,
  };
};

/**
 * Format price with appropriate currency symbol
 */
export const formatPrice = (amount: number, currency: string, symbol: string): string => {
  if (currency === "NGN") {
    return `${symbol}${amount.toLocaleString()}`;
  } else {
    return `${symbol}${amount}`;
  }
};

/**
 * Get user region from server-side (for API routes)
 * Uses request headers to determine location
 */
export const getServerSideRegion = (request: Request): Region => {
  // Try to get country from various headers
  const headers = request.headers;
  const countryCode = 
    headers.get('cf-ipcountry') || // Cloudflare
    headers.get('x-vercel-ip-country') || // Vercel
    headers.get('x-country-code') || // Custom header
    null;

  return determineUserRegion(countryCode || undefined);
};
