import prisma from "@/utils/prisma";
import { FeatureType, PlanLimits } from "@/types/subscription";
import { PlanType } from "@prisma/client";
import { getPlanLimits } from "@/lib/subscription-config";

export interface UsageCheckResult {
  allowed: boolean;
  current: number;
  limit: number;
  remaining: number;
  percentage: number;
  unlimited: boolean;
  message?: string;
}

/**
 * Simple utility to check if a user can perform an action
 */
export async function checkUsageLimit(
  userId: string,
  feature: FeatureType,
  planType: PlanType = "Free"
): Promise<UsageCheckResult> {
  try {
    // Get plan limits
    const planLimits = getPlanLimits(planType);

    // Map FeatureType enum to PlanLimits property names
    let featureLimits;
    let limit = 0;

    switch (feature) {
      case FeatureType.TRANSFORMATIONS:
        featureLimits = planLimits.transformations;
        if (featureLimits?.unlimited) {
          const currentUsage = await getCurrentUsageCount(userId, feature);
          return {
            allowed: true,
            current: currentUsage,
            limit: -1,
            remaining: -1,
            percentage: 0,
            unlimited: true,
          };
        }
        limit = featureLimits?.weekly || 0;
        break;

      case FeatureType.QUIZZES:
        featureLimits = planLimits.quizzes;
        if (featureLimits?.unlimited) {
          const currentUsage = await getCurrentUsageCount(userId, feature);
          return {
            allowed: true,
            current: currentUsage,
            limit: -1,
            remaining: -1,
            percentage: 0,
            unlimited: true,
          };
        }
        // Calculate total quiz limit: perContent * transformation limit
        const quizPerContent = featureLimits?.perContent || 0;
        const transformationLimit = planLimits.transformations?.weekly || 0;
        limit = quizPerContent * transformationLimit;
        break;

      case FeatureType.FLASHCARDS:
        featureLimits = planLimits.flashcards;
        if (featureLimits?.unlimited) {
          const currentUsage = await getCurrentUsageCount(userId, feature);
          return {
            allowed: true,
            current: currentUsage,
            limit: -1,
            remaining: -1,
            percentage: 0,
            unlimited: true,
          };
        }
        // Calculate total flashcard limit: perContent * transformation limit
        const flashcardPerContent = featureLimits?.perContent || 0;
        const transformationLimitFlashcard =
          planLimits.transformations?.weekly || 0;
        limit = flashcardPerContent * transformationLimitFlashcard;
        break;

      case FeatureType.AI_CHAT:
        featureLimits = planLimits.aiChat;
        if (featureLimits?.unlimited) {
          const currentUsage = await getCurrentUsageCount(userId, feature);
          return {
            allowed: true,
            current: currentUsage,
            limit: -1,
            remaining: -1,
            percentage: 0,
            unlimited: true,
          };
        }
        limit = featureLimits?.daily || 0;
        break;

      default:
        limit = 0;
    }

    // Get current usage count
    const currentUsage = await getCurrentUsageCount(userId, feature);

    const remaining = Math.max(0, limit - currentUsage);
    const percentage =
      limit > 0 ? Math.min(100, (currentUsage / limit) * 100) : 0;
    const allowed = currentUsage < limit;

    return {
      allowed,
      current: currentUsage,
      limit,
      remaining,
      percentage,
      unlimited: false,
      message: allowed
        ? undefined
        : `You've reached your ${feature.toLowerCase()} limit of ${limit} for your ${planType} plan.`,
    };
  } catch (error) {
    console.error("Error checking usage limit:", error);
    // MODIFIED: Fail closed - deny the action if the check itself fails.
    return {
      allowed: false,
      current: 0, // Or a more appropriate value indicating an error state
      limit: 0, // Or a more appropriate value
      remaining: 0,
      percentage: 100, // Indicates limit reached or error
      unlimited: false,
      message:
        "An error occurred while checking your usage limits. Please try again.",
    };
  }
}

/**
 * Record usage for a feature with audit logging
 */
export async function recordUsage(
  userId: string,
  feature: FeatureType,
  metadata?: Record<string, any>
): Promise<void> {
  try {
    const resetDate = getResetDate(feature);
    const timestamp = new Date();

    // Get current usage before incrementing
    const currentUsage = await getCurrentUsageCount(userId, feature);

    console.log(
      `[USAGE_TRACKING] Recording usage for user ${userId}, feature ${feature}, current count: ${currentUsage}`
    );

    // Use transaction to ensure consistency
    const result = await prisma.$transaction(async (tx) => {
      // Upsert usage record
      const usageRecord = await tx.usageRecord.upsert({
        where: {
          userId_feature_resetDate: {
            userId,
            feature,
            resetDate,
          },
        },
        update: {
          count: { increment: 1 },
          lastUsedAt: timestamp,
          metadata: metadata || {},
        },
        create: {
          userId,
          feature,
          count: 1,
          resetDate,
          lastUsedAt: timestamp,
          metadata: metadata || {},
        },
      });

      // Create audit log
      await tx.usageAuditLog.create({
        data: {
          usageRecordId: usageRecord.id,
          userId,
          feature,
          action: "increment",
          previousCount: currentUsage,
          newCount: currentUsage + 1,
          metadata: {
            ...metadata,
            timestamp: timestamp.toISOString(),
            resetDate: resetDate.toISOString(),
          },
          timestamp,
        },
      });

      return usageRecord;
    });

    console.log(
      `[USAGE_TRACKING] Successfully recorded usage for user ${userId}, feature ${feature}, new count: ${result.count}`
    );
  } catch (error) {
    console.error(
      `[USAGE_TRACKING] Error recording usage for user ${userId}, feature ${feature}:`,
      error
    );
    // Don't throw - usage recording failure shouldn't break the main flow
    // But we should still log this for monitoring
  }
}

/**
 * Get current usage count for a user and feature
 */
export async function getCurrentUsageCount(
  userId: string,
  feature: FeatureType
): Promise<number> {
  try {
    const resetDate = getResetDate(feature);

    console.log(
      `[USAGE_TRACKING] Getting usage count for user ${userId}, feature ${feature}, resetDate: ${resetDate.toISOString()}`
    );

    const usage = await prisma.usageRecord.findUnique({
      where: {
        userId_feature_resetDate: {
          userId,
          feature,
          resetDate,
        },
      },
    });

    const count = usage?.count || 0;
    console.log(
      `[USAGE_TRACKING] Current usage count for user ${userId}, feature ${feature}: ${count}`
    );

    return count;
  } catch (error) {
    console.error(
      `[USAGE_TRACKING] Error getting usage count for user ${userId}, feature ${feature}:`,
      error
    );
    return 0;
  }
}

/**
 * Calculate reset date based on feature type, using UTC for all calculations.
 * EXPORTED to ensure consistent reset date logic across the application
 */
export function getResetDate(feature: FeatureType): Date {
  const now = new Date();

  switch (feature) {
    case FeatureType.TRANSFORMATIONS:
      // Weekly reset (Monday UTC)
      // getUTCDay() returns 0 for Sunday, 1 for Monday, ..., 6 for Saturday
      const currentUTCDay = now.getUTCDay();
      // Calculate days to subtract to get to the previous/current Monday
      // If current day is Sunday (0), subtract 6 days to get to previous Monday.
      // Otherwise, subtract (currentUTCDay - 1) days.
      const daysToSubtractForMonday =
        currentUTCDay === 0 ? 6 : currentUTCDay - 1;
      const mondayUTC = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate() - daysToSubtractForMonday
        )
      );
      // The date is already at 00:00:00.000Z due to Date.UTC()
      return mondayUTC;

    case FeatureType.AI_CHAT:
      // Daily reset (00:00 UTC)
      const todayUTC = new Date(
        Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate())
      );
      return todayUTC;

    case FeatureType.QUIZZES:
    case FeatureType.FLASHCARDS:
    default:
      // Monthly reset (1st of the month, 00:00 UTC)
      const monthStartUTC = new Date(
        Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1)
      );
      return monthStartUTC;
  }
}

/**
 * Get user's usage data for dashboard with validation
 */
export async function getUserUsageData(
  userId: string,
  planType: PlanType = "Free"
) {
  const features = [
    FeatureType.TRANSFORMATIONS,
    FeatureType.QUIZZES,
    FeatureType.FLASHCARDS,
    FeatureType.AI_CHAT,
  ];

  const usageData = await Promise.all(
    features.map(async (feature) => {
      const result = await checkUsageLimit(userId, feature, planType);
      return {
        feature,
        ...result,
      };
    })
  );

  return usageData;
}
