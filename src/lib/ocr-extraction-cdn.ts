/**
 * OCR extraction utility using CDN-based approach
 * This avoids the need to install packages locally
 */
import pdfToText from "react-pdftotext";

// Define types for Tesseract.js
interface TesseractLoggerMessage {
  status: string;
  progress?: number;
  workerId?: string;
  jobId?: string;
  [key: string]: any;
}

interface TesseractWorker {
  load: () => Promise<void>;
  loadLanguage: (lang: string) => Promise<void>;
  initialize: (lang: string) => Promise<void>;
  recognize: (image: string) => Promise<{ data: { text: string } }>;
  terminate: () => Promise<void>;
}

interface TesseractWorkerOptions {
  logger?: (message: TesseractLoggerMessage) => void;
  [key: string]: any;
}

interface TesseractStatic {
  createWorker: (options?: TesseractWorkerOptions) => Promise<TesseractWorker>;
}

// Function to dynamically load Tesseract.js from CDN
const loadTesseract = async (): Promise<TesseractStatic> => {
  // Check if Tesseract is already loaded
  if ((window as any).Tesseract) {
    return (window as any).Tesseract;
  }

  // Load Tesseract.js from CDN
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src =
      "https://cdn.jsdelivr.net/npm/tesseract.js@4/dist/tesseract.min.js";
    script.onload = () => resolve((window as any).Tesseract);
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

// Function to dynamically load PDF.js from CDN
const loadPdfJs = async () => {
  // Check if PDF.js is already loaded
  if ((window as any).pdfjsLib) {
    return (window as any).pdfjsLib;
  }

  // Load PDF.js from CDN
  return new Promise((resolve, reject) => {
    const script = document.createElement("script");
    script.src =
      "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.min.js";
    script.onload = () => {
      // Load worker
      const workerScript = document.createElement("script");
      workerScript.src =
        "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js";
      workerScript.onload = () => {
        (window as any).pdfjsLib.GlobalWorkerOptions.workerSrc =
          "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js";
        resolve((window as any).pdfjsLib);
      };
      workerScript.onerror = reject;
      document.head.appendChild(workerScript);
    };
    script.onerror = reject;
    document.head.appendChild(script);
  });
};

/**
 * Convert PDF to images using PDF.js
 */
export const pdfToImages = async (file: File): Promise<string[]> => {
  try {
    const pdfjsLib = await loadPdfJs();

    // Read the file as an ArrayBuffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document
    const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });
    const pdf = await loadingTask.promise;

    // Get the total number of pages
    const numPages = pdf.numPages;
    const images: string[] = [];

    // Process each page
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      // Get the page
      const page = await pdf.getPage(pageNum);

      // Set the scale for better OCR results
      const viewport = page.getViewport({ scale: 1.5 });

      // Create a canvas element to render the page
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      if (!context) {
        throw new Error("Could not create canvas context");
      }

      // Set canvas dimensions to match the page
      canvas.width = viewport.width;
      canvas.height = viewport.height;

      // Render the page to the canvas
      await page.render({
        canvasContext: context,
        viewport,
      }).promise;

      // Convert the canvas to an image data URL
      const imageDataUrl = canvas.toDataURL("image/png");
      images.push(imageDataUrl);
    }

    return images;
  } catch (error) {
    console.error("Error converting PDF to images:", error);
    throw error;
  }
};

/**
 * Extract text from images using Tesseract.js
 */
export const extractTextFromImages = async (
  images: string[],
  progressCallback?: (progress: number) => void
): Promise<string> => {
  try {
    const Tesseract = await loadTesseract();
    const totalImages = images.length;
    const textPages: string[] = [];

    for (let i = 0; i < images.length; i++) {
      // Update progress
      if (progressCallback) {
        progressCallback(i / totalImages);
      }

      // Create a worker
      const worker = await Tesseract.createWorker({
        logger: (m: TesseractLoggerMessage) => {
          if (progressCallback && m.status === "recognizing text") {
            const pageProgress = m.progress || 0;
            const overallProgress =
              i / totalImages + pageProgress / totalImages;
            progressCallback(overallProgress);
          }
        },
      });

      // Initialize worker
      await worker.load();
      await worker.loadLanguage("eng");
      await worker.initialize("eng");

      // Recognize text
      const { data } = await worker.recognize(images[i]);
      textPages.push(data.text);

      // Terminate worker
      await worker.terminate();
    }

    return textPages.join("\n\n");
  } catch (error) {
    console.error("Error extracting text from images:", error);
    throw error;
  }
};

/**
 * Smart PDF text extraction that tries regular extraction first,
 * then falls back to OCR if needed
 */
export const smartPdfTextExtraction = async (
  file: File,
  progressCallback?: (progress: number) => void
): Promise<{ text: string; usedOcr: boolean }> => {
  // Create a no-op progress callback if none is provided
  const updateProgress = progressCallback || (() => {});
  try {
    // Try regular extraction first with react-pdftotext (faster for normal PDFs)
    updateProgress(0.1);
    console.log("Attempting extraction with react-pdftotext...");

    try {
      const text = await pdfToText(file);
      updateProgress(0.4);

      // If text extraction succeeded and returned reasonable amount of text, use it
      if (text && text.trim().length > 100) {
        console.log("Successfully extracted text with react-pdftotext");
        updateProgress(1.0);
        return { text, usedOcr: false };
      }

      // If text extraction succeeded but returned very little text,
      // it might be a scanned PDF, so use OCR
      console.log("Text extraction returned minimal text, trying OCR...");
      updateProgress(0.5); // Update progress to show we're starting OCR

      // Convert PDF to images
      const images = await pdfToImages(file);
      updateProgress(0.6); // Update progress after converting to images

      // Extract text from images
      const ocrText = await extractTextFromImages(images, (progress) => {
        // Scale progress from 0.6 to 1.0
        updateProgress(0.6 + progress * 0.4);
      });

      console.log("OCR extraction complete");
      return { text: ocrText, usedOcr: true };
    } catch (error) {
      // If regular extraction fails, fall back to OCR
      console.warn(
        "Regular PDF extraction failed, falling back to OCR:",
        error
      );
      updateProgress(0.5); // Update progress to show we're starting OCR

      // Convert PDF to images
      const images = await pdfToImages(file);
      updateProgress(0.6); // Update progress after converting to images

      // Extract text from images
      const ocrText = await extractTextFromImages(images, (progress) => {
        // Scale progress from 0.6 to 1.0
        updateProgress(0.6 + progress * 0.4);
      });

      console.log("OCR extraction complete (after fallback)");
      return { text: ocrText, usedOcr: true };
    }
  } catch (error) {
    console.error("Error in smart PDF text extraction:", error);
    throw error;
  }
};
