// lib/paystack.ts
import https from 'https';
import { URL } from 'url';

const PAYSTACK_SECRET = process.env.PAYSTACK_SECRET_KEY!;

if (!PAYSTACK_SECRET) {
  throw new Error('PAYSTACK_SECRET_KEY is required');
}

function paystackRequest<T>(
  path: string,
  method: 'GET' | 'POST',
  body?: Record<string, unknown>
): Promise<T> {
  const url = new URL(`https://api.paystack.co${path}`);
  const options: https.RequestOptions = {
    hostname: url.hostname,
    port: 443,
    path: url.pathname + url.search,
    method,
    headers: {
      Authorization: `Bearer ${PAYSTACK_SECRET}`,
      'Content-Type': 'application/json',
    },
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => (data += chunk));
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          // Check if the response indicates an error
          if (res.statusCode && res.statusCode >= 400) {
            reject(new Error(`Paystack API error (${res.statusCode}): ${parsed.message || data}`));
            return;
          }
          resolve(parsed);
        } catch (err) {
          reject(new Error(`Failed to parse Paystack response: ${err instanceof Error ? err.message : String(err)}`));
        }
      });
    });

    req.on('error', reject);
    if (body) req.write(JSON.stringify(body));
    req.end();
  });
}

// Create subscription directly using Paystack's subscription endpoint
// This returns subscription_code and email_token immediately
export async function createSubscriptionDirect(customer: string, plan: string, authorization?: string): Promise<{ status: boolean; data?: any }> {
  const body: Record<string, unknown> = {
    customer,
    plan
  };
  
  if (authorization) {
    body.authorization = authorization;
  }
  
  const response = await paystackRequest<{ status: boolean; data?: any }>('/subscription', 'POST', body);
  console.log('🔍 Direct subscription creation response:', JSON.stringify(response, null, 2));
  return response;
}

// Create customer first, then create subscription
export async function createCustomerAndSubscription(email: string, plan: string, firstName?: string, lastName?: string, phone?: string): Promise<{ status: boolean; data?: any }> {
  try {
    // First, create or get customer
    const customerResponse = await paystackRequest<{ status: boolean; data?: { customer_code: string; [key: string]: any } }>('/customer', 'POST', {
      email,
      first_name: firstName,
      last_name: lastName,
      phone
    });
    
    if (!customerResponse.status || !customerResponse.data?.customer_code) {
      throw new Error('Failed to create customer');
    }
    
    console.log('✅ Customer created:', customerResponse.data.customer_code);
    
    // Then create subscription using customer code
    return await createSubscriptionDirect(customerResponse.data.customer_code, plan);
  } catch (error) {
    console.error('Error in createCustomerAndSubscription:', error);
    throw error;
  }
}

// Legacy function - now uses transaction initialization (fallback method)
export async function createSubscription(email: string, plan: string): Promise<{ status: boolean; data?: any }> {
  // Use initialize transaction with plan code to create subscription
  // This creates a checkout URL for the customer to pay and subscribe
  // Note: When using a plan code, the amount parameter is required but will be overridden by the plan amount
  const response = await paystackRequest<{ status: boolean; data?: any }>('/transaction/initialize', 'POST', {
    email,
    amount: "100", // Dummy amount in kobo (₦1) - will be overridden by plan amount
    plan,
    callback_url: `${process.env.NEXT_PUBLIC_APP_URL  || 'http://localhost:3000' || "qlipify.com"}/api/subscriptions/callback`
  });
  
  console.log('🔍 Raw Paystack response:', JSON.stringify(response, null, 2));
  return response;
}

export async function activateSubscription(code: string, token: string): Promise<{ status: boolean; data?: any }> {
  return paystackRequest<{ status: boolean; data?: any }>('/subscription/enable', 'POST', { code, token });
}

export async function cancelSubscription(code: string, token: string): Promise<{ status: boolean; data?: any }> {
  return paystackRequest<{ status: boolean; data?: any }>('/subscription/disable', 'POST', { code, token });
}

export async function fetchSubscription(idOrCode: string): Promise<{ status: boolean; data?: { email_token?: string; [key: string]: any } }> {
  return paystackRequest<{ status: boolean; data?: { email_token?: string; [key: string]: any } }>(`/subscription/${idOrCode}`, 'GET');
}

export async function verifyTransaction(reference: string): Promise<{ status: boolean; data?: any }> {
  return paystackRequest<{ status: boolean; data?: any }>(`/transaction/verify/${reference}`, 'GET');
}

// Legacy export for backward compatibility during migration
export const paymentsClient = {
  createSubscription,
  activateSubscription,
  cancelSubscription,
  fetchSubscription,
  verifyTransaction,
};

export default paymentsClient;