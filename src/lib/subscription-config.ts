import { PlanType } from "@/data/pricingData";
import { PlanLimits } from "@/types/subscription";
import { getFlutterwavePlanCode, Region } from "./flutterwave-config";

export const getPlanLimits = (planType: PlanType): PlanLimits => {
  switch (planType) {
    case "Free":
      return {
        transformations: { weekly: 2 },
        quizzes: { perContent: 1 },
        flashcards: { perContent: 1 },
        aiChat: { daily: 5 },
        sharing: false,
        translation: false,
      };
    case "Starter":
      return {
        transformations: { weekly: 10 },
        quizzes: { perContent: 2 },
        flashcards: { perContent: 2 },
        aiChat: { daily: 20 },
        sharing: true,
        translation: true,
      };
    case "Pro":
      return {
        transformations: { weekly: 20 },
        quizzes: { perContent: 4 },
        flashcards: { perContent: 4 },
        aiChat: { daily: 40 },
        sharing: true,
        translation: true,
        analytics: true,
      };
    case "Unlimited":
      return {
        transformations: { unlimited: true },
        quizzes: { unlimited: true },
        flashcards: { unlimited: true },
        aiChat: { unlimited: true },
        sharing: true,
        translation: true,
        analytics: true,
        priority: true,
      };
    default:
      return getPlanLimits("Free");
  }
};

// Get Flutterwave plan code for subscription
export const getFlutterwaveProductId = (
  planType: PlanType,
  billingCycle: "monthly" | "annual",
  region: Region
): string => {
  return getFlutterwavePlanCode(planType, billingCycle, region);
};

export async function getUserSubscription(userId: string) {
  const { db, subscription } = await import("@/lib/db");
  const { eq, inArray, desc, and } = await import("drizzle-orm");

  const result = await db
    .select()
    .from(subscription)
    .where(
      and(
        eq(subscription.userId, userId),
        inArray(subscription.status, ["ACTIVE", "NON_RENEWING"])
      )
    )
    .orderBy(desc(subscription.createdAt))
    .limit(1);

  return result[0] || null;
}
