import { PlanType } from "@/data/pricingData";
import { PlanLimits } from "@/types/subscription";
import { prisma } from "./prisma";

// You'll need to replace these with your actual product IDs from Paystack dashboard
export const PRODUCT_IDS = {
  starter_monthly: process.env.STARTER_MONTHLY_PRODUCT_ID || "",
  starter_annual: process.env.STARTER_ANNUAL_PRODUCT_ID || "",
  pro_monthly: process.env.PRO_MONTHLY_PRODUCT_ID || "",
  pro_annual: process.env.PRO_ANNUAL_PRODUCT_ID || "",
  unlimited_monthly: process.env.UNLIMITED_MONTHLY_PRODUCT_ID || "",
  unlimited_annual: process.env.UNLIMITED_ANNUAL_PRODUCT_ID || "",
} as const;

export const getPlanLimits = (planType: PlanType): PlanLimits => {
  switch (planType) {
    case "Free":
      return {
        transformations: { weekly: 2 },
        quizzes: { perContent: 1 },
        flashcards: { perContent: 1 },
        aiChat: { daily: 5 },
        sharing: false,
        translation: false,
      };
    case "Starter":
      return {
        transformations: { weekly: 10 },
        quizzes: { perContent: 2 },
        flashcards: { perContent: 2 },
        aiChat: { daily: 20 },
        sharing: true,
        translation: true,
      };
    case "Pro":
      return {
        transformations: { weekly: 20 },
        quizzes: { perContent: 4 },
        flashcards: { perContent: 4 },
        aiChat: { daily: 40 },
        sharing: true,
        translation: true,
        analytics: true,
      };
    case "Unlimited":
      return {
        transformations: { unlimited: true },
        quizzes: { unlimited: true },
        flashcards: { unlimited: true },
        aiChat: { unlimited: true },
        sharing: true,
        translation: true,
        analytics: true,
        priority: true,
      };
    default:
      return getPlanLimits("Free");
  }
};

export const getProductId = (
  planType: PlanType,
  billingCycle: "monthly" | "annual"
): string => {
  const key =
    `${planType.toLowerCase()}_${billingCycle}` as keyof typeof PRODUCT_IDS;
  return PRODUCT_IDS[key];
};

export async function getUserSubscription(userId: string) {
  const { db, subscription } = await import("@/lib/db");
  const { eq, inArray, desc, and } = await import("drizzle-orm");

  const result = await db
    .select()
    .from(subscription)
    .where(
      and(
        eq(subscription.userId, userId),
        inArray(subscription.status, ["ACTIVE", "NON_RENEWING"])
      )
    )
    .orderBy(desc(subscription.createdAt))
    .limit(1);

  return result[0] || null;
}
