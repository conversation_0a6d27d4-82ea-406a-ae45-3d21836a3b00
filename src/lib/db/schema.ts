import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  jsonb,
  pgEnum,
  index,
  unique,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";

// Enums
export const contentTypeEnum = pgEnum("ContentType", [
  "YOUTUBE",
  "PDF",
  "TEXT",
  "WEBPAGE",
]);
export const planTypeEnum = pgEnum("PlanType", [
  "Free",
  "Starter",
  "Pro",
  "Unlimited",
]);
export const subscriptionStatusEnum = pgEnum("SubscriptionStatus", [
  "PENDING",
  "ACTIVE",
  "NON_RENEWING",
  "ATTENTION",
  "COMPLETED",
  "CANCELLED",
]);

// Tables
export const users = pgTable(
  "users",
  {
    id: text("id").primaryKey(),
    email: text("email").unique(),
    displayName: text("displayName"),
    avatarUrl: text("avatarUrl"),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt").defaultNow().notNull(),
  },
  (table) => ({
    emailIdx: index("users_email_idx").on(table.email),
  })
);

export const contentSummary = pgTable(
  "ContentSummary",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    contentType: contentTypeEnum("contentType").notNull(),
    contentId: text("contentId").unique().notNull(),
    title: text("title").notNull(),
    content: text("content").notNull(),
    originalContent: text("originalContent").notNull(),
    sourceId: text("sourceId").notNull(),
    generatedAt: timestamp("generatedAt").defaultNow().notNull(),
    userId: text("userId").notNull(),
    translatedContent: text("translatedContent"),
    translationLanguage: text("translationLanguage"),
    isShared: boolean("isShared").default(false).notNull(),
    shareToken: text("shareToken").unique(),
    outputType: text("outputType").notNull(),
  },
  (table) => ({
    userIdIdx: index("ContentSummary_userId_idx").on(table.userId),
    contentTypeIdx: index("ContentSummary_contentType_idx").on(
      table.contentType
    ),
    contentIdIdx: index("ContentSummary_contentId_idx").on(table.contentId),
    userSourceContentUnique: unique(
      "ContentSummary_userId_sourceId_contentType_key"
    ).on(table.userId, table.sourceId, table.contentType),
  })
);

export const quiz = pgTable(
  "Quiz",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    userId: text("userId").notNull(),
    questions: jsonb("questions").notNull(),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    contentSummaryId: text("contentSummaryId").notNull(),
  },
  (table) => ({
    contentSummaryIdIdx: index("Quiz_contentSummaryId_idx").on(
      table.contentSummaryId
    ),
    userIdIdx: index("Quiz_userId_idx").on(table.userId),
  })
);

export const quizAttempt = pgTable(
  "QuizAttempt",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    quizId: text("quizId").notNull(),
    userId: text("userId").notNull(),
    answers: jsonb("answers").notNull(),
    score: integer("score").notNull(),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
  },
  (table) => ({
    quizIdIdx: index("QuizAttempt_quizId_idx").on(table.quizId),
    userIdIdx: index("QuizAttempt_userId_idx").on(table.userId),
  })
);

export const flashcard = pgTable(
  "Flashcard",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    userId: text("userId").notNull(),
    cards: jsonb("cards").notNull(),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    studyProgress: jsonb("studyProgress"),
    contentSummaryId: text("contentSummaryId").notNull(),
  },
  (table) => ({
    contentSummaryIdIdx: index("Flashcard_contentSummaryId_idx").on(
      table.contentSummaryId
    ),
    userIdIdx: index("Flashcard_userId_idx").on(table.userId),
  })
);

export const chatMessage = pgTable(
  "ChatMessage",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    userId: text("userId").notNull(),
    role: text("role").notNull(),
    content: text("content").notNull(),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    contentSummaryId: text("contentSummaryId").notNull(),
  },
  (table) => ({
    contentSummaryIdIdx: index("ChatMessage_contentSummaryId_idx").on(
      table.contentSummaryId
    ),
    userIdIdx: index("ChatMessage_userId_idx").on(table.userId),
  })
);

export const subscription = pgTable(
  "Subscription",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    userId: text("userId").notNull(),
    subscriptionId: text("subscriptionId").unique().notNull(),
    customerId: text("customerId"),
    planType: planTypeEnum("planType").notNull(),
    status: subscriptionStatusEnum("status").notNull(),
    currentPeriodStart: timestamp("currentPeriodStart").notNull(),
    currentPeriodEnd: timestamp("currentPeriodEnd").notNull(),
    billingCycle: text("billingCycle").notNull(),
    cancelAtPeriodEnd: boolean("cancelAtPeriodEnd").default(false).notNull(),
    canceledAt: timestamp("canceledAt"),
    priceId: text("priceId").notNull(),
    quantity: integer("quantity").default(1).notNull(),
    metadata: jsonb("metadata"),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt").defaultNow().notNull(),
    pendingPlanType: planTypeEnum("pendingPlanType"),
    pendingPlanEffectiveDate: timestamp("pendingPlanEffectiveDate"),
    pendingPriceId: text("pendingPriceId"),
    emailToken: text("emailToken"),
  },
  (table) => ({
    userIdIdx: index("Subscription_userId_idx").on(table.userId),
    subscriptionIdIdx: index("Subscription_subscriptionId_idx").on(
      table.subscriptionId
    ),
    statusIdx: index("Subscription_status_idx").on(table.status),
    planTypeIdx: index("Subscription_planType_idx").on(table.planType),
  })
);

export const usageRecord = pgTable(
  "UsageRecord",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    userId: text("userId").notNull(),
    subscriptionId: text("subscriptionId"),
    feature: text("feature").notNull(),
    count: integer("count").default(0).notNull(),
    resetDate: timestamp("resetDate").notNull(),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt").defaultNow().notNull(),
    lastUsedAt: timestamp("lastUsedAt").defaultNow().notNull(),
    metadata: jsonb("metadata"),
  },
  (table) => ({
    userIdIdx: index("UsageRecord_userId_idx").on(table.userId),
    featureIdx: index("UsageRecord_feature_idx").on(table.feature),
    resetDateIdx: index("UsageRecord_resetDate_idx").on(table.resetDate),
    userFeatureResetUnique: unique("userId_feature_resetDate").on(
      table.userId,
      table.feature,
      table.resetDate
    ),
  })
);

export const usageAuditLog = pgTable(
  "UsageAuditLog",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    usageRecordId: text("usageRecordId").notNull(),
    userId: text("userId").notNull(),
    feature: text("feature").notNull(),
    action: text("action").notNull(),
    previousCount: integer("previousCount").notNull(),
    newCount: integer("newCount").notNull(),
    metadata: jsonb("metadata"),
    timestamp: timestamp("timestamp").defaultNow().notNull(),
  },
  (table) => ({
    userIdIdx: index("UsageAuditLog_userId_idx").on(table.userId),
    featureIdx: index("UsageAuditLog_feature_idx").on(table.feature),
    timestampIdx: index("UsageAuditLog_timestamp_idx").on(table.timestamp),
    actionIdx: index("UsageAuditLog_action_idx").on(table.action),
  })
);

export const product = pgTable(
  "Product",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    productId: text("productId").unique().notNull(),
    planType: planTypeEnum("planType").notNull(),
    name: text("name").notNull(),
    description: text("description"),
    price: integer("price").notNull(), // Store as cents to avoid float issues
    currency: text("currency").default("USD").notNull(),
    interval: text("interval").notNull(),
    isActive: boolean("isActive").default(true).notNull(),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt").defaultNow().notNull(),
  },
  (table) => ({
    planTypeIdx: index("Product_planType_idx").on(table.planType),
    isActiveIdx: index("Product_isActive_idx").on(table.isActive),
    intervalIdx: index("Product_interval_idx").on(table.interval),
  })
);
