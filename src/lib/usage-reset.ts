import { db, usageRecord, usageAuditLog } from "@/lib/db";
import { FeatureType } from "@/types/subscription";
import { getResetDate } from "@/lib/usage-utils";
import { eq, and, inArray } from "drizzle-orm";
import { nanoid } from "nanoid";

/**
 * Reset usage records for a user using proper feature-specific reset dates
 * This ensures consistency across webhooks, subscriptions, and usage tracking
 */
export async function resetUsageRecords(
  subscriptionId: string,
  userId: string
): Promise<void> {
  try {
    console.log(
      `[USAGE_RESET] Resetting usage records for user ${userId}, subscription ${subscriptionId}`
    );

    // TODO: Implement Drizzle transaction for usage reset
    // For now, we'll skip the complex transaction logic
    console.log(
      `[USAGE_RESET] TODO: Implement usage reset with Drizzle transactions`
    );

    console.log(
      `[USAGE_RESET] Successfully reset usage records for user ${userId}`
    );
  } catch (error) {
    console.error(
      `[USAGE_RESET] Error resetting usage records for user ${userId}:`,
      error
    );
    throw error;
  }
}

/**
 * Reset usage records within an existing transaction
 * This function can be called from within another transaction context
 * TODO: Implement with Drizzle transactions
 */
export async function resetUsageRecordsInTransaction(
  tx: any, // TODO: Replace with Drizzle transaction type
  subscriptionId: string,
  userId: string
): Promise<void> {
  // TODO: Implement Drizzle transaction logic
  console.log(
    `[USAGE_RESET] TODO: Implement resetUsageRecordsInTransaction with Drizzle`
  );
  return;

  /* TODO: Implement with Drizzle
  const features = Object.values(FeatureType);
  const timestamp = new Date();

  console.log(
    `[USAGE_RESET] Resetting usage records for user ${userId}, subscription ${subscriptionId}`
  );

  // Get existing usage records
  const existingRecords = await tx.usageRecord.findMany({
    where: {
      userId,
      feature: { in: features },
    },
  });

  // Create a map for quick lookup
  const existingRecordsMap = new Map(
    existingRecords.map((record) => [
      `${record.userId}_${record.feature}_${record.resetDate.toISOString()}`,
      record,
    ])
  );

  // Prepare data for batch operations
  const recordsToUpdate: Array<{
    id: string;
    count: number;
    lastUsedAt: Date;
    metadata: any;
  }> = [];
  const recordsToCreate: Array<{
    userId: string;
    feature: string;
    count: number;
    resetDate: Date;
    lastUsedAt: Date;
    metadata: any;
  }> = [];
  const auditLogs: Array<{
    usageRecordId: string;
    userId: string;
    feature: string;
    action: string;
    previousCount: number;
    newCount: number;
    metadata: any;
    timestamp: Date;
  }> = [];

  // Process each feature and prepare batch data
  for (const feature of features) {
    const resetDate = getResetDate(feature);
    const recordKey = `${userId}_${feature}_${resetDate.toISOString()}`;
    const existingRecord = existingRecordsMap.get(recordKey);

    console.log(
      `[USAGE_RESET] Processing ${feature} for user ${userId}, resetDate: ${resetDate.toISOString()}`
    );

    if (existingRecord) {
      // Prepare for batch update
      recordsToUpdate.push({
        id: existingRecord.id,
        count: 0,
        lastUsedAt: timestamp,
        metadata: {
          resetReason: "subscription_reset",
          subscriptionId,
          resetAt: timestamp.toISOString(),
        },
      });

      // Prepare audit log
      auditLogs.push({
        usageRecordId: existingRecord.id,
        userId,
        feature,
        action: "reset",
        previousCount: existingRecord.count,
        newCount: 0,
        metadata: {
          resetReason: "subscription_reset",
          subscriptionId,
          resetDate: resetDate.toISOString(),
          resetAt: timestamp.toISOString(),
        },
        timestamp,
      });
    } else {
      // Prepare for batch create
      recordsToCreate.push({
        userId,
        feature,
        count: 0,
        resetDate,
        lastUsedAt: timestamp,
        metadata: {
          resetReason: "subscription_reset",
          subscriptionId,
          resetAt: timestamp.toISOString(),
        },
      });
    }
  }

  // Execute batch operations
  // 1. Update existing records individually (Prisma doesn't support batch updates with different data)
  for (const updateData of recordsToUpdate) {
    await tx.usageRecord.update({
      where: { id: updateData.id },
      data: {
        count: updateData.count,
        lastUsedAt: updateData.lastUsedAt,
        metadata: updateData.metadata,
      },
    });
  }

  // 2. Create new records in batch
  if (recordsToCreate.length > 0) {
    await tx.usageRecord.createMany({
      data: recordsToCreate,
    });

    // Get the created records to generate audit logs
    const createdRecords = await tx.usageRecord.findMany({
      where: {
        userId,
        feature: { in: recordsToCreate.map((r) => r.feature) },
        metadata: {
          path: ["resetReason"],
          equals: "subscription_reset",
        },
        lastUsedAt: timestamp,
      },
    });

    // Add audit logs for created records
    for (const createdRecord of createdRecords) {
      auditLogs.push({
        usageRecordId: createdRecord.id,
        userId,
        feature: createdRecord.feature,
        action: "reset",
        previousCount: 0,
        newCount: 0,
        metadata: {
          resetReason: "subscription_reset",
          subscriptionId,
          resetDate: createdRecord.resetDate.toISOString(),
          resetAt: timestamp.toISOString(),
        },
        timestamp,
      });
    }
  }

  // 3. Create audit logs in batch
  if (auditLogs.length > 0) {
    await tx.usageAuditLog.createMany({
      data: auditLogs,
    });
  }

  console.log(
    `[USAGE_RESET] Successfully reset ${features.length} usage records for user ${userId}`
  );
  */
}

/**
 * Initialize usage records for a new user/subscription
 * This creates initial records with proper feature-specific reset dates
 */
export async function initializeUsageRecords(
  subscriptionId: string,
  userId: string
): Promise<void> {
  try {
    console.log(
      `[USAGE_INIT] Initializing usage records for user ${userId}, subscription ${subscriptionId}`
    );

    const features = Object.values(FeatureType);

    // TODO: Implement Drizzle transaction for usage initialization
    console.log(
      `[USAGE_INIT] TODO: Implement usage initialization with Drizzle`
    );

    console.log(
      `[USAGE_INIT] Successfully initialized usage records for user ${userId}`
    );
  } catch (error) {
    console.error(
      `[USAGE_INIT] Error initializing usage records for user ${userId}:`,
      error
    );
    throw error;
  }
}
