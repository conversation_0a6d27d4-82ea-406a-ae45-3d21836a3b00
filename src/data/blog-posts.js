// This file is an ES module version of blog-posts.ts for use with sitemap.js

export const blogPosts = [
  {
    id: "1",
    title: "How AI is Revolutionizing the Way We Learn from Videos",
    description:
      "Discover how AI-powered tools like Qlipify are transforming video content into effective learning materials, saving students hours of study time.",
    date: "2025-04-15",
    author: "<PERSON>",
    authorRole: "Learning Technology Specialist",
    authorImage:
      "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WIBglLDctvxMJAXT48nCEmLjhzl7RYFpqaZbB",
    coverImage:
      "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WSomzWOxbA14emWGKX9vxapyENMgosPzUlkqC",
    tags: ["AI Learning", "Video Summarization", "EdTech"],
    slug: "ai-revolutionizing-video-learning",
    readTime: 6,
  },
  {
    id: "2",
    title: "The Science of Retention: Why Flashcards and Quizzes Work",
    description:
      "Explore the cognitive science behind active recall and spaced repetition, and how these techniques can improve your learning retention by up to 70%.",
    date: "2025-03-22",
    author: "Dr. <PERSON>",
    authorRole: "Cognitive Scientist",
    authorImage:
      "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WFAAAxkqhI9LXTfxC5Ad4lp1WocqmwHirgGPa",
    coverImage:
      "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WqLKPaiJCSgB7dznvlMb6wQR4PxkeaKjfrEV2",
    tags: ["Learning Science", "Memory Techniques", "Study Methods"],
    slug: "science-of-retention-flashcards-quizzes",
    readTime: 8,
  },
  {
    id: "3",
    title: "From Information Overload to Focused Learning: A Student's Guide",
    description:
      "Learn practical strategies to combat information overload and transform overwhelming content into manageable, focused learning materials.",
    date: "2025-02-08",
    author: "Michael Rodriguez",
    authorRole: "Education Consultant",
    authorImage:
      "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WtevZSJFF3oRuv98kie6WBJf4ZzTbKXQG1xSN",
    coverImage:
      "https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WwTZYY975T4mbzADqpj03J7no6xHQNl9dWhut",
    tags: ["Study Tips", "Information Management", "Productivity"],
    slug: "information-overload-focused-learning",
    readTime: 5,
  },
];

// Export functions similar to the TypeScript version
export function getBlogPostBySlug(slug) {
  return blogPosts.find((post) => post.slug === slug);
}

export function getAllBlogPosts() {
  return [...blogPosts].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );
}

export function getRelatedPosts(currentPostId, limit = 2) {
  return blogPosts.filter((post) => post.id !== currentPostId).slice(0, limit);
}
