"use client";

import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { Loader2, LucideIcon } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface LoadingCardProps {
  title: string;
  message: string;
  icon?: LucideIcon;
  isComplete?: boolean;
  completeIcon?: LucideIcon;
  className?: string;
}

/**
 * A reusable loading card component that can be used for various loading states
 * including content transformation, quiz generation, and flashcard generation.
 * Uses shadcn Card component with enhanced shadows for better visibility.
 */
export function LoadingCard({
  title,
  message,
  icon: Icon,
  isComplete = false,
  completeIcon: CompleteIcon,
  className,
}: LoadingCardProps) {
  // Animation state for the loading indicator
  const [animationStep, setAnimationStep] = useState(0);
  
  // Animate the loading indicator
  useEffect(() => {
    if (isComplete) return;
    
    const interval = setInterval(() => {
      setAnimationStep((prev) => (prev + 1) % 3);
    }, 600);
    
    return () => clearInterval(interval);
  }, [isComplete]);

  return (
    <div className={cn("flex flex-col items-center justify-center py-8 px-4", className)}>
      <Card className="w-full max-w-md shadow-xl border bg-card/50 backdrop-blur-sm">
        <CardHeader className="pb-0">
          <CardTitle className="text-2xl font-semibold text-center text-primary">
            {title}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="p-6 flex flex-col items-center">
          {/* Loading animation */}
          <div className="relative w-24 h-24 mb-6">
            <div className="absolute inset-0 flex items-center justify-center">
              {isComplete ? (
                <>{CompleteIcon && <CompleteIcon className="w-16 h-16 text-primary animate-pulse" />}</>
              ) : (
                <div className="relative">
                  <Loader2 className="w-16 h-16 text-primary animate-spin" />
                  {Icon && <Icon className="w-6 h-6 text-primary absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />}
                </div>
              )}
            </div>
          </div>
          
          {/* Progress text */}
          <h3 className="text-lg font-medium text-center mb-2">
            {message}
          </h3>
          
          {/* Loading dots animation */}
          <div className="flex space-x-2 h-6 items-center justify-center">
            {!isComplete && (
              <>
                <div className={cn(
                  "w-2 h-2 rounded-full bg-primary transition-opacity duration-300",
                  animationStep === 0 ? "opacity-100" : "opacity-30"
                )} />
                <div className={cn(
                  "w-2 h-2 rounded-full bg-primary transition-opacity duration-300",
                  animationStep === 1 ? "opacity-100" : "opacity-30"
                )} />
                <div className={cn(
                  "w-2 h-2 rounded-full bg-primary transition-opacity duration-300",
                  animationStep === 2 ? "opacity-100" : "opacity-30"
                )} />
              </>
            )}
            {isComplete && (
              <span className="text-sm text-primary">Complete!</span>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
