"use client";

import type React from "react";

import { useState, useEffect, useRef, ChangeEvent } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useContentStore } from "@/store/contentStore";
import { Send, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useChatStore } from "@/store/chatStore";

import MarkdownRender from "./MarkdownRender";
import { useUsageLimit } from "@/hooks/useUsageLimit";
import { FeatureType } from "@/types/subscription";

const Chat = () => {
  const [message, setMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const { sourceContent, metadata, contentType } = useContentStore();
  const { messages, setMessages, addMessage, clearMessages, setContentInfo } =
    useChatStore();
  const { isChecking, checkUsageLimit } = useUsageLimit();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Function to auto-resize the textarea based on content
  const autoResizeTextarea = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = "auto";
      // Set the height to the scrollHeight to fit all content
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  // Scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    } else if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  };

  useEffect(() => {
    // Use a small timeout to ensure DOM is updated before scrolling
    const scrollTimer = setTimeout(() => {
      scrollToBottom();
    }, 50);

    return () => clearTimeout(scrollTimer);
  }, [messages]);

  // Handle window resize events to ensure proper scrolling
  useEffect(() => {
    const handleResize = () => {
      scrollToBottom();
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Handle content changes and chat history loading
  useEffect(() => {
    const contentId = metadata?.contentId ?? null;
    const currentContentType = contentType || null;

    // Only clear messages if contentId changes
    if (contentId !== useChatStore.getState().contentId) {
      clearMessages();
      setContentInfo(contentId, currentContentType);

      if (contentId) {
        setIsFetching(true);
        fetch(`/api/chat?contentId=${contentId}`)
          .then(async (response) => {
            if (!response.ok) throw new Error("Failed to load chat history");
            const data = await response.json();
            setMessages(data.messages || []);
          })
          .catch((error) => console.error(error))
          .finally(() => setIsFetching(false));
      }
    }
  }, [
    metadata?.contentId,
    contentType,
    clearMessages,
    setMessages,
    setContentInfo,
  ]);

  // Handle message input change
  const handleMessageChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
    // Auto-resize the textarea when content changes
    autoResizeTextarea();
  };

  // Reset textarea height when message is cleared
  useEffect(() => {
    if (message === "" && textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  }, [message]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const trimmedMessage = message.trim();
    if (!trimmedMessage) return;

    if (!sourceContent || !metadata) {
      toast.error("Please analyze content first to chat about it");
      return;
    }

    const userMessage = { role: "user" as const, content: trimmedMessage };
    setMessage(""); // Clear input immediately
    addMessage(userMessage);
    setIsLoading(true);

    // Check usage limit after displaying user message
    const canProceed = await checkUsageLimit(FeatureType.AI_CHAT);
    if (!canProceed) {
      // Add limit message with upgrade link
      const limitMessage = {
        role: "assistant" as const,
        content:
          "You've reached your daily chat message limit for this plan. [Upgrade your plan](/upgrade) to continue chatting with unlimited messages.",
      };
      addMessage(limitMessage);
      setIsLoading(false);
      return;
    }

    try {
      // Format source content based on type
      let formattedContent = "";
      if (Array.isArray(sourceContent)) {
        formattedContent = sourceContent.map((t) => t.text).join("\n\n");
      } else {
        formattedContent = sourceContent.toString();
      }

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          message: trimmedMessage,
          sourceContent: formattedContent,
          metadata,
          contentType,
          previousMessages: messages, // Include previous messages for context
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 429) {
          throw new Error(
            errorData.error ||
              "Chat message limit exceeded. Please upgrade your plan or try again later."
          );
        }
        throw new Error(errorData.error || "Failed to get response");
      }

      const data = await response.json();
      addMessage({ role: "assistant", content: data.content });
      // Ensure scrolling happens after the message is added and rendered
      setTimeout(scrollToBottom, 100);
    } catch (error) {
      console.error("Chat error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to get response from AI"
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-background relative">
      <div
        className="flex-1 overflow-y-auto p-4 pb-16"
        ref={chatContainerRef}
        style={{
          height: "100%",
          maxHeight: "calc(100% - 70px)", // Reserve space for the input area
        }}
      >
        {isFetching ? (
          <div className="flex justify-center items-center h-full">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">
                Loading conversation...
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.length === 0 && (
              <div className="flex flex-col justify-center items-center h-32 text-center space-y-2">
                <p className="text-muted-foreground">
                  Ask questions about the{" "}
                  {contentType === "text" ? "text" : "content"}
                </p>
                <div className="text-xs text-muted-foreground/70 max-w-xs">
                  Try asking about specific details, explanations of concepts,
                  or summaries of sections
                </div>
              </div>
            )}
            {messages.map((msg, i) => (
              <div
                key={msg.id || i}
                className={cn(
                  "flex",
                  msg.role === "user" ? "justify-end" : "justify-start"
                )}
              >
                <div
                  className={cn(
                    "max-w-[95%] rounded-lg px-4 py-2",
                    msg.role === "user"
                      ? "bg-primary text-primary-foreground"
                      : "bg-muted w-full"
                  )}
                >
                  <div className="prose prose-sm dark:prose-invert max-w-none overflow-x-auto">
                    <MarkdownRender content={msg.content} />
                  </div>
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="max-w-[80%] rounded-lg px-4 py-2 bg-muted">
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-muted-foreground">Thinking...</span>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} className="h-1" />
          </div>
        )}
      </div>

      <div className="fixed bottom-0 left-0 right-0 md:absolute p-4 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 w-full">
        <div className="space-y-3">
          <form onSubmit={handleSubmit} className="flex gap-2 items-start">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={handleMessageChange}
              placeholder={`Ask about the ${
                contentType === "text" ? "text" : "content"
              }...`}
              disabled={isLoading || isFetching}
              className="flex-1 min-h-[40px] max-h-[120px] py-2 px-3 resize-none"
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  if (message.trim() && !isLoading && !isFetching) {
                    handleSubmit(
                      e as unknown as React.FormEvent<HTMLFormElement>
                    );
                  }
                }
              }}
            />
            <Button
              type="submit"
              size="icon"
              disabled={
                isLoading || isChecking || isFetching || !message.trim()
              }
              className="mt-1"
            >
              {isLoading || isChecking ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Chat;
