import type { BlogPost } from "@/data/blog-posts";
// Using type import only to avoid import issues
import Image from "next/image";
import Link from "next/link";
import { Calendar, Clock } from "lucide-react";

interface BlogPostCardProps {
  post: BlogPost;
  featured?: boolean;
}

export default function BlogPostCard({
  post,
  featured = false,
}: BlogPostCardProps) {
  return (
    <Link href={`/blog/${post.slug}`} className="group block h-full">
      <div
        className={`bg-card rounded-lg overflow-hidden border border-border hover:border-primary/50 transition-all hover:shadow-md h-full flex flex-col ${
          featured ? "md:flex-row" : ""
        }`}
      >
        <div
          className={`relative ${
            featured ? "h-64 md:h-auto md:w-1/2" : "h-48"
          } w-full overflow-hidden`}
        >
          <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all z-10" />
          <Image
            src={post.coverImage}
            alt={post.title}
            width={600}
            height={300}
            className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
            unoptimized
          />
        </div>

        <div
          className={`p-6 flex flex-col ${
            featured ? "md:w-1/2" : ""
          } flex-grow`}
        >
          <div className="flex flex-wrap gap-2 mb-3">
            {post.tags.slice(0, featured ? 3 : 2).map((tag) => (
              <span
                key={tag}
                className="text-xs px-2 py-1 rounded-full bg-primary/10 text-primary"
              >
                {tag}
              </span>
            ))}
          </div>

          <h2
            className={`${
              featured ? "text-2xl" : "text-xl"
            } font-semibold mb-3 group-hover:text-primary transition-colors line-clamp-2`}
          >
            {post.title}
          </h2>

          <p className="text-foreground/70 mb-4 text-sm line-clamp-3 flex-grow">
            {post.description}
          </p>

          <div className="flex items-center justify-between text-xs text-foreground/60 mt-auto">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>
                {new Date(post.date).toLocaleDateString("en-US", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                })}
              </span>
            </div>

            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{post.readTime} min read</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
