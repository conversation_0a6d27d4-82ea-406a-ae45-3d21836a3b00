import React, { useState } from "react";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import "katex/dist/katex.min.css";
import ReactMarkdown from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneDark } from "react-syntax-highlighter/dist/cjs/styles/prism";
import { Button } from "@/components/ui/button";
import { Copy, Check } from "lucide-react";
import { toast } from "sonner";

// Custom CodeBlock component with copy functionality
interface CodeBlockProps {
  children?: React.ReactNode;
  className?: string;
  inline?: boolean;
}

const CodeBlock = ({ children, className, inline }: CodeBlockProps) => {
  const [isCopied, setIsCopied] = useState(false);

  // Convert children to string for processing
  const codeString = String(children || "").replace(/\n$/, "");

  // Extract language from className (format: "language-javascript")
  const match = /language-(\w+)/.exec(className || "");
  const language = match ? match[1] : "";

  // Debug log for code blocks
  if (
    codeString.includes("javascript") ||
    codeString.includes("console.log") ||
    className?.includes("language-")
  ) {
    console.log("CodeBlock - Processing:", {
      codeString,
      className,
      inline,
      language,
    });
  }

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(codeString);
      setIsCopied(true);
      toast.success("Code copied to clipboard!");
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Copy error:", error);
      toast.error("Failed to copy code");
    }
  };

  // For inline code (single backticks), use simple styling - keep original behavior
  if (inline) {
    return (
      <code className="bg-muted text-foreground px-1.5 py-0.5 rounded text-sm font-mono border border-border">
        {children}
      </code>
    );
  }

  // Only apply syntax highlighter for actual code blocks (triple backticks)
  // Check if this is a real code block by looking for:
  // 1. Language specification (className="language-xxx")
  // 2. Multiline content (contains newlines)
  // 3. Code-like patterns (function calls, brackets, etc.)
  const hasLanguage = className?.includes("language-");
  const isMultiline = codeString.includes("\n");
  const looksLikeCode =
    /[{}();=]/.test(codeString) ||
    codeString.includes("function") ||
    codeString.includes("console.log");

  const isRealCodeBlock =
    hasLanguage ||
    (isMultiline && codeString.trim().length > 10) ||
    (looksLikeCode && codeString.trim().length > 20);

  if (!isRealCodeBlock) {
    // Treat as inline code - this handles single backticks properly
    return (
      <code className="bg-muted text-foreground px-1.5 py-0.5 rounded text-sm font-mono border border-border">
        {children}
      </code>
    );
  }

  // For code blocks, use syntax highlighter with copy button and always dark background
  return (
    <div className="relative group my-4">
      <div className="absolute top-3 right-3 z-10">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="h-8 w-8 p-0 opacity-100 md:opacity-0 md:group-hover:opacity-100 transition-opacity bg-gray-800/80 hover:bg-gray-700 border border-gray-600 text-gray-200"
        >
          {isCopied ? (
            <Check className="h-4 w-4 text-green-400" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </Button>
      </div>
      <SyntaxHighlighter
        style={oneDark} // Always use dark theme for code blocks
        language={language || "text"}
        PreTag="div"
        customStyle={{
          margin: 0,
          borderRadius: "0.5rem",
          fontSize: "0.875rem",
          lineHeight: "1.5",
          padding: "1rem",
          border: "1px solid #374151",
          backgroundColor: "#1f2937", // Always dark background
        }}
        codeTagProps={{
          style: {
            fontFamily:
              "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace",
          },
        }}
        wrapLines={true}
        wrapLongLines={true}
      >
        {codeString}
      </SyntaxHighlighter>
    </div>
  );
};

interface MarkdownProps {
  content: string;
}

const MarkdownRender = ({ content }: MarkdownProps) => {
  // Debug log to see the actual content being rendered
  if (
    content.includes("```") ||
    content.includes("javascript") ||
    content.includes("console.log")
  ) {
    console.log("MarkdownRender - Code content detected:", content);
  }

  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath, remarkGfm]}
      rehypePlugins={[rehypeKatex, rehypeRaw]}
      components={{
        // Custom code block component
        code: CodeBlock,
        // Override default components to ensure proper alignment
        ul: ({ ...props }) => (
          <ul className="list-disc pl-5 text-left" {...props} />
        ),
        ol: ({ ...props }) => (
          <ol className="list-decimal pl-5 text-left" {...props} />
        ),
        li: ({ ...props }) => <li className="mb-1 text-left" {...props} />,
        p: ({ ...props }) => <p className="text-left mb-3" {...props} />,
      }}
    >
      {content}
    </ReactMarkdown>
  );
};

export default MarkdownRender;
