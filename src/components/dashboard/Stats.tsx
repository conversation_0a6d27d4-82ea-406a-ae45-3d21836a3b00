"use client";

import { useDashboardStore } from "@/store/dashboardStore";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, BookOpen, ClipboardList, MessageCircle } from "lucide-react";
import { Progress } from "@/components/ui/progress";

// Enhanced Stats Component
export function Stats() {
  const { usage, isLoading, planType } = useDashboardStore();

  const usageCards = [
    {
      icon: MessageCircle,
      label: "AI Chat",
      feature: "aiChat",
      description: "Daily chat messages",
      className: "bg-purple-500/10 text-purple-500",
    },
    {
      icon: FileText,
      label: "Transformations",
      feature: "transformations",
      description: "Weekly transformations",
      className: "bg-blue-500/10 text-blue-500",
    },
    {
      icon: BookOpen,
      label: "Flashcards",
      feature: "flashcards",
      description: "Total flashcards",
      className: "bg-green-500/10 text-green-500",
    },
    {
      icon: ClipboardList,
      label: "Quizzes",
      feature: "quizzes",
      description: "Total quizzes",
      className: "bg-orange-500/10 text-orange-500",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {usageCards.map((card, index) => {
        const featureUsage = usage[card.feature as keyof typeof usage];
        const displayLimit = featureUsage.unlimited
          ? "∞"
          : featureUsage.limit.toLocaleString();
        const percentage = featureUsage.unlimited ? 0 : featureUsage.percentage;

        return (
          <Card
            key={index}
            className="overflow-hidden border bg-card transition-all hover:shadow-md"
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {card.label}
              </CardTitle>
              <div className={`rounded-full p-2 ${card.className}`}>
                <card.icon className="h-4 w-4" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    {isLoading ? (
                      <div className="h-4 w-16 bg-muted animate-pulse rounded"></div>
                    ) : (
                      `${featureUsage.current} / ${displayLimit}`
                    )}
                  </span>
                  {!featureUsage.unlimited && (
                    <span className="text-xs text-muted-foreground">
                      {Math.round(percentage)}%
                    </span>
                  )}
                </div>
                {!featureUsage.unlimited && (
                  <Progress value={percentage} className="h-2" />
                )}
                <div className="flex justify-between items-center">
                  <p className="text-xs text-muted-foreground">
                    {card.description}
                  </p>
                  {!featureUsage.unlimited && (
                    <span className="text-xs text-muted-foreground">
                      {featureUsage.remaining} left
                    </span>
                  )}
                </div>
                {featureUsage.unlimited && (
                  <p className="text-xs text-green-600 font-medium">
                    Unlimited ({planType} plan)
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
