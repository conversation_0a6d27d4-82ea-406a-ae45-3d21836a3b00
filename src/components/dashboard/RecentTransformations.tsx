"use client";

import { useRecentTransformationsStore } from "@/store/recentTransformationsStore"; // Changed
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import {
  FileText,
  ChevronRight,
  Calendar,
  Video,
  Globe,
  BookText,
  File,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useEffect } from "react"; // Added

export function RecentTransformations() {
  const router = useRouter();
  // Changed to useRecentTransformationsStore
  const { recentContent, isLoading, fetchRecentTransformations, error } =
    useRecentTransformationsStore();

  useEffect(() => {
    // Fetch data when component mounts if not already fetched recently by polling
    // The store itself handles polling, this is more for initial load if needed
    // and to ensure data is present if polling hasn't kicked in yet or was stopped.
    if (recentContent.length === 0 && !isLoading) {
      fetchRecentTransformations(true); // Force fetch if empty and not loading
    }
  }, [fetchRecentTransformations, recentContent.length, isLoading]);

  // Get the 5 most recent transformations (store already limits to 7, slicing for display consistency if needed)
  const displayTransformations = recentContent.slice(0, 5);

  // Function to get badge variant based on length type
  const getOutputBadgeVariant = (outputType: "summary" | "notes"): string => {
    switch (outputType) {
      case "summary":
        return "bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-400";
      case "notes":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
    }
  };

  // Function to get card background color based on content type
  const getContentTypeColor = (contentType: string) => {
    switch (contentType?.toLowerCase()) {
      case "youtube":
        return "bg-red-600/10 text-red-600";
      case "text":
        return "bg-indigo-600/10 text-indigo-600";
      case "webpage":
        return "bg-blue-600/10 text-blue-600";
      case "pdf":
        return "bg-orange-600/10 text-orange-600";
      default:
        return "bg-gray-600/10 text-gray-600";
    }
  };

  // Function to get icon based on content type
  const getContentTypeIcon = (contentType: string) => {
    switch (contentType?.toLowerCase()) {
      case "youtube":
        return <Video className="h-5 w-5" />;
      case "pdf":
        return <File className="h-5 w-5" />;
      case "text":
        return <BookText className="h-5 w-5" />;
      case "webpage":
        return <Globe className="h-5 w-5" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  // Function to clean title if it has markdown formatting
  const cleanTitle = (title: string) => {
    if (title.startsWith("##") || title.startsWith("**")) {
      return title
        .replace(/^(##|\*\*)\s*/, "")
        .replace(/(\*\*)$/, "")
        .trim();
    }
    return title;
  };

  return (
    <Card className="border shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl">Recent Transformations</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/transformations")}
            className="text-sm flex items-center gap-1 text-muted-foreground hover:text-foreground"
          >
            View all
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>
          Your most recently transformed content
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        {isLoading && displayTransformations.length === 0 ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="flex items-start gap-3 p-3 rounded-lg bg-muted/30 animate-pulse h-16"
              ></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center py-6 bg-red-50 dark:bg-red-950/50 rounded-lg">
            <p className="text-red-600 dark:text-red-400">Error: {error}</p>
            <Button
              onClick={() => fetchRecentTransformations(true)}
              variant="link"
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        ) : displayTransformations.length === 0 ? (
          <div className="text-center py-6 bg-muted/30 rounded-lg">
            <p className="text-muted-foreground">
              You haven&apos;t transformed any content yet
            </p>
            <Button
              onClick={() => router.push("/new")}
              variant="link"
              className="mt-2"
            >
              Transform your first content
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {displayTransformations.map((item) => (
              <div
                key={item.id}
                className="flex items-start gap-3 p-3 rounded-lg transition-colors hover:bg-muted/50 cursor-pointer"
                onClick={() =>
                  router.push(
                    `/transform/${
                      item.contentId
                    }?type=${item.contentType.toLowerCase()}&output=${
                      item.outputType
                    }`
                  )
                }
              >
                <div
                  className={cn(
                    "p-2 rounded-full",
                    getContentTypeColor(item.contentType)
                  )}
                >
                  {getContentTypeIcon(item.contentType)}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm truncate">
                    {cleanTitle(item.title)}
                  </h4>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge
                      variant="outline"
                      className={getOutputBadgeVariant(
                        item.outputType as "summary" | "notes"
                      )}
                    >
                      {item.outputType}
                    </Badge>
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3 mr-1" />
                      {format(new Date(item.generatedAt), "MMM d, yyyy")}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
