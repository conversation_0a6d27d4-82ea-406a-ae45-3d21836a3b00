"use client";

import { useEffect } from "react";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslateStore, LanguageCode } from "@/store/translateStore";

const languageOptions: { value: LanguageCode; label: string }[] = [
  { value: "en", label: "English" },
  { value: "ar", label: "Arabic" },
  { value: "es", label: "Spanish" },
  { value: "fr", label: "French" },
  { value: "de", label: "German" },
  { value: "it", label: "Italian" },
  { value: "pt", label: "Portuguese" },
  { value: "ru", label: "Russian" },
  { value: "ja", label: "Japanese" },
  { value: "zh", label: "Chinese" },
];

interface TranslationSelectorProps {
  summary: string;
}

/**
 * Translator component that triggers translation of markdown content.
 */
export function Translator({ summary }: TranslationSelectorProps) {
  const {
    selectedLanguage,
    setSelectedLanguage,
    setTranslatedSummary,
    isTranslating,
    setIsTranslating,
  } = useTranslateStore();

  useEffect(() => {
    const translate = async () => {
      if (!summary || selectedLanguage === "en") {
        setTranslatedSummary("");
        return;
      }

      setIsTranslating(true);
      try {
        const res = await fetch("/api/translate", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ summary, target_lang: selectedLanguage }),
        });

        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(errorData.error || "Translation failed");
        }

        const data = await res.json();
        if (!data.translatedText) {
          throw new Error("No translation returned");
        }

        setTranslatedSummary(data.translatedText);
        toast.success("Translation successful");
      } catch (error) {
        console.error("Translation error:", error);
        toast.error(
          error instanceof Error ? error.message : "Translation error"
        );
        setTranslatedSummary("");
      } finally {
        setIsTranslating(false);
      }
    };

    translate();
  }, [summary, selectedLanguage, setIsTranslating, setTranslatedSummary]);

  return (
    <Select
      value={selectedLanguage}
      onValueChange={(value) => setSelectedLanguage(value as LanguageCode)}
      disabled={isTranslating}
    >
      <SelectTrigger className="w-[100px] sm:w-[180px]">
        <SelectValue>
          <div className="flex items-center gap-2">
            {isTranslating && <Loader2 className="h-4 w-4 animate-spin" />}
            <span>
              {languageOptions.find((opt) => opt.value === selectedLanguage)
                ?.label || "Language"}
            </span>
          </div>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {languageOptions.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            disabled={isTranslating}
          >
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
