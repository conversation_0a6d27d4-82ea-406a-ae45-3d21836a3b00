"use client";

import { useEffect, useState, useRef } from "react";
import { createClient } from "@/utils/supabase/client";
import { useAuthStore } from "@/store/authStore";
import { Loader2 } from "lucide-react";
import { usePathname } from "next/navigation";
// Define User type locally since we're moving away from Prisma
type User = {
  id: string;
  email: string | null;
  displayName: string | null;
  avatarUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
};
import { isPublicRoute as checkPublicRoute } from "@/utils/routes";

export default function AuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const supabase = createClient();
  const { user, setUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(true);
  // We're storing dbUser in state for potential future use
  const [dbUser, setDbUser] = useState<User | null>(null);
  const pathname = usePathname();
  const profileFetchedRef = useRef(false);
  const authInitializedRef = useRef(false);

  // Determine if the current route is protected
  const isAuthPage = pathname?.startsWith("/auth");
  const isPublicRoute = pathname ? checkPublicRoute(pathname) : true;
  const isProtectedRoute = !isPublicRoute;

  // Fetch user profile only once when user is available - optimized version
  useEffect(() => {
    // Skip if no user or already fetched
    if (!user || profileFetchedRef.current) return;

    // Try to load from localStorage first for immediate rendering
    if (user && !dbUser) {
      const cachedProfile = localStorage.getItem("user_profile");
      if (cachedProfile) {
        try {
          const parsedProfile = JSON.parse(cachedProfile);
          setDbUser(parsedProfile);

          // If the cached profile is recent (less than 1 hour old), don't fetch again
          const profileTimestamp = localStorage.getItem(
            "user_profile_timestamp"
          );
          if (profileTimestamp) {
            const timestamp = parseInt(profileTimestamp, 10);
            const now = Date.now();
            const oneHour = 60 * 60 * 1000;

            if (now - timestamp < oneHour) {
              profileFetchedRef.current = true;
              return;
            }
          }
        } catch (e) {
          console.error("Error parsing cached profile:", e);
        }
      }
    }

    // Use a small delay to prioritize rendering the UI first
    const timeoutId = setTimeout(async () => {
      try {
        profileFetchedRef.current = true; // Mark as fetched to prevent duplicate requests
        const response = await fetch("/api/user/profile", {
          cache: "no-store",
          headers: { "x-request-id": crypto.randomUUID() }, // Add unique request ID
        });

        if (response.ok) {
          const userData = await response.json();
          setDbUser(userData.user);
          // Store user data in localStorage for faster access on subsequent loads
          if (userData.user) {
            localStorage.setItem("user_profile", JSON.stringify(userData.user));
            localStorage.setItem(
              "user_profile_timestamp",
              Date.now().toString()
            );
          }
        }
      } catch (dbError) {
        console.error("Error fetching user profile:", dbError);
      }
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [user, dbUser]);

  // Initialize auth and check session
  useEffect(() => {
    if (authInitializedRef.current) return;

    const initializeAuth = async () => {
      try {
        authInitializedRef.current = true;

        // Get the session and update the store
        const {
          data: { session },
        } = await supabase.auth.getSession();

        const currentUser = session?.user ?? null;
        setUser(currentUser);

        // Set up auth change listener
        const {
          data: { subscription },
        } = supabase.auth.onAuthStateChange(async (event, session) => {
          const updatedUser = session?.user ?? null;
          setUser(updatedUser);

          // Reset profile fetched flag when auth state changes
          if (event === "SIGNED_IN" || event === "SIGNED_OUT") {
            profileFetchedRef.current = false;
            if (event === "SIGNED_OUT") {
              setDbUser(null);
              localStorage.removeItem("user_profile");
            }
          }

          if (event === "SIGNED_IN" && isAuthPage) {
            // Hard redirect on sign in from auth page
            window.location.href = "/new";
            return;
          }

          if (event === "SIGNED_OUT" && isProtectedRoute) {
            // Hard redirect on sign out from protected route
            window.location.href = "/auth";
            return;
          }
        });

        return () => {
          subscription.unsubscribe();
        };
      } catch (error) {
        console.error("Auth initialization error:", error);
        setUser(null);
        setDbUser(null);
      } finally {
        // Reduce loading time by setting isLoading to false as soon as auth is initialized
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [setUser, supabase.auth, isAuthPage, isProtectedRoute]);

  // Client-side route protection with optimized redirects
  useEffect(() => {
    if (isLoading) return; // Don't redirect while loading

    // If not logged in and trying to access protected route
    if (!user && isProtectedRoute) {
      window.location.href = "/auth";
      return;
    }

    // If logged in and trying to access auth page
    if (user && isAuthPage) {
      window.location.href = "/new";
      return;
    }

    // If logged in and on homepage
    if (user && pathname === "/") {
      window.location.href = "/new";
      return;
    }
  }, [user, pathname, isLoading, isProtectedRoute, isAuthPage]);

  // Show loading state only for protected routes when authentication is necessary
  if (isLoading && isProtectedRoute) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="w-12 h-12 animate-spin text-primary mx-auto" />
          <p className="text-lg font-semibold">Loading...</p>
        </div>
      </div>
    );
  }

  // If we're on a protected route and not authenticated, don't render children
  if (!user && isProtectedRoute) {
    return null;
  }

  // If we're on auth page and authenticated, don't render children
  if (user && isAuthPage) {
    return null;
  }

  // Otherwise, render children
  return <>{children}</>;
}
