'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useAuthStore } from '@/store/authStore';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { AuthError } from '@supabase/supabase-js';
import Google from './Google';

function getAuthErrorMessage(error: AuthError | Error | unknown): string {
  if (error instanceof Error) {
    // Handle specific Supabase error codes
    if ('code' in error && typeof error.code === 'number') {
      switch (error.code) {
        case 400:
          return 'Provider is not enabled. Please contact support.';
        case 401:
          return 'Invalid credentials. Please try again.';
        case 422:
          return 'Invalid email format. Please check your email.';
        case 429:
          return 'Too many requests. Please try again later.';
        default:
          return error.message || 'An error occurred during authentication.';
      }
    }
    return error.message;
  }
  return 'An unexpected error occurred. Please try again.';
}

export function GoogleSignInButton() {
  const { signInWithGoogle } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);

  const handleSignIn = async () => {
    try {
      setIsLoading(true);
      await signInWithGoogle();
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error);
      toast.error(errorMessage);
      console.error("Google sign-in error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant="outline"
      onClick={handleSignIn}
      disabled={isLoading}
      className="w-full"
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
       <Google/>
      )}
      Sign in with Google
    </Button>
  );
}

export function EmailSignInForm({ captchaToken }: { captchaToken?: string }) {
  const { signInWithEmail } = useAuthStore();
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSent, setIsSent] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast.error("Please enter your email address");
      return;
    }

    try {
      setIsLoading(true);
      await signInWithEmail(email, captchaToken);
      setIsSent(true);
      toast.success(
        "Magic link sent! Please check your email (including spam folder)."
      );
      setEmail("");
    } catch (error) {
      const errorMessage = getAuthErrorMessage(error);
      toast.error(errorMessage);
      console.error("Email sign-in error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <Input
        type="email"
        placeholder="Enter your email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        disabled={isLoading}
        required
      />
      <Button type="submit" className="w-full" disabled={isLoading}>
        {isLoading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : isSent ? (
          "Check your email"
        ) : (
          "Sign in with Email"
        )}
      </Button>
      {isSent && (
        <p className="text-sm text-muted-foreground text-center">
          Didn&apos;t receive the email? Check your spam folder or try again.
        </p>
      )}
    </form>
  );
}
