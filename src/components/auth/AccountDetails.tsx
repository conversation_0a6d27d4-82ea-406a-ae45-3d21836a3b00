"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { User, LogOut } from "lucide-react";
import { useAuthStore } from "@/store/authStore"; // Import useAuthStore
interface AccountDetailsCardProps {
  onSignOut: () => void; 
}
export default function AccountDetails({
  onSignOut,
}: AccountDetailsCardProps) {
  const { user } = useAuthStore(); 

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-xl">
          <User className="h-5 w-5 text-primary" />
          Profile
        </CardTitle>
        <CardDescription>
          Your account information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 flex-1 flex flex-col">
        <div className="grid gap-1.5">
          <Label
            htmlFor="name"
            className="text-sm font-medium text-muted-foreground"
          >
            Name
          </Label>
          <p id="name" className="text-base">
            {user?.user_metadata?.full_name || "Not provided"}
          </p>
        </div>
        <div className="grid gap-1.5">
          <Label
            htmlFor="email"
            className="text-sm font-medium text-muted-foreground"
          >
            Email
          </Label>
          <p id="email" className="text-base">
            {user?.email || "Not available"}
          </p>
        </div>
        <div className="mt-auto pt-4">
          <Button
            variant="destructive"
            onClick={onSignOut} // This prop is still necessary
            className="w-full sm:w-auto"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Sign Out
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
