"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

import { useSubscriptionStore } from "@/store/subscriptionStore";
import { useRouter } from "next/navigation";
import {
  TrendingUp,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  CreditCard,
} from "lucide-react";

interface SubscriptionWidgetProps {
  className?: string;
  compact?: boolean;
}

export function SubscriptionWidget({
  className = "",
  compact = false,
}: SubscriptionWidgetProps) {
  const { subscription, loading, error } = useSubscriptionStore();
  const router = useRouter();

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-500";
      case "ON_HOLD":
        return "bg-yellow-500";
      case "CANCELLED":
        return "bg-red-500";
      case "PENDING":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case "ACTIVE":
        return <CheckCircle className="h-3 w-3" />;
      case "ON_HOLD":
        return <AlertCircle className="h-3 w-3" />;
      case "CANCELLED":
        return <AlertCircle className="h-3 w-3" />;
      case "PENDING":
        return <Clock className="h-3 w-3" />;
      default:
        return <CreditCard className="h-3 w-3" />;
    }
  };


  if (loading) {
    return (
      <div
        className={`flex items-center gap-2 p-3 border rounded-lg ${className}`}
      >
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm">Loading...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={`flex items-center gap-2 p-3 border rounded-lg text-red-500 ${className}`}
      >
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm">Error loading subscription</span>
      </div>
    );
  }

  const isFreePlan = !subscription || subscription.planType === "Free";
  const hasActiveSubscription = subscription?.status === "ACTIVE" || subscription?.status === "NON_RENEWING";

  if (compact) {
    return (
      <div
        className={`flex items-center justify-between p-3 border rounded-lg ${className}`}
      >
        <div className="flex items-center gap-2">
          {getStatusIcon(subscription?.status || null)}
          <div>
            <p className="text-sm font-medium">
              {subscription?.planType || "Free"}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge
            variant={hasActiveSubscription ? "default" : "secondary"}
            className={`${getStatusColor(
              subscription?.status || null
            )} text-white text-xs`}
          >
            {subscription?.status || "Free"}
          </Badge>
          {isFreePlan && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => router.push("/upgrade")}
              className="text-xs px-2 py-1 h-6"
            >
              Upgrade
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 p-4 border rounded-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {getStatusIcon(subscription?.status || null)}
          <div>
            <p className="font-medium">
              {subscription?.planType || "Free"} Plan
            </p>
            {subscription?.billingCycle && (
              <p className="text-xs text-muted-foreground capitalize">
                {subscription.billingCycle} billing
              </p>
            )}
          </div>
        </div>
        <Badge
          variant={hasActiveSubscription ? "default" : "secondary"}
          className={`${getStatusColor(
            subscription?.status || null
          )} text-white`}
        >
          {subscription?.status || "Free"}
        </Badge>
      </div>

  

      {/* Action Button */}
      {isFreePlan && (
        <Button
          size="sm"
          variant="default"
          onClick={() => router.push("/upgrade")}
          className="w-full"
        >
          <TrendingUp className="h-3 w-3 mr-1" />
          Upgrade Plan
        </Button>
      )}
    </div>
  );
}
