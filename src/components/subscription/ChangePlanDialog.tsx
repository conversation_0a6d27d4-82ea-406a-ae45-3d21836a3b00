"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  Dialog<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { pricingData } from "@/data/pricingData";
import type { PlanType } from "@/data/pricingData";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { AlertTriangle, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { cn } from "@/lib/utils";
import { Check } from "lucide-react";

interface ChangePlanDialogProps {
  variant?:
    | "default"
    | "outline"
    | "secondary"
    | "destructive"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function ChangePlanDialog({
  variant = "default",
  size = "default",
  className = "",
}: ChangePlanDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showWarning, setShowWarning] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<{
    planType: PlanType;
    billingCycle: "monthly" | "annual";
  } | null>(null);

  const { subscription } = useSubscriptionStore();
  const currentPlanType = subscription?.planType || "Free";

  // Filter out Free plan and current plan
  const availablePlans = Object.entries(pricingData.plans).filter(
    ([planName]) => planName !== "Free" && planName !== currentPlanType
  ) as [PlanType, (typeof pricingData.plans)[PlanType]][];

  // Get billing cycle from current subscription or default to monthly
  const currentBillingCycle: "monthly" | "annual" =
    subscription?.billingCycle === "annual" ? "annual" : "monthly";

  const handlePlanChangeConfirm = () => {
    if (!selectedPlan) return;
    setShowWarning(true);
  };

  const handlePlanChange = async () => {
    if (!selectedPlan) return;

    setLoading(true);
    setShowWarning(false);
    
    try {
      // Show loading toast
      toast.loading("Initiating plan change...", { id: "plan-change" });
      
      const response = await fetch("/api/subscriptions/change-plan", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          newPlanType: selectedPlan.planType,
          newBillingCycle: selectedPlan.billingCycle,
        }),

      });

      const data = await response.json();
      if (!response.ok) throw new Error(data.error);

      // Redirect to checkout if URL is provided
      if (data.checkoutUrl) {
        toast.success("Redirecting to checkout...", { id: "plan-change" });
        // Keep dialog open and loading state until redirect
        window.location.href = data.checkoutUrl;
      } else {
        toast.success(data.message || "Plan change initiated", { id: "plan-change" });
        setIsOpen(false);
        setLoading(false);
      }
      
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to change plan",
        { id: "plan-change" }
      );
      setLoading(false);
    }
    // Note: Don't set loading to false here as we want to keep it until redirect
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      // Prevent closing dialog when loading
      if (!loading) {
        setIsOpen(open);
      }
    }}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          Change Plan
        </Button>
      </DialogTrigger>
      <DialogContent
        className="w-full max-w-4xl md:max-w-5xl xl:max-w-6xl p-8 md:p-12 rounded-2xl shadow-2xl border-0 bg-muted h-[95vh] overflow-auto"
        style={{ minWidth: 0 }}
      >
        <DialogHeader>
          <DialogTitle className="text-xl md:text-2xl font-bold mb-4 text-center">
            Change Your Plan
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 xl:gap-12">
          {availablePlans.map(([planType, plan]) => (
            <Card
              key={planType}
              className={cn(
                "cursor-pointer transition-all hover:shadow-xl border-2 flex flex-col h-full group",
                selectedPlan?.planType === planType &&
                  "border-primary ring-2 ring-primary",
                plan.isPopular && "border-purple-500 dark:border-purple-500"
              )}
              onClick={() =>
                setSelectedPlan({
                  planType,
                  billingCycle: currentBillingCycle,
                })
              }
            >
              {plan.isPopular && (
                <div className="absolute top-0 right-0">
                  <Badge className="rounded-tl-none rounded-br-none rounded-tr-md rounded-bl-md bg-primary text-primary-foreground">
                    Popular
                  </Badge>
                </div>
              )}
              <CardHeader className={cn("pb-3", plan.color)}>
                <CardTitle className="text-lg flex items-center gap-2">
                  {planType}
                </CardTitle>
                <div className="flex items-baseline">
                  <span className="text-3xl font-bold">
                    ₦{plan[currentBillingCycle].toLocaleString()}
                  </span>
                  <span className="text-sm text-muted-foreground ml-2">
                    /{currentBillingCycle === "monthly" ? "month" : "year"}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground h-12 overflow-hidden">
                  {plan.description} {/* Re-added plan.description */}
                </p>
              </CardHeader>
              <CardContent className="p-6 space-y-4 flex-grow">
                <div className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-start">
                      <Check
                        className={cn(
                          "h-5 w-5 mr-3 shrink-0",
                          plan.iconColor || "text-primary"
                        )}
                      />
                      <span className="text-sm">{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <DialogFooter>
          <Button
            onClick={handlePlanChangeConfirm}
            disabled={!selectedPlan || loading}
            className="w-full text-base py-3 font-semibold"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Processing...
              </>
            ) : (
              "Change Plan"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>

      {/* Warning Dialog */}
      <AlertDialog open={showWarning} onOpenChange={(open) => {
        // Prevent closing warning dialog when loading
        if (!loading) {
          setShowWarning(open);
        }
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Confirm Plan Change
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>
                <strong>This will cancel your current {currentPlanType} plan.</strong>
              </p>
              <p>
                Your current plan will remain active until the end of your billing period, 
                and you'll be redirected to checkout to complete the change to the {selectedPlan?.planType} plan.
              </p>
              <p className="text-sm text-muted-foreground">
                If you don't complete the checkout, you can reactivate your current plan later.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handlePlanChange}
              disabled={loading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Continue to Checkout"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Dialog>
  );
}
