"use client";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CreditCard } from "lucide-react";
import { SubscriptionStatus } from "./SubscriptionStatus";

interface SubscriptionCardProps {

  className?: string;
}

export function SubscriptionCard({
  className = "",
}: SubscriptionCardProps) {
  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="space-y-1">
        <CardTitle className="flex items-center gap-2 text-xl">
          <CreditCard className="h-5 w-5" />
          Subscription
        </CardTitle>

      </CardHeader>
      <CardContent>
        <SubscriptionStatus />
      </CardContent>
    </Card>
  );
}
