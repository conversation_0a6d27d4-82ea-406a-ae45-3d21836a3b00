"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";

import { <PERSON><PERSON><PERSON><PERSON>gle, Loader2, X } from "lucide-react";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { toast } from "sonner";

interface CancelSubscriptionButtonProps {
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function CancelSubscriptionButton({
  variant = "outline",
  size = "default",
  className = "",
}: CancelSubscriptionButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { subscription, cancelSubscription } = useSubscriptionStore();

  const handleCancel = async () => {
    if (!subscription) return;

    setLoading(true);
    try {
      // Always cancel at period end - user keeps access until billing period ends
      const result = await cancelSubscription(true);

      toast.success(result.message || "Subscription cancelled successfully");
      setIsOpen(false);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to cancel subscription"
      );
    } finally {
      setLoading(false);
    }
  };

  // Early return if no subscription data
  if (!subscription) {
    return null;
  }

  const currentPeriodEnd = subscription.currentPeriodEnd
    ? new Date(subscription.currentPeriodEnd).toLocaleDateString()
    : "the end of your billing period";

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          <X className="h-4 w-4 mr-2" />
          Cancel Subscription
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-destructive" />
            Cancel Subscription
          </DialogTitle>
          <DialogDescription>
            We're sorry to see you go! Your {subscription.planType} plan will be
            cancelled at the end of your current billing period.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="p-4 border rounded-lg bg-muted/50">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-amber-500" />
              <span className="font-medium">What happens when you cancel:</span>
            </div>
            <ul className="text-sm text-muted-foreground space-y-1 ml-6">
              <li>• You'll keep full access until {currentPeriodEnd}</li>
              <li>• No future charges will be made</li>
              <li>• Your data and settings will be preserved</li>
              <li>• You can reactivate anytime before the period ends</li>
            </ul>
          </div>

         <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Good to know:</strong> You can upgrade to a different plan
              instead of cancelling, which will take effect immediately and give
              you access to new features right away.
            </p>
          </div> 
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={loading}
          >
            Keep Subscription
          </Button>
          <Button
            variant="destructive"
            onClick={handleCancel}
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Cancelling...
              </>
            ) : (
              "Confirm Cancellation"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
