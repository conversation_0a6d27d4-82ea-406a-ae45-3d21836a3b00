"use client";

import { useEffect } from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Receipt,
  Download,
  Loader2,
  AlertCircle,
  FileText,
} from "lucide-react";
import {
  useBillingHistory,
} from "@/store/subscriptionStore";
import { BillingHistoryItem } from "@/types/subscription";

interface BillingHistoryProps {
  className?: string;
}

export function BillingHistory({ className = "" }: BillingHistoryProps) {
  const { billingHistory, loading, error, refetch } = useBillingHistory();

  useEffect(() => {
    refetch();
  }, [refetch]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-500 hover:bg-green-600";
      case "ATTENTION":
        return "bg-yellow-500 hover:bg-yellow-600";
      case "CANCELLED":
      case "COMPLETED":
        return "bg-red-500 hover:bg-red-600";
      case "PENDING":
        return "bg-blue-500 hover:bg-blue-600";
      case "NON_RENEWING":
        return "bg-purple-500 hover:bg-purple-600";
      default:
        return "bg-gray-500 hover:bg-gray-600";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const calculateAmount = (subscription: BillingHistoryItem) => {
    // Pricing based on plan type and billing cycle in Naira
    const pricing = {
      Free: { monthly: 0, annual: 0 },
      Starter: { monthly: 4500, annual: 43200 },
      Pro: { monthly: 8500, annual: 81600 },
      Unlimited: { monthly: 15000, annual: 144000 },
    };

    const planPricing = pricing[subscription.planType as keyof typeof pricing];
    if (!planPricing) return "₦0";

    const amount =
      subscription.billingCycle === "annual"
        ? planPricing.annual
        : planPricing.monthly;

    return `₦${amount.toLocaleString()}`;
  };

  const handleDownloadInvoice = (subscriptionId: string) => {
    // Mock download functionality - in real app, this would call an API
    console.log(`Downloading invoice for subscription: ${subscriptionId}`);
    // You can implement actual invoice generation/download here
  };

  if (loading) {
    return (
      <Card className={`w-full ${className}`}>
        <CardHeader className="space-y-1">
          <CardTitle className="flex items-center gap-2 text-xl">
            <Receipt className="h-5 w-5" />
            Billing History
          </CardTitle>
          <CardDescription>
            Your subscription and billing history
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading billing history...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`w-full ${className}`}>
        <CardHeader className="space-y-1">
          <CardTitle className="flex items-center gap-2 text-xl">
            <Receipt className="h-5 w-5" />
            Billing History
          </CardTitle>
          <CardDescription>
            Your subscription and billing history
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8 text-destructive">
            <AlertCircle className="h-6 w-6 mr-2" />
            <span>Failed to load billing history</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader className="space-y-1">
        <CardTitle className="flex items-center gap-2 text-xl">
          <Receipt className="h-5 w-5" />
          Billing History
        </CardTitle>
        <CardDescription>
          {billingHistory.length > 0
            ? `${billingHistory.length} subscription record${
                billingHistory.length !== 1 ? "s" : ""
              } found`
            : "No billing history available"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {billingHistory.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No billing history</h3>
            <p className="text-muted-foreground mb-4">
              You haven't made any subscription purchases yet.
            </p>
            <Button variant="outline" size="sm">
              Upgrade to Pro
            </Button>
          </div>
        ) : (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Billing</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Invoice</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {billingHistory.map((subscription) => (
                  <TableRow key={subscription.id} className="hover:bg-muted/50">
                    <TableCell className="font-medium">
                      {formatDate(subscription.createdAt)}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {subscription.planType}
                        </span>
                        <span className="text-sm text-muted-foreground capitalize">
                          {subscription.billingCycle}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">
                      <div className="flex flex-col">
                        <span>
                          {formatDate(subscription.currentPeriodStart)}
                        </span>
                        <span className="text-xs">
                          to {formatDate(subscription.currentPeriodEnd)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="secondary"
                        className={`${getStatusColor(
                          subscription.status
                        )} text-white text-xs`}
                      >
                        {subscription.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {calculateAmount(subscription)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDownloadInvoice(subscription.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Download className="h-4 w-4" />
                        <span className="sr-only">Download invoice</span>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
