import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { Loader2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface ReactivateButtonProps {
  variant?: "default" | "outline" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}

export function ReactivateButton({
  variant = "outline",
  size = "default",
  className = "",
}: ReactivateButtonProps) {
  const { reactivateSubscription, reactivating, subscription } = useSubscriptionStore();
  
  // Check if this is a plan change reactivation
  const hasPendingPlanChange = !!subscription?.pendingPlanType && !!subscription?.cancelAtPeriodEnd;

  const handleReactivate = async () => {
    try {
      await reactivateSubscription();
    } catch (error) {
      console.error("Failed to reactivate subscription:", error);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant={variant} size={size} className={className}>
          {reactivating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Reactivating...
            </>
          ) : (
            "Reactivate Subscription"
          )}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {hasPendingPlanChange ? "Cancel Plan Change" : "Reactivate Subscription"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {hasPendingPlanChange ? (
              <>
                This will cancel your pending plan change to {subscription?.pendingPlanType} and 
                keep your current {subscription?.planType} plan active. You&apos;ll continue 
                to be billed according to your current billing cycle.
              </>
            ) : (
              <>
                Your subscription will be reactivated immediately. You&apos;ll
                continue to be billed according to your current billing cycle.
              </>
            )}
            {" "}Are you sure you want to continue?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleReactivate} disabled={reactivating}>
            {reactivating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {hasPendingPlanChange ? "Canceling..." : "Reactivating..."}
              </>
            ) : (
              hasPendingPlanChange ? "Yes, cancel plan change" : "Yes, reactivate"
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
