"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Separator } from "@/components/ui/separator";
import { useSubscriptionStore } from "@/store/subscriptionStore";

import {
  CreditCard,
  Calendar,
  TrendingUp,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { CancelSubscriptionButton } from "./CancelSubscriptionButton";
import { ChangePlanDialog } from "./ChangePlanDialog";
import { ReactivateButton } from "./ReactivateButton";
import type { ButtonProps } from "@/components/ui/button";

interface SubscriptionStatusProps {
  className?: string;
  buttonVariant?: ButtonProps["variant"];
  buttonSize?: ButtonProps["size"];
}

export function SubscriptionStatus({
  className = "",
  buttonVariant = "default",
  buttonSize = "default",
}: SubscriptionStatusProps) {
  const { subscription, loading, error } = useSubscriptionStore();

  const router = useRouter();

  const handleUpgrade = () => {
    router.push("/upgrade");
  };

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-500";
      case "ON_HOLD":
        return "bg-yellow-500";
      case "CANCELLED":
        return "bg-red-500";
      case "PENDING":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: string | null) => {
    switch (status) {
      case "ACTIVE":
        return <CheckCircle className="h-4 w-4" />;
      case "ON_HOLD":
        return <AlertCircle className="h-4 w-4" />;
      case "CANCELLED":
        return <AlertCircle className="h-4 w-4" />;
      case "PENDING":
        return <Clock className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-center p-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading subscription...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-center p-8 text-red-500">
          <AlertCircle className="h-6 w-6" />
          <span className="ml-2">Failed to load subscription</span>
        </div>
      </div>
    );
  }

  const isFreePlan =
    !subscription || subscription.planType === "Free" || !subscription.status;
  const isActive = subscription?.status === "ACTIVE" || subscription?.status === "NON_RENEWING";
  const isActuallyCancelled = subscription?.status === "CANCELLED";
  const isScheduledToCancel = !!subscription?.cancelAtPeriodEnd; // Correctly interpret truthy values
  const hasPendingPlanChange = !!subscription?.pendingPlanType && isScheduledToCancel;

  // Determine the actual plan type to display
  const displayPlanType = subscription?.planType || "Free";
  const displayStatus = subscription?.status || "Free";

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Plan Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {getStatusIcon(subscription?.status || null)}
            <div>
              <p className="font-medium">{displayPlanType} Plan</p>
              {subscription?.billingCycle && isActive && (
                <p className="text-sm text-muted-foreground capitalize">
                  {subscription.billingCycle} billing
                </p>
              )}
            </div>
          </div>
        </div>
        <Badge
          variant={isActive ? "default" : "secondary"}
          className={`${getStatusColor(
            subscription?.status || null
          )} text-white`}
        >
          {displayStatus}
        </Badge>
      </div>

      {/* Subscription Details */}
      {isActive && subscription && (
        <div className="space-y-3">
          <Separator />
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-muted-foreground">Next billing</p>
                <p className="font-medium">
                  {formatDate(subscription.currentPeriodEnd!)}
                </p>
              </div>
            </div>
            {subscription.cancelAtPeriodEnd && (
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-yellow-500" />
                <div>
                  {hasPendingPlanChange ? (
                    <>
                      <p className="text-yellow-600">Plan change pending</p>
                      <p className="font-medium text-xs">
                        {subscription.pendingPlanType} plan waiting for checkout
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-yellow-600">Cancels on</p>
                      <p className="font-medium">
                        {formatDate(subscription.currentPeriodEnd!)}
                      </p>
                    </>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="space-y-2 pt-2">
        <div className="flex gap-2">
          {isFreePlan ? (
            // Free plan users only see upgrade button
            <Button
              onClick={handleUpgrade}
              className="flex-1"
              size={buttonSize}
              variant={buttonVariant}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          ) : isScheduledToCancel ? (
            // Subscription is scheduled to cancel (e.g., ACTIVE but cancelAtPeriodEnd is true)
            hasPendingPlanChange ? (
              // Has pending plan change - show reactivate to cancel the plan change
              <>
                <ReactivateButton
                  variant={
                    buttonVariant as
                      | "default"
                      | "outline"
                      | "secondary"
                      | undefined
                  }
                  size={buttonSize ?? undefined}
                  className="flex-1"
                />
                <Button
                  onClick={handleUpgrade}
                  className="flex-1"
                  size={buttonSize}
                  variant="outline"
                >
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Complete Checkout
                </Button>
              </>
            ) : (
              // Regular cancellation - show reactivate and change plan
              <>
                <ReactivateButton
                  variant={
                    buttonVariant as
                      | "default"
                      | "outline"
                      | "secondary"
                      | undefined
                  }
                  size={buttonSize ?? undefined}
                  className="flex-1"
                />
                <ChangePlanDialog
                  size={buttonSize ?? undefined}
                  className="flex-1"
                  variant={
                    buttonVariant as
                      | "default"
                      | "outline"
                      | "secondary"
                      | undefined
                  }
                />
              </>
            )
          ) : isActive ? (
            // Active and NOT scheduled to cancel
            <>
              <ChangePlanDialog
                size={buttonSize ?? undefined}
                className="flex-1"
                variant={
                  buttonVariant as
                    | "default"
                    | "outline"
                    | "secondary"
                    | undefined
                }
              />
              <CancelSubscriptionButton
                variant="destructive"
                size={buttonSize ?? undefined}
                className="flex-1"
              />
            </>
          ) : isActuallyCancelled ? (
            // Status is CANCELLED (and not previously caught by isScheduledToCancel if it was within billing period but status already CANCELLED)
            // This typically means the subscription is fully terminated and past its period end.
            <Button
              onClick={handleUpgrade}
              className="flex-1"
              size={buttonSize}
              variant={buttonVariant}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Subscribe Again
            </Button>
          ) : (
            // Fallback for other non-free, non-active, non-cancelled, non-scheduled-to-cancel states
            // (e.g., PENDING, ON_HOLD). Offer upgrade.
            <Button
              onClick={handleUpgrade}
              className="flex-1"
              size={buttonSize}
              variant={buttonVariant}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Upgrade Plan
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
