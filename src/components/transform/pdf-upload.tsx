"use client";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { formatBytes } from "@/lib/utils";
import { useState, useCallback } from "react";
import { FileText } from "lucide-react";

// Increase max file size to 25MB
const MAX_FILE_SIZE = 25 * 1024 * 1024;
const MAX_FILE_SIZE_FORMATTED = "25MB";

interface PdfUploadProps {
  isPdfLoading: boolean;
  selectedFile: File | null;
  onFileSelect: (file: File | null) => void;
  progress?: number; // Add progress prop for showing percentage
}

export function PdfUpload({
  isPdfLoading,
  selectedFile,
  onFileSelect,
  progress = 0,
}: PdfUploadProps) {
  const [isDragging, setIsDragging] = useState(false);

  // Handle file validation and selection
  const handleFileSelection = useCallback(
    (file: File) => {
      if (file.size > MAX_FILE_SIZE) {
        toast.error(`File size must be less than ${MAX_FILE_SIZE_FORMATTED}`);
        return;
      }

      if (!file.type.includes("pdf")) {
        toast.error("Only PDF files are supported");
        return;
      }

      onFileSelect(file);
    },
    [onFileSelect]
  );

  // Drag and drop event handlers
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        const file = e.dataTransfer.files[0];
        handleFileSelection(file);
      }
    },
    [handleFileSelection]
  );

  return (
    <div
      className={`flex flex-col items-center justify-center p-8 border-2 border-dashed rounded-lg ${
        isDragging
          ? "border-primary bg-primary/5"
          : "border-muted-foreground/20"
      } h-[200px] sm:h-[220px] transition-colors duration-200`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        type="file"
        id="pdf-upload"
        accept=".pdf"
        className="hidden"
        onChange={(e) => {
          if (e.target.files && e.target.files[0]) {
            handleFileSelection(e.target.files[0]);
          }
        }}
        disabled={isPdfLoading}
      />

      {!selectedFile ? (
        <>
          <FileText
            className={`w-10 h-10 mb-2 ${
              isDragging ? "text-primary" : "text-muted-foreground"
            }`}
          />
          <h3 className="text-lg font-medium mb-2">PDF Transformation</h3>
          <p className="text-muted-foreground text-center mb-4">
            {isDragging ? (
              <span className="text-primary font-medium">
                Drop your PDF file here
              </span>
            ) : (
              <>
                Upload and transform PDF documents into summaries
                <br />
                <span className="text-xs">
                  (Maximum file size: {MAX_FILE_SIZE_FORMATTED})
                </span>
                <br />
                <span className="text-xs italic mt-1 block">
                  Drag & drop your PDF file here or click the button below
                </span>
              </>
            )}
          </p>
          <Button
            variant="outline"
            onClick={() => document.getElementById("pdf-upload")?.click()}
            disabled={isPdfLoading}
          >
            Select PDF
          </Button>
        </>
      ) : (
        <>
          <h3 className="text-lg font-medium mb-2">PDF Selected</h3>

          <div className="flex flex-col items-center mb-4">
            <p className="font-medium text-center">{selectedFile.name}</p>
            <p className="text-sm text-muted-foreground">
              {formatBytes(selectedFile.size)}
            </p>
          </div>

          {isPdfLoading ? (
            <div className="flex flex-col items-center">
              <div className="h-1 w-full bg-muted rounded-full overflow-hidden mb-2">
                <div
                  className="h-full bg-primary transition-all duration-300"
                  style={{
                    width: `${Math.max(5, Math.min(100, progress * 100))}%`,
                  }}
                ></div>
              </div>
              <p className="text-sm text-muted-foreground">
                <span className="inline-block">
                  Processing PDF...{" "}
                  {progress > 0 ? `${Math.round(progress * 100)}%` : ""}
                </span>
              </p>
            </div>
          ) : (
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  onFileSelect(null);
                  // Reset the file input
                  const fileInput = document.getElementById(
                    "pdf-upload"
                  ) as HTMLInputElement;
                  if (fileInput) fileInput.value = "";
                }}
              >
                Remove
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById("pdf-upload")?.click()}
              >
                Change File
              </Button>
            </div>
          )}
        </>
      )}
    </div>
  );
}
