"use client";

import type React from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { nanoid } from "nanoid";
import { getVideoId } from "@/utils/youtube";
import { useAuthStore } from "@/store/authStore";
import { useTextStore } from "@/store/textStore";
import { usePdfStore } from "@/store/pdfStore";

import { smartPdfTextExtraction } from "@/lib/ocr-extraction-cdn";
import { Youtube, FileText, BookOpen, Globe } from "lucide-react";
import { YouTubeInput } from "./youtube-input";
import { PdfUpload } from "./pdf-upload";
import { OutputTypeSelector } from "./output-type-selector";
import { TransformButton } from "./transform-button";
import { TextInput } from "./text-input";
import { WebpageInput } from "./webpage-input";
import { useUsageLimit } from "@/hooks/useUsageLimit";
import UsageLimitModal from "@/components/common/UsageLimitModal";
import { FeatureType } from "@/types/subscription";

type OutputType = "summary" | "notes";
type InputType = "youtube" | "pdf" | "text" | "url";

const outputOptions = [
  {
    value: "summary" as OutputType,
    label: "Summary",
    icon: FileText,
    description: "A concise overview of the key points",
  },
  {
    value: "notes" as OutputType,
    label: "Notes",
    icon: BookOpen,
    description: "Detailed, structured notes for study",
  },
];

export function TransformForm() {
  const router = useRouter();
  const { user } = useAuthStore();
  const { setText } = useTextStore();
  const { setPdfBuffer, setPdfName, setPageCount, setIsOcr } = usePdfStore();
  const {
    isModalOpen,
    feature,
    usageData,
    planType,
    isChecking,
    checkUsageLimit,
    closeModal,
  } = useUsageLimit();

  const [inputUrl, setInputUrl] = useState("");
  const [webUrl, setWebUrl] = useState("");
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPdfLoading, setIsPdfLoading] = useState(false);
  const [pdfProgress, setPdfProgress] = useState(0);
  const [isWebLoading, setIsWebLoading] = useState(false);
  const [outputType, setOutputType] = useState<OutputType>("summary");
  const [activeTab, setActiveTab] = useState<InputType>("youtube");
  const [selectedPdfFile, setSelectedPdfFile] = useState<File | null>(null);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!user) {
      toast.error("Please sign in to transform content");
      router.push("/auth");
      return;
    }

    if (activeTab === "youtube") {
      if (!inputUrl) {
        toast.error("Please enter a YouTube URL");
        return;
      }

      const videoId = getVideoId(inputUrl);
      if (!videoId) {
        toast.error("Invalid YouTube URL");
        return;
      }

      // Check usage limit before processing
      const canProceed = await checkUsageLimit(FeatureType.TRANSFORMATIONS);
      if (!canProceed) {
        return;
      }

      setIsLoading(true);
      toast.info("Analyzing video...");

      try {
        // Use the efficient content lookup API to check if content exists
        const lookupResponse = await fetch(`/api/content-lookup`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            sourceId: videoId,
            contentType: "youtube",
          }),
        });

        const lookupData = await lookupResponse.json();

        // If we already have this content, redirect to it
        if (lookupData.found && lookupData.content) {
          router.push(
            `/transform/${lookupData.content.contentId}?type=youtube&output=${outputType}`
          );
          return;
        }

        // Generate transcript
        toast.info(
          "Extracting transcript from video. This may take a moment...",
          {
            duration: 1000,
          }
        );

        const transcriptResponse = await fetch("/api/transcript", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            url: `https://www.youtube.com/watch?v=${videoId}`,
            contentType: "youtube",
          }),
        });

        if (!transcriptResponse.ok) {
          const errorData = await transcriptResponse.json();
          throw new Error(errorData.error || "Failed to fetch transcript");
        }

        const transcriptData = await transcriptResponse.json();

        // Check if transcript is available
        if (
          !transcriptData.transcript ||
          transcriptData.transcript.length === 0 ||
          !transcriptData.metadata.hasTranscript
        ) {
          toast.error("Unable to process this video. Please try another one.");
          setIsLoading(false);
          return;
        }

        // Now redirect to transform page
        router.push(`/transform/${videoId}?type=youtube&output=${outputType}`);
      } catch (error) {
        console.error("YouTube processing error:", error);
        toast.error(
          error instanceof Error
            ? error.message
            : "Something went wrong. Please try again."
        );
        setIsLoading(false);
      }
    } else if (activeTab === "text") {
      if (!inputText || inputText.trim().length === 0) {
        toast.error("Please enter some text to transform");
        return;
      }

      // Check usage limit before processing
      const canProceed = await checkUsageLimit(FeatureType.TRANSFORMATIONS);
      if (!canProceed) {
        return;
      }

      setIsLoading(true);

      try {
        setText(inputText);
        setOutputType(outputType);

        const response = await fetch("/api/text", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            text: inputText,
            outputType,
          }),
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || "Failed to process text");
        }

        const data = await response.json();
        router.push(
          `/transform/${data.contentId}?type=text&output=${outputType}`
        );
      } catch (error) {
        console.error("Text transformation error:", error);
        toast.error(
          error instanceof Error ? error.message : "Failed to process text"
        );
        setIsLoading(false);
      }
    } else if (activeTab === "url") {
      if (!webUrl || !webUrl.trim().startsWith("http")) {
        toast.error(
          "Please enter a valid URL starting with http:// or https://"
        );
        return;
      }

      // Check usage limit before processing
      const canProceed = await checkUsageLimit(FeatureType.TRANSFORMATIONS);
      if (!canProceed) {
        return;
      }

      setIsWebLoading(true);
      handleWebUrlExtraction(webUrl);
    } else if (activeTab === "pdf" && selectedPdfFile) {
      // Check usage limit before processing
      const canProceed = await checkUsageLimit(FeatureType.TRANSFORMATIONS);
      if (!canProceed) {
        return;
      }

      setIsPdfLoading(true);
      handlePdfUpload(selectedPdfFile);
    }
  };

  const handlePdfFileSelect = (file: File | null) => {
    setSelectedPdfFile(file);
  };

  const handlePdfUpload = async (file: File) => {
    if (!user) {
      toast.error("Please sign in to transform content");
      router.push("/auth");
      return;
    }

    setIsPdfLoading(true);

    try {
      // Reset progress
      setPdfProgress(0.05);

      console.log("Starting PDF extraction...");

      // Use smart extraction that tries regular extraction first, then falls back to OCR if needed
      const { text: extractedText, usedOcr } = await smartPdfTextExtraction(
        file,
        (progress) => {
          // Update progress state to show in the UI
          setPdfProgress(progress);
          console.log(
            `PDF extraction progress: ${Math.round(progress * 100)}%`
          );
        }
      );

      console.log(`PDF extraction complete. OCR used: ${usedOcr}`);
      setPdfProgress(0.95); // Almost done

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error("Unable to extract text from this PDF");
      }

      // If OCR was used, show a notification
      if (usedOcr) {
        toast.success("Advanced text recognition was used for this PDF", {
          duration: 5000,
        });
      }

      const contentId = nanoid(10);

      setPdfBuffer(await file.arrayBuffer());
      setPdfName(file.name);
      setPageCount(Math.ceil(extractedText.length / 3000));
      setIsOcr(usedOcr); // Store whether OCR was used
      setOutputType(outputType);

      // Final step - saving to database
      setPdfProgress(0.98);

      // Add a size check before sending to API
      if (extractedText.length > 10 * 1024 * 1024) {
        // 10MB text limit
        throw new Error(
          "PDF content is too large to process. Please try a smaller file."
        );
      }

      try {
        // Validate the extracted text to ensure it's a valid string
        if (typeof extractedText !== "string") {
          throw new Error("Invalid PDF content format");
        }

        // Ensure the text is properly sanitized to avoid JSON parsing issues
        const sanitizedText = extractedText
          .replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/g, "") // Remove control chars
          .replace(/\uFFFD/g, ""); // Remove replacement character

        console.log(
          `Sending PDF content to API, length: ${sanitizedText.length}`
        );

        const response = await fetch("/api/pdf", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            contentId,
            fileName: file.name,
            text: sanitizedText,
            outputType,
            usedOcr,
          }),
        });

        // Complete the progress
        setPdfProgress(1.0);

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to save PDF information");
        }

        // Wait a moment to ensure the PDF data is saved before redirecting
        await new Promise((resolve) => setTimeout(resolve, 500));

        router.push(`/transform/${contentId}?type=pdf&output=${outputType}`);
      } catch (error) {
        console.error("Error saving PDF to database:", error);
        if (error instanceof Error) {
          if (error.message.includes("JSON")) {
            throw new Error(
              "The PDF content couldn't be processed. It may be too large or contain unsupported formatting."
            );
          } else if (error.message.includes("Invalid array length")) {
            throw new Error(
              "The PDF content contains invalid data. Please try a different PDF file."
            );
          } else {
            throw error; // Re-throw for the outer catch block
          }
        } else {
          throw error; // Re-throw for the outer catch block
        }
      }
    } catch (error) {
      console.error("PDF transformation error:", error);

      // Show user-friendly error message based on error type
      if (error instanceof Error) {
        if (error.message.includes("Unable to extract text")) {
          toast.error(
            "We couldn't extract text from this PDF. It may be password-protected or contain only images."
          );
        } else if (error.message.includes("too large")) {
          toast.error(
            "This PDF is too large to process. Please try a smaller file or break it into parts."
          );
        } else if (error.message.includes("JSON")) {
          toast.error(
            "There was a problem processing your PDF. The content may be too complex or contain unsupported formatting."
          );
        } else if (
          error.message.includes("timeout") ||
          error.message.includes("timed out")
        ) {
          toast.error(
            "The PDF processing timed out. Please try a smaller file or break it into parts."
          );
        } else if (
          error.message.includes("Invalid array length") ||
          error.message.includes("invalid data")
        ) {
          toast.error(
            "The PDF contains invalid data that couldn't be processed. Please try a different PDF file."
          );
        } else {
          toast.error(
            "There was a problem processing your PDF. Please try again."
          );
        }
      } else {
        toast.error(
          "There was a problem processing your PDF. Please try again."
        );
      }
    } finally {
      setIsPdfLoading(false);
      setPdfProgress(0); // Reset progress
    }
  };

  const handleWebUrlExtraction = async (url: string) => {
    if (!user) {
      toast.error("Please sign in to transform content");
      router.push("/auth");
      return;
    }

    setIsWebLoading(true);
    toast.info("Extracting content from webpage. This may take a moment...", {
      duration: 3000,
    });

    try {
      // Use the efficient content lookup API to check if content exists
      const lookupResponse = await fetch(`/api/content-lookup`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          url,
          contentType: "WEBPAGE",
        }),
      });

      const lookupData = await lookupResponse.json();

      // If we already have this content, redirect to it
      if (lookupData.found && lookupData.content) {
        router.push(
          `/transform/${lookupData.content.contentId}?type=webpage&output=${outputType}`
        );
        return;
      }

      const response = await fetch("/api/extract-url", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ url }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to extract content from URL"
        );
      }

      const data = await response.json();

      if (!data.content || data.content.trim().length === 0) {
        throw new Error("Could not extract content from the webpage");
      }

      const contentId = nanoid(10);

      const registerResponse = await fetch("/api/webpage", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          contentId,
          url,
          title: data.title || "Webpage Content",
          content: data.content,
          author: data.author,
          date: data.date,
          image: data.image,
          outputType,
        }),
      });

      if (!registerResponse.ok) {
        const errorData = await registerResponse.json();
        throw new Error(
          errorData.error || "Failed to register webpage content"
        );
      }

      router.push(`/transform/${contentId}?type=webpage&output=${outputType}`);
    } catch (error) {
      console.error("Webpage extraction error:", error);
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to extract webpage content"
      );
    } finally {
      setIsWebLoading(false);
    }
  };

  const isTransformDisabled =
    (activeTab === "youtube" && !inputUrl) ||
    (activeTab === "text" && !inputText.trim()) ||
    (activeTab === "url" && (!webUrl || !webUrl.trim().startsWith("http"))) ||
    (activeTab === "pdf" && !selectedPdfFile);

  return (
    <Card className="overflow-hidden border border-border/40 shadow-lg">
      <div className="p-4 sm:p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs
            value={activeTab}
            onValueChange={(value) => setActiveTab(value as InputType)}
          >
            <TabsList className="grid grid-cols-4 mb-6 bg-muted/50 p-1">
              <TabsTrigger
                value="youtube"
                className="flex items-center justify-center gap-2"
              >
                <Youtube className="h-4 w-4" />
                <span className="hidden sm:inline">YouTube</span>
              </TabsTrigger>
              <TabsTrigger
                value="pdf"
                className="flex items-center justify-center gap-2"
              >
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">PDF</span>
              </TabsTrigger>
              <TabsTrigger
                value="url"
                className="flex items-center justify-center gap-2"
              >
                <Globe className="h-4 w-4" />
                <span className="hidden sm:inline">Webpage</span>
              </TabsTrigger>
              <TabsTrigger
                value="text"
                className="flex items-center justify-center gap-2"
              >
                <BookOpen className="h-4 w-4" />
                <span className="hidden sm:inline">Text</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="youtube" className="space-y-4 mt-0">
              <YouTubeInput inputUrl={inputUrl} setInputUrl={setInputUrl} />
            </TabsContent>

            <TabsContent value="pdf" className="mt-0">
              <PdfUpload
                isPdfLoading={isPdfLoading}
                selectedFile={selectedPdfFile}
                onFileSelect={handlePdfFileSelect}
                progress={pdfProgress}
              />
            </TabsContent>

            <TabsContent value="url" className="mt-0">
              <WebpageInput webUrl={webUrl} setWebUrl={setWebUrl} />
            </TabsContent>

            <TabsContent value="text" className="mt-0">
              <TextInput inputText={inputText} setInputText={setInputText} />
            </TabsContent>

            <div className="flex flex-col sm:flex-row gap-4 mt-6">
              <div className="w-full sm:w-1/3">
                <OutputTypeSelector
                  outputType={outputType}
                  setOutputType={setOutputType}
                  options={outputOptions}
                />
              </div>

              <div className="w-full sm:w-2/3">
                <TransformButton
                  isLoading={
                    isLoading || isWebLoading || isPdfLoading || isChecking
                  }
                  isDisabled={isTransformDisabled}
                  isChecking={isChecking}
                />
              </div>
            </div>
          </Tabs>
        </form>
      </div>
      <UsageLimitModal
        isOpen={isModalOpen}
        onClose={closeModal}
        feature={feature}
        usageData={usageData}
        planType={planType}
      />
    </Card>
  );
}
