"use client";

import { But<PERSON> } from "@/components/ui/button";

interface TransformButtonProps {
  isLoading: boolean;
  isDisabled: boolean;
  canTransform?: boolean;
  isChecking?: boolean;
  onClick?: () => void;
}

export function TransformButton({
  isLoading,
  isDisabled,
  isChecking = false,
  onClick,
}: TransformButtonProps) {
  return (
    <Button
      type={onClick ? "button" : "submit"}
      onClick={onClick}
      disabled={isLoading || isDisabled}
      className="h-12 w-full cursor-pointer"
    >
      {isLoading && !isChecking
        ? "Transforming..."
        : isChecking
        ? "Checking Usage..."
        : "Transform"}
    </Button>
  );
}
