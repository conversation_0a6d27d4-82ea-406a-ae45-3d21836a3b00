"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Menu,
  PlusSquare,
  Home,
  Wand2,
  UserCircle2,
  Moon,
  SunMedium,
  ChevronDown,
  History,
  Loader2,
  ArrowRight,
  LogIn,
  PanelLeftClose,
  MessageSquareQuote,
  LogOut,
  Sparkles,
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useSidebarStore } from "@/store/sidebarStore";
import { useRecentTransformationsStore } from "@/store/recentTransformationsStore";
import Link from "next/link";
import { useAuthStore } from "@/store/authStore";
import { useSubscriptionStore } from "@/store/subscriptionStore";
import { usePathname } from "next/navigation";
import { useTheme } from "next-themes";
import { cn } from "@/lib/utils";
import React, { useState, useEffect } from "react";
import { SignOutModal } from "@/components/common/SignOutModal";
import Image from "next/image";

const noFocusRing =
  "focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none";

interface NavItemProps {
  href: string;
  icon: React.ElementType;
  label: string;
  currentPath: string;
  onClick: () => void;
  isProFeature?: boolean;
  isFreePlan?: boolean;
  isActiveCustom?: (path: string) => boolean;
}

const NavButton: React.FC<NavItemProps> = ({
  href,
  icon: Icon,
  label,
  currentPath,
  onClick,
  isProFeature,
  isFreePlan,
  isActiveCustom,
}) => {
  const isActive = isActiveCustom ? isActiveCustom(href) : currentPath === href;

  const activeClasses =
    "bg-primary/10 dark:bg-primary/20 text-primary font-semibold relative before:content-[''] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-5 before:w-1 before:bg-primary before:rounded-r-md";

  const hoverClasses = "hover:bg-muted/60 dark:hover:bg-muted/50";

  if (isProFeature) {
    return (
      <div className="relative mt-2">
        <Button
          variant={isActive ? "secondary" : "default"}
          className={cn(
            "justify-start gap-3 w-full h-12 text-base",
            "bg-gradient-to-r from-blue-500 to-violet-600 hover:from-blue-600 hover:to-violet-700 text-white",
            noFocusRing,
            isActive &&
              "ring-2 ring-primary/70 ring-offset-2 ring-offset-background"
          )}
          asChild
          onClick={onClick}
        >
          <Link href={href}>
            <Icon className="h-5 w-5" /> {/* Consistent icon size */}
            <span>{label}</span>
          </Link>
        </Button>
        {isFreePlan && (
          <div className="absolute -top-2.5 -right-1 bg-red-500 text-white text-xs font-semibold px-2 py-0.5 rounded-full shadow-md">
            20% OFF
          </div>
        )}
      </div>
    );
  }

  return (
    <Button
      variant="ghost"
      className={cn(
        "justify-start gap-3 w-full h-12 text-base",
        noFocusRing,
        isActive ? activeClasses : hoverClasses,
        !isActive && "text-muted-foreground hover:text-foreground"
      )}
      asChild
      onClick={onClick}
    >
      <Link href={href}>
        <Icon className="h-5 w-5" /> {/* Consistent icon size */}
        <span>{label}</span>
      </Link>
    </Button>
  );
};

export default function AppSidebar() {
  const { isLeftSidebarOpen, toggleLeftSidebar } = useSidebarStore();
  const {
    recentContent,
    isLoading: isRecentContentLoading,
    startPolling,
    stopPolling,
  } = useRecentTransformationsStore();

  const { user } = useAuthStore();
  const { subscription, fetchSubscription } = useSubscriptionStore();

  const isFreePlan =
    !subscription ||
    subscription.planType === "Free" ||
    !subscription.status ||
    (subscription.status !== "ACTIVE" && subscription.status !== "NON_RENEWING");

  React.useEffect(() => {
    if (user) {
      fetchSubscription(true);
    }
  }, [fetchSubscription, user]);

  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const [showSignOutModal, setShowSignOutModal] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    if (user) {
      startPolling();
    } else {
      stopPolling();
    }
    return () => {
      stopPolling();
    };
  }, [user, startPolling, stopPolling]);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };
    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    const setVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    };
    setVh();
    window.addEventListener("resize", setVh);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
      window.removeEventListener("resize", setVh);
    };
  }, []);

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const truncateTitle = (title: string, maxLength: number) => {
    let cleanTitle = title;
    if (title.startsWith("##") || title.startsWith("**")) {
      cleanTitle = title
        .replace(/^(##|\*\*)\s*/, "")
        .replace(/(\*\*)$/, "")
        .trim();
    }
    return cleanTitle.length > maxLength
      ? cleanTitle.substring(0, maxLength) + "..."
      : cleanTitle;
  };

  const handleMobileItemClick = () => {
    if (isMobileView) {
      toggleLeftSidebar();
    }
  };

  const handleMenuDropdownItemClick = () => {
    setIsDropdownOpen(false);
    if (isMobileView) {
      toggleLeftSidebar();
    }
  };

  const contentLimit = 4;

  const mainNavItems = [
    { href: "/new", icon: PlusSquare, label: "New Content" },
    { href: "/dashboard", icon: Home, label: "Dashboard" },
    { href: "/transformations", icon: Wand2, label: "My Creations" },
  ];

  if (user) {
    mainNavItems.push({
      href: "/account",
      icon: UserCircle2,
      label: "My Account",
    });
  }

  return (
    <>
      <div
        style={{ height: "calc(var(--vh, 1vh) * 100)" }}
        className={cn(
          "fixed left-0 top-0 bottom-0 bg-background border-r z-40",
          "w-[280px] transition-transform duration-300 flex flex-col",
          "h-screen",
          isLeftSidebarOpen ? "translate-x-0" : "-translate-x-full",
          "shadow-lg dark:shadow-slate-800/50"
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between h-16 px-4 border-b shrink-0">
            {/* FIX: Removed gap-1.5 for closer logo image and text */}
            <Link
              href="/"
              className="flex items-center gap-0"
              onClick={handleMobileItemClick}
            >
              <Image
                src="https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WFITvZJhI9LXTfxC5Ad4lp1WocqmwHirgGPav" // Replace with your actual logo URL
                alt="Qlipify logo"
                width={36}
                height={36}
                unoptimized
                className="rounded-md"
              />
              <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-violet-500 -ml-2">
                {" "}
                {/* Added ml-1 for slight separation if needed */}
                lipify
              </span>
            </Link>
            {/* FIX: Removed lg:hidden to show toggle button on desktop */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleLeftSidebar}
              className={cn(noFocusRing)}
            >
              <PanelLeftClose size={24} />
            </Button>
          </div>

          {/* Scrollable content area */}
          {/* FIX: Reverted padding to p-2 and adjusted spacing for content sections */}
          <div className="flex flex-col overflow-y-auto flex-1 p-2">
            {/* Main Navigation */}
            {/* FIX: Reverted nav space-y to space-y-1 */}
            <nav className="space-y-1">
              {mainNavItems.map((item) => (
                <NavButton
                  key={item.href}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  currentPath={pathname}
                  onClick={handleMobileItemClick}
                />
              ))}
              {/* Upgrade Button - using NavButton component for consistency */}
              {isFreePlan && user && (
                <NavButton
                  href="/upgrade"
                  icon={Sparkles}
                  label="Upgrade to Pro"
                  currentPath={pathname}
                  onClick={handleMobileItemClick}
                  isProFeature={true}
                  isFreePlan={isFreePlan}
                />
              )}
            </nav>

            {/* Recent Section */}
            {user && recentContent && recentContent.length > 0 && (
              <div className="pt-3 mt-2">
                <div className="px-3 py-2 mb-1">
                  <h3 className="text-sm font-semibold text-muted-foreground tracking-wide uppercase flex items-center gap-2">
                    <History className="h-4 w-4" />
                    Recent Creations
                  </h3>
                </div>

                <div className="space-y-1">
                  {isRecentContentLoading && recentContent.length === 0 ? (
                    <div className="flex items-center justify-center py-4">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  ) : (
                    recentContent.slice(0, contentLimit).map((item) => (
                      <Button
                        key={item.id}
                        variant="ghost"
                        className={cn(
                          "w-full justify-start h-10 text-left pl-4 pr-2 py-2",
                          "text-sm font-normal text-muted-foreground hover:text-foreground hover:bg-muted/60 dark:hover:bg-muted/50 rounded-md",
                          noFocusRing,
                          pathname === `/transform/${item.contentId}` &&
                            "bg-muted dark:bg-muted/70 text-foreground font-medium"
                        )}
                        asChild
                        onClick={handleMobileItemClick}
                      >
                        <Link
                          href={`/transform/${
                            item.contentId
                          }?type=${item.contentType.toLowerCase()}&output=${
                            item.outputType
                          }`}
                          className="truncate"
                        >
                          {truncateTitle(item.title, 28)}
                        </Link>
                      </Button>
                    ))
                  )}
                </div>

                <div className="mt-2">
                  <Button
                    variant="ghost"
                    className={cn(
                      "justify-start gap-2 w-full h-10 text-sm",
                      "text-muted-foreground hover:text-primary hover:bg-transparent dark:hover:bg-transparent font-medium",
                      noFocusRing
                    )}
                    asChild
                    onClick={handleMobileItemClick}
                  >
                    <Link
                      href="/transformations"
                      className="flex items-center px-2"
                    >
                      <span>View All Creations</span>
                      <ArrowRight className="h-4 w-4 " />
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* User profile section */}
          <div className="shrink-0 mt-auto border-t p-2">
            {user ? (
              <DropdownMenu
                open={isDropdownOpen}
                onOpenChange={setIsDropdownOpen}
              >
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className={cn(
                      "flex items-center justify-between w-full gap-2 px-3 py-3 h-auto rounded-md",
                      "hover:bg-muted/70 dark:hover:bg-muted/50",
                      noFocusRing
                    )}
                  >
                    <div className="flex items-center gap-3 truncate">
                      <Avatar className="h-9 w-9 border">
                        <AvatarImage src={user.user_metadata?.avatar_url} />
                        <AvatarFallback className="text-sm font-medium bg-muted text-muted-foreground">
                          {user.user_metadata?.full_name?.[0]?.toUpperCase() ||
                            "?"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col items-start truncate">
                        <span className="text-sm font-medium text-foreground truncate">
                          {user.user_metadata?.full_name || "User"}
                        </span>
                        <span className="text-xs text-muted-foreground truncate">
                          {user.email || "<EMAIL>"}
                        </span>
                      </div>
                    </div>
                    <ChevronDown className="h-4 w-4 text-muted-foreground shrink-0" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  side="top"
                  align="start"
                  className="w-[270px] mb-1 p-1 bg-popover text-popover-foreground border shadow-xl"
                  onCloseAutoFocus={(e) => e.preventDefault()}
                >
                  <div className="flex items-center gap-3 p-3 border-b mb-1">
                    <Avatar className="h-10 w-10 border">
                      <AvatarImage src={user.user_metadata?.avatar_url} />
                      <AvatarFallback className="text-base font-medium bg-muted text-muted-foreground">
                        {user.user_metadata?.full_name?.[0]?.toUpperCase() ||
                          "?"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex flex-col truncate">
                      <span className="text-sm font-semibold text-foreground truncate">
                        {user.user_metadata?.full_name || "User"}
                      </span>
                      <span className="text-xs text-muted-foreground truncate">
                        {user.email || "<EMAIL>"}
                      </span>
                    </div>
                  </div>

                  {[
                    {
                      href: "/account",
                      icon: UserCircle2,
                      label: "My Account",
                    },
                    {
                      href: "/feedback",
                      icon: MessageSquareQuote,
                      label: "Send Feedback",
                    },
                  ].map((item) => (
                    <DropdownMenuItem
                      key={item.label}
                      className="py-2.5 px-3 hover:bg-muted focus:bg-muted rounded-md cursor-pointer text-sm"
                      onClick={handleMenuDropdownItemClick}
                      asChild
                    >
                      <Link
                        href={item.href}
                        className="flex items-center gap-2.5 w-full"
                      >
                        <item.icon className="h-5 w-5 text-muted-foreground" />
                        <span>{item.label}</span>
                      </Link>
                    </DropdownMenuItem>
                  ))}

                  <DropdownMenuItem
                    className="py-2.5 px-3 hover:bg-muted focus:bg-muted rounded-md cursor-pointer text-sm"
                    onClick={() => {
                      toggleTheme();
                      handleMenuDropdownItemClick();
                    }}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2.5">
                        {theme === "dark" ? (
                          <>
                            <SunMedium className="h-5 w-5 text-yellow-500" />
                            <span>Light Mode</span>
                          </>
                        ) : (
                          <>
                            <Moon className="h-5 w-5 text-sky-500" />
                            <span>Dark Mode</span>
                          </>
                        )}
                      </div>
                      <div
                        className={`w-9 h-5 flex items-center rounded-full p-0.5 transition-colors ${
                          theme === "dark" ? "bg-primary/30" : "bg-muted"
                        }`}
                      >
                        <div
                          className={`w-4 h-4 rounded-full bg-background shadow-md transform transition-transform ${
                            theme === "dark" ? "translate-x-4" : "translate-x-0"
                          }`}
                        />
                      </div>
                    </div>
                  </DropdownMenuItem>

                  <DropdownMenuSeparator className="my-1" />

                  <DropdownMenuItem
                    className="py-2.5 px-3 hover:bg-destructive/10 focus:bg-destructive/10 rounded-md cursor-pointer text-sm group"
                    onClick={() => {
                      setShowSignOutModal(true);
                      handleMenuDropdownItemClick();
                    }}
                  >
                    <div className="flex items-center gap-2.5 w-full text-destructive group-hover:text-destructive-foreground">
                      <LogOut className="h-5 w-5" />
                      <span>Log out</span>
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="p-2">
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-center gap-2 h-11 text-base",
                    noFocusRing,
                    "hover:bg-primary/5 dark:hover:bg-primary/10 hover:border-primary"
                  )}
                  asChild
                  onClick={handleMobileItemClick}
                >
                  <Link href="/auth">
                    <LogIn className="h-5 w-5" />
                    <span>Sign In</span>
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile overlay backdrop */}
      {isLeftSidebarOpen && isMobileView && (
        <div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-30 lg:hidden"
          onClick={toggleLeftSidebar}
        />
      )}

      {/* Hamburger menu button when sidebar is closed */}
      {/* FIX: Removed lg:hidden to show on desktop as well when sidebar is closed */}
      {!isLeftSidebarOpen && (
        <div className="fixed left-2 top-2 z-50">
          <Button
            variant="outline"
            size="icon"
            onClick={toggleLeftSidebar}
            className={cn(
              "bg-background/80 backdrop-blur-md shadow-lg hover:bg-accent h-10 w-10 rounded-full",
              noFocusRing
            )}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      )}

      <SignOutModal
        open={showSignOutModal}
        onOpenChange={setShowSignOutModal}
      />
    </>
  );
}
