"use client";

import { useEffect, useState, memo } from "react";
import { usePathname } from "next/navigation";
import { useAuthStore } from "@/store/authStore";
import AppSidebar from "./AppSidebar";
import { useSidebarStore } from "@/store/sidebarStore";
import Navbar from "@/components/home/<USER>";
import Footer from "@/components/home/<USER>";
import { PUBLIC_ROUTES_ARRAY } from "@/utils/routes";

// Memoize the layout components to prevent unnecessary re-renders
const PublicLayout = memo(({ children }: { children: React.ReactNode }) => (
  <div className="min-h-screen flex flex-col">
    <Navbar />
    <main className="flex-1">{children}</main>
    <Footer />
  </div>
));
PublicLayout.displayName = "PublicLayout";

const AuthLayout = memo(({ children }: { children: React.ReactNode }) => (
  <>{children}</>
));
AuthLayout.displayName = "AuthLayout";

const ProtectedLayout = memo(
  ({
    children,
    isLeftSidebarOpen,
  }: {
    children: React.ReactNode;
    isLeftSidebarOpen: boolean;
  }) => (
    <div className="flex h-[100dvh] overflow-hidden">
      <AppSidebar />
      <div
        className={`flex-1 overflow-y-auto transition-all duration-300 h-full relative ${
          isLeftSidebarOpen ? "md:ml-[280px]" : "ml-0"
        }`}
      >
        {children}
      </div>
    </div>
  )
);
ProtectedLayout.displayName = "ProtectedLayout";

// Use public routes from centralized routes file
const PUBLIC_ROUTES = PUBLIC_ROUTES_ARRAY;

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { user } = useAuthStore();
  const { isLeftSidebarOpen } = useSidebarStore();
  const [isClient, setIsClient] = useState(false);

  // Optimize route checking with a single function
  const getRouteType = () => {
    if (!pathname)
      return { isPublicRoute: true, isSharePage: false, isAuthPage: false };

    const isAuthPage = pathname.startsWith("/auth");
    const isSharePage = pathname.startsWith("/share");

    // Check if current path is in PUBLIC_ROUTES
    const isPublicRoute = PUBLIC_ROUTES.some(
      (route) =>
        pathname === route || (route !== "/" && pathname.startsWith(route))
    );

    return { isPublicRoute, isSharePage, isAuthPage };
  };

  const { isPublicRoute, isSharePage, isAuthPage } = getRouteType();
  const isProtectedRoute = !isPublicRoute;

  // Client detection - only run once
  useEffect(() => {
    setIsClient(true);
  }, []);

  // If it's a protected route and user is not authenticated, or
  // if it's an auth page and user is authenticated,
  // don't render anything (middleware will handle the redirect)
  if (isClient && ((isProtectedRoute && !user) || (isAuthPage && user))) {
    return null;
  }

  // For public routes (except share page), render with Navbar and Footer
  if (isPublicRoute && !isSharePage && !isAuthPage) {
    return <PublicLayout>{children}</PublicLayout>;
  }

  // For auth pages, render without any layout
  if (isAuthPage) {
    return <AuthLayout>{children}</AuthLayout>;
  }

  // For all other routes (including share and protected routes), render with sidebar
  return (
    <ProtectedLayout isLeftSidebarOpen={isLeftSidebarOpen}>
      {children}
    </ProtectedLayout>
  );
}
