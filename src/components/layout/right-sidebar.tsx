"use client";

import { useRef } from "react";
import { MessageSquare } from "lucide-react";
import Chat from "../Chat";

export function RightSidebar() {
  const sidebarRef = useRef<HTMLDivElement>(null);

  return (
    <div className="h-screen border-l overflow-hidden flex flex-col">
      {/* Chat header */}
      <div className="flex items-center gap-2 px-4 py-3 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <MessageSquare className="h-5 w-5 text-primary" />
        <h2 className="font-medium">AI Tutor</h2>
      </div>

      {/* Chat container */}
      <div ref={sidebarRef} className="flex-1 overflow-hidden flex flex-col">
        <Chat />
      </div>
    </div>
  );
}
