"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { RefreshCw, BookOpen } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { useContentStore } from "@/store/contentStore";
import { useFlashcardStore } from "@/store/flashcardStore";
import MarkdownRender from "./MarkdownRender";
import { LoadingCard } from "@/components/ui/loading-card";
import { useUsageLimit } from "@/hooks/useUsageLimit";
import UsageLimitModal from "@/components/common/UsageLimitModal";
import { FeatureType } from "@/types/subscription";

const Flashcards = () => {
  const {
    contentId: storedContentId,
    contentType: storedContentType,
    flashcards,
    currentCard,
    isFlipped,
    studied,
    setContentInfo,
    setFlashcards,
    setCurrentCard,
    setIsFlipped,
    addStudied,
    resetFlashcards,
  } = useFlashcardStore();

  const { sourceContent, metadata, contentType } = useContentStore();
  const {
    isModalOpen,
    feature,
    usageData,
    planType,
    isChecking,
    checkUsageLimit,
    closeModal,
  } = useUsageLimit();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset the store if the user navigates to different content
  useEffect(() => {
    if (metadata?.contentId && metadata.contentId !== storedContentId) {
      resetFlashcards();
      setContentInfo(metadata.contentId, contentType);
    }
  }, [
    metadata?.contentId,
    storedContentId,
    contentType,
    resetFlashcards,
    setContentInfo,
  ]);

  // Generate new flashcards only when the user clicks the button
  const generateFlashcards = async () => {
    if (!sourceContent || !metadata) {
      toast.error("Please analyze content first to generate flashcards");
      return;
    }

    // Check usage limit before processing
    const canProceed = await checkUsageLimit(FeatureType.FLASHCARDS);
    if (!canProceed) {
      return;
    }

    setIsLoading(true);
    setError(null);

    // Reset flashcards each time they generate new ones
    resetFlashcards();
    setContentInfo(metadata.contentId, contentType);

    try {
      // Format source content based on type
      let formattedContent = "";
      if (Array.isArray(sourceContent)) {
        formattedContent = sourceContent.map((t) => t.text).join("\n\n");
      } else {
        formattedContent = sourceContent.toString();
      }

      const response = await fetch("/api/flashcards", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sourceContent: formattedContent,
          metadata,
          contentId: metadata.contentId,
          contentType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || "Failed to generate flashcards";
        if (response.status === 429) {
          throw new Error(
            "Rate limit reached. Please wait a moment before trying again."
          );
        } else if (response.status === 504) {
          throw new Error(
            "The request took too long. Try with shorter content or try again later."
          );
        } else if (response.status === 500 && errorMessage.includes("parse")) {
          throw new Error(
            "There was an issue processing the content. Please try again."
          );
        } else {
          throw new Error(errorMessage);
        }
      }

      const data = await response.json();

      if (
        !data.flashcards ||
        !Array.isArray(data.flashcards) ||
        data.flashcards.length === 0
      ) {
        throw new Error("No flashcards were generated");
      }

      setFlashcards(data.flashcards);
      toast.success(`Generated ${data.flashcards.length} flashcards`);
    } catch (error) {
      console.error("Flashcard generation error:", error);
      setError(
        error instanceof Error ? error.message : "Failed to generate flashcards"
      );
      toast.error(
        error instanceof Error ? error.message : "Failed to generate flashcards"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const nextCard = () => {
    if (currentCard < flashcards.length - 1) {
      setCurrentCard(currentCard + 1);
      setIsFlipped(false);
      addStudied(currentCard);
    }
  };

  const previousCard = () => {
    if (currentCard > 0) {
      setCurrentCard(currentCard - 1);
      setIsFlipped(false);
    }
  };

  const flipCard = () => {
    setIsFlipped(!isFlipped);
    if (!isFlipped) {
      addStudied(currentCard);
    }
  };

  // Show loader if generating
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingCard
          title="Generating Flashcards..."
          message="Creating flashcards based on your content"
          icon={BookOpen}
          className="py-12"
        />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full gap-4 p-4">
        <p className="text-sm text-destructive text-center">{error}</p>
        <Button onClick={generateFlashcards} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  // If no flashcards yet, show "Generate" button
  if (flashcards.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full gap-4 p-4">
        <p className="text-sm text-center text-muted-foreground">
          Generate flashcards to study key points from the{" "}
          {contentType === "text" ? "text" : "content"}
        </p>

        <Button
          onClick={generateFlashcards}
          variant="default"
          size="sm"
          disabled={isLoading || isChecking || !sourceContent}
        >
          {isLoading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Generating Flashcards...
            </>
          ) : isChecking ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Checking Usage...
            </>
          ) : (
            <>
              <BookOpen className="mr-2 h-4 w-4" />
              Generate Flashcards
            </>
          )}
        </Button>
      </div>
    );
  }

  // Calculate progress percentage
  const progressPercentage = Math.round(
    (studied.length / flashcards.length) * 100
  );

  // Otherwise, display the flashcards with side navigation
  return (
    <div className="flex flex-col h-full">
      {/* Progress bar at the top */}
      <div className="px-4 pt-4 pb-2">
        <div className="flex justify-end items-center text-xs text-gray-400 mb-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={generateFlashcards}
            className="h-6 px-2 text-xs"
          >
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
        <div className="w-full bg-gray-700 dark:bg-gray-700 rounded-full h-1.5">
          <div
            className="bg-blue-500 h-1.5 rounded-full"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>

      {/* Flashcard container */}
      <div className="flex-1 flex flex-col items-center justify-center p-4">
        {/* Flashcard */}
        <div className="w-full max-w-2xl mx-auto">
          <div
            className={cn(
              "relative w-full h-[350px] md:h-[400px] transition-all duration-500 preserve-3d cursor-pointer",
              isFlipped ? "rotate-y-180" : ""
            )}
            onClick={flipCard}
          >
            {/* Front Side */}
            <div className="absolute inset-0 w-full backface-hidden">
              <Card className="w-full h-full flex flex-col shadow-md hover:shadow-lg transition-shadow border-0 bg-gray-900 dark:bg-gray-900 rounded-xl overflow-hidden text-white">
                <CardContent className="p-0 flex flex-col h-full">
                  <div className="text-sm text-blue-400 font-medium mb-2 mt-6 text-center">
                    Question
                  </div>
                  <div className="prose prose-invert max-w-none px-8 py-4 flex-1 flex items-center justify-center text-lg w-full overflow-y-auto">
                    <div className="w-full flex justify-center items-center">
                      <div className="w-full max-w-lg text-center">
                        <MarkdownRender
                          content={flashcards[currentCard].front}
                        />
                      </div>
                    </div>
                  </div>
                  <p className="text-xs text-center text-gray-400 mb-6">
                    * Tap to flip *
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Back Side */}
            <div className="absolute inset-0 w-full backface-hidden rotate-y-180">
              <Card className="w-full h-full flex flex-col shadow-md border-0 bg-gray-900 dark:bg-gray-900 rounded-xl overflow-hidden text-white">
                <CardContent className="p-0 flex flex-col h-full">
                  <div className="text-sm text-blue-400 font-medium mb-2 mt-6 text-center">
                    Answer
                  </div>
                  <div className="prose prose-invert max-w-none px-8 py-4 flex-1 flex items-center justify-center text-lg w-full overflow-y-auto">
                    <div className="w-full flex justify-center items-center">
                      <div className="w-full max-w-lg text-center">
                        <MarkdownRender
                          content={flashcards[currentCard].back}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Navigation controls below card */}
          <div className="w-max mx-auto">
            <div className="flex gap-6 items-center mt-6">
              <button
                onClick={previousCard}
                disabled={currentCard === 0}
                className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800 text-white hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
                aria-label="Previous card"
              >
                <span className="text-xl font-light">&#10094;</span>
              </button>

              <span className="text-sm text-gray-400">
                {currentCard + 1} / {flashcards.length}
              </span>

              <button
                onClick={nextCard}
                disabled={currentCard === flashcards.length - 1}
                className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800 text-white hover:bg-gray-700 disabled:opacity-30 disabled:cursor-not-allowed transition-colors"
                aria-label="Next card"
              >
                <span className="text-xl font-light">&#10095;</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <UsageLimitModal
        isOpen={isModalOpen}
        onClose={closeModal}
        feature={feature}
        usageData={usageData}
        planType={planType}
      />
    </div>
  );
};

export default Flashcards;
