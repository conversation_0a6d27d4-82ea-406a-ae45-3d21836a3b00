"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Co<PERSON>, Share2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Open, MoreHorizontal } from "lucide-react";
import { Translator } from "@/components/Translator";
import { cn } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ContentOptionsProps {
  outputType: "summary" | "notes";
  onOutputTypeChange: (type: "summary" | "notes") => void;
  onCopy: () => void;
  onShare: () => void;
  isCopying: boolean;
  hasShareOption: boolean;
  outputContent: string;
}

export function ContentOptions({
  outputType,
  onOutputTypeChange,
  onCopy,
  onShare,
  isCopying,
  hasShareOption,
  outputContent,
}: ContentOptionsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-card rounded-xl border shadow-sm mb-6 overflow-hidden">
      <div className="p-3 sm:p-4">
        {/* Mobile-first responsive layout */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          
          {/* Content Type Selector - Full width on mobile */}
          <div className="flex w-full sm:w-auto">
            <div className="flex w-full sm:w-auto rounded-lg overflow-hidden border bg-muted/30">
              <Button
                variant={outputType === "summary" ? "default" : "ghost"}
                size="sm"
                onClick={() => onOutputTypeChange("summary")}
                className={cn(
                  "flex-1 sm:flex-none flex items-center justify-center gap-2 rounded-none font-medium transition-all",
                  outputType === "summary" 
                    ? "bg-primary text-primary-foreground shadow-sm" 
                    : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
                )}
              >
                <FileText className="h-4 w-4" />
                <span className="text-sm">Summary</span>
              </Button>

              <Button
                variant={outputType === "notes" ? "default" : "ghost"}
                size="sm"
                onClick={() => onOutputTypeChange("notes")}
                className={cn(
                  "flex-1 sm:flex-none flex items-center justify-center gap-2 rounded-none font-medium transition-all",
                  outputType === "notes" 
                    ? "bg-primary text-primary-foreground shadow-sm" 
                    : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
                )}
              >
                <BookOpen className="h-4 w-4" />
                <span className="text-sm">Notes</span>
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between sm:justify-end gap-2">
            
            {/* Translator - Always visible */}
            <div className="flex-shrink-0">
              <Translator summary={outputContent} />
            </div>

            {/* Desktop: Show all buttons */}
            <div className="hidden sm:flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onCopy}
                disabled={isCopying}
                className="flex items-center gap-2 hover:bg-muted/50 transition-colors"
              >
                <Copy className="h-4 w-4" />
                <span className="text-sm">{isCopying ? "Copying..." : "Copy"}</span>
              </Button>

              {hasShareOption && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onShare}
                  className="flex items-center gap-2 hover:bg-muted/50 transition-colors"
                >
                  <Share2 className="h-4 w-4" />
                  <span className="text-sm">Share</span>
                </Button>
              )}
            </div>

            {/* Mobile: Dropdown menu for actions */}
            <div className="sm:hidden">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-9 w-9 p-0 hover:bg-muted/50"
                  >
                    <MoreHorizontal className="h-4 w-4" />
                    <span className="sr-only">More options</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  <DropdownMenuItem onClick={onCopy} disabled={isCopying}>
                    <Copy className="h-4 w-4 mr-2" />
                    {isCopying ? "Copying..." : "Copy"}
                  </DropdownMenuItem>
                  {hasShareOption && (
                    <DropdownMenuItem onClick={onShare}>
                      <Share2 className="h-4 w-4 mr-2" />
                      Share
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
