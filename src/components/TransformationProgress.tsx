"use client";

import { useContentStore } from "@/store/contentStore";
import { FileText, BookOpen, CheckCircle } from "lucide-react";
import { LoadingCard } from "@/components/ui/loading-card";

export function TransformationProgress() {
  const { outputType, isOutputLoading } = useContentStore();

  // Determine if the process is complete
  const isComplete = !isOutputLoading;

  // Show the appropriate title based on output type and completion state
  const title = isComplete
    ? outputType === "summary"
      ? "Summary Ready"
      : "Notes Ready"
    : outputType === "summary"
    ? "Generating Summary..."
    : "Generating Notes...";

  // Show appropriate message based on completion state
  const message = isComplete
    ? outputType === "summary"
      ? "Loading your summary..."
      : "Loading your notes..."
    : "Preparing content for processing...";

  // Choose the appropriate icon based on the output type
  const icon = outputType === "summary" ? FileText : BookOpen;

  return (
    <LoadingCard
      title={title}
      message={message}
      icon={icon}
      isComplete={isComplete}
      completeIcon={CheckCircle}
      className="py-12"
    />
  );
}
