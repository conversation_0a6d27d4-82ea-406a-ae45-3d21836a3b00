"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  Check,
  Star,
  Rocket,
  Infinity as InfinityIcon,
} from "lucide-react";
import Link from "next/link";
import pricingData, { PlanType } from "@/data/pricingData";

export default function Pricing() {
  const [isAnnual, setIsAnnual] = useState(true);
  const { discountPercent } = pricingData;

  const plans = Object.keys(pricingData.plans) as PlanType[];

  const renderIcon = (planName: PlanType) => {
    switch (planName) {
      case "Starter":
        return (
          <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/40 flex items-center justify-center">
            <Rocket className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
        );
      case "Pro":
        return (
          <div className="w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900/40 flex items-center justify-center">
            <Star className="h-6 w-6 text-purple-600 dark:text-purple-400" />
          </div>
        );
      case "Unlimited":
        return (
          <div className="w-10 h-10 rounded-full bg-emerald-100 dark:bg-emerald-900/40 flex items-center justify-center">
            <InfinityIcon className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 md:py-24">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <div className="inline-block px-4 py-1.5 mb-6 rounded-full bg-blue-50 dark:bg-blue-950/30 text-blue-600 dark:text-blue-400 text-sm font-medium">
            PRICING PLANS
          </div>
          <h1 className="text-3xl md:text-4xl font-bold mb-6">
            Choose Your Learning Plan
          </h1>
          <p className="text-lg text-foreground/70 max-w-2xl mx-auto">
            Select the plan that fits your learning needs and budget. All plans
            include our core learning features.
          </p>

          <div className="flex items-center justify-center mt-8 bg-muted rounded-full p-1 w-max mx-auto">
            <button
              onClick={() => setIsAnnual(false)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                !isAnnual
                  ? "bg-background shadow-sm text-foreground"
                  : "text-foreground/70"
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setIsAnnual(true)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                isAnnual
                  ? "bg-background shadow-sm text-foreground"
                  : "text-foreground/70"
              }`}
            >
              Annually{" "}
              <span className="text-green-600 dark:text-green-500 ml-1">
                -{discountPercent}%
              </span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 w-full mx-auto">
          {plans.map((planName) => {
            const plan = pricingData.plans[planName];
            return (
              <Card
                key={planName}
                className={`relative overflow-hidden transition-all hover:shadow-lg ${
                  plan.isPopular
                    ? "border-purple-500 dark:border-purple-500 shadow-md transform hover:-translate-y-1"
                    : "border-border/40 hover:border-foreground/30"
                }`}
              >
                {plan.isPopular && (
                  <div className="absolute -right-12 top-5 rotate-45 bg-purple-600 text-white py-1 px-12 text-xs font-medium">
                    MOST POPULAR
                  </div>
                )}
                <CardHeader
                  className={`pb-2 border-b border-border/10 ${plan.color}`}
                >
                  <div className="flex justify-between items-start">
                    {renderIcon(planName)}
                    {plan.isPopular && (
                      <div className="hidden md:flex items-center text-purple-600 dark:text-purple-400 text-sm">
                        <Star className="h-4 w-4 mr-1 fill-current" />
                        <span>Popular</span>
                      </div>
                    )}
                  </div>
                  <h3 className="text-xl font-bold mt-4">{planName}</h3>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="mb-6">
                    <div className="flex items-baseline">
                      <span className="text-3xl md:text-4xl font-bold">
                        ${isAnnual ? plan.annual : plan.monthly}
                      </span>
                      <span className="text-foreground/70 ml-1">
                        {isAnnual ? "/year" : "/month"}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-start">
                        <div className="rounded-full bg-green-100 dark:bg-green-900/30 p-1 mr-3 mt-0.5">
                          <Check className="h-3 w-3 text-green-600 dark:text-green-400" />
                        </div>
                        <span className="text-foreground/80 text-sm">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="pt-4">
                  <Link
                    href={`/auth?plan=${planName.toLowerCase()}`}
                    className="w-full"
                  >
                    <Button
                      className={`w-full py-6 ${
                        plan.isPopular
                          ? "bg-purple-600 hover:bg-purple-700 text-white"
                          : `bg-background border hover:${plan.color} text-foreground`
                      }`}
                      variant={plan.isPopular ? "default" : "outline"}
                    >
                      {isAnnual ? "Get Annual Plan" : "Get Monthly Plan"}
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}
