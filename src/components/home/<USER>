"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Menu, X, Moon, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import Image from "next/image";
import { useAuthStore } from "@/store/authStore";
import { usePathname } from "next/navigation";

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const { user } = useAuthStore();
  const pathname = usePathname();
  const isHomePage = pathname === "/";

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleNavigation = (sectionId: string) => {
    setIsMenuOpen(false);
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    } else if (!isHomePage) {
      // If we're not on the homepage and the element doesn't exist,
      // navigate to the homepage with the hash
      window.location.href = `/#${sectionId}`;
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 dark:bg-[#050A1B] backdrop-blur supports-[backdrop-filter]:bg-background/60 dark:supports-[backdrop-filter]:bg-[#050A1B]/95">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <Link href="/" className="flex items-center">
            <div className="relative flex items-center">
              <Image
                src="https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WFITvZJhI9LXTfxC5Ad4lp1WocqmwHirgGPav"
                alt="Qlipify logo"
                width={50}
                height={50}
                unoptimized
              />
              <span className="inline-block text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-violet-500 ml-[-8px] transform translate-y-0">
                lipify
              </span>
            </div>
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6">
          <button
            onClick={() => handleNavigation("features")}
            className="text-sm font-medium text-foreground/80 hover:text-blue-600 transition-colors"
          >
            Features
          </button>
          <button
            onClick={() => handleNavigation("how-it-works")}
            className="text-sm font-medium text-foreground/80 hover:text-blue-600 transition-colors"
          >
            How It Works
          </button>
          <button
            onClick={() => handleNavigation("problems")}
            className="text-sm font-medium text-foreground/80 hover:text-blue-600 transition-colors"
          >
            Why Qlipify
          </button>
          <button
            onClick={() => handleNavigation("testimonials")}
            className="text-sm font-medium text-foreground/80 hover:text-blue-600 transition-colors"
          >
            Testimonials
          </button>
          <button
            onClick={() => handleNavigation("pricing")}
            className="text-sm font-medium text-foreground/80 hover:text-blue-600 transition-colors"
          >
            Pricing
          </button>
          <button
            onClick={() => handleNavigation("faq")}
            className="text-sm font-medium text-foreground/80 hover:text-blue-600 transition-colors"
          >
            FAQ
          </button>
        </nav>

        <div className="hidden md:flex items-center gap-3">
          {mounted && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="text-foreground/80"
            >
              {theme === "dark" ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
              <span className="sr-only">Toggle theme</span>
            </Button>
          )}
          {user ? (
            <Link href="/dashboard">
              <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                Dashboard
              </Button>
            </Link>
          ) : (
            <>
              <Link href="/auth">
                <Button
                  variant="ghost"
                  className="text-foreground/80 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-950/30"
                >
                  Log in
                </Button>
              </Link>
              <Link href="/auth" className="hidden sm:block">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-5">
                  Try for free
                </Button>
              </Link>
            </>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="flex items-center md:hidden">
          {mounted && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="mr-1 text-foreground/80"
            >
              {theme === "dark" ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
              <span className="sr-only">Toggle theme</span>
            </Button>
          )}
          <button
            className="text-foreground/80"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden border-b border-border/40 bg-background/95 dark:bg-[#050A1B] backdrop-blur supports-[backdrop-filter]:bg-background/60 dark:supports-[backdrop-filter]:bg-[#050A1B]/95">
          <div className="container mx-auto py-4 px-4 flex flex-col gap-3">
            <button
              onClick={() => handleNavigation("features")}
              className="text-sm font-medium p-2 text-foreground/80 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:text-blue-600 rounded-md"
            >
              Features
            </button>
            <button
              onClick={() => handleNavigation("how-it-works")}
              className="text-sm font-medium p-2 text-foreground/80 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:text-blue-600 rounded-md"
            >
              How It Works
            </button>
            <button
              onClick={() => handleNavigation("problems")}
              className="text-sm font-medium p-2 text-foreground/80 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:text-blue-600 rounded-md"
            >
              Why Qlipify
            </button>
            <button
              onClick={() => handleNavigation("testimonials")}
              className="text-sm font-medium p-2 text-foreground/80 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:text-blue-600 rounded-md"
            >
              Testimonials
            </button>
            <button
              onClick={() => handleNavigation("pricing")}
              className="text-sm font-medium p-2 text-foreground/80 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:text-blue-600 rounded-md"
            >
              Pricing
            </button>
            <button
              onClick={() => handleNavigation("faq")}
              className="text-sm font-medium p-2 text-foreground/80 hover:bg-blue-50 dark:hover:bg-blue-950/30 hover:text-blue-600 rounded-md"
            >
              FAQ
            </button>
            <div className="flex flex-col gap-2 pt-2 border-t border-border/40">
              {user ? (
                <Link href="/dashboard">
                  <Button className="w-full justify-center bg-blue-600 hover:bg-blue-700 text-white">
                    Dashboard
                  </Button>
                </Link>
              ) : (
                <>
                  <Link href="/auth">
                    <Button
                      variant="ghost"
                      className="w-full justify-center text-foreground/80"
                    >
                      Log in
                    </Button>
                  </Link>
                  <Link href="/auth">
                    <Button className="w-full justify-center bg-blue-600 hover:bg-blue-700 text-white">
                      Try for free
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  );
}
