import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { ArrowRight } from "lucide-react";

export default function AboutUs() {
  return (
    <section id="about" className="py-16 bg-white dark:bg-gray-950">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="max-w-3xl mx-auto text-center mb-8">
          <div className="inline-block px-4 py-1.5 mb-4 rounded-full bg-purple-50 dark:bg-purple-950/30 text-purple-600 dark:text-purple-400 text-sm font-medium">
            OUR STORY
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Why We Built Qlipify
          </h2>
        </div>

        <div className="bg-white dark:bg-gray-900 rounded-xl border border-purple-100 dark:border-purple-900 overflow-hidden shadow-md">
          <div className="p-6 md:p-8">
            <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
              <div className="flex-shrink-0">
                <Avatar className="h-20 w-20 md:h-24 md:w-24 border-2 border-purple-200 dark:border-purple-800">
                  <AvatarImage
                    src="https://0uknh5ir68.ufs.sh/f/nUueOgDyrA7WFITvZJhI9LXTfxC5Ad4lp1WocqmwHirgGPav?height=128&width=128"
                    alt="Founder"
                  />
                  <AvatarFallback className="text-xl">FD</AvatarFallback>
                </Avatar>
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-bold mb-3 text-center md:text-left">
                  From Frustration to Solution
                </h3>
                <div className="space-y-3 text-foreground/80">
                  <p>
                    I built Qlipify after struggling with information overload
                    as a student. Spending hours on content only to forget most
                    of it was frustrating and inefficient.
                  </p>
                  <p>
                    Our mission is simple: help you learn faster and remember
                    more through AI-powered tools that make learning interactive
                    and personalized.
                  </p>
                </div>

                <div className="mt-6 flex flex-col md:flex-row items-center justify-between gap-4">
                  <p className="font-medium text-purple-600 dark:text-purple-400">
                    The Qlipify Team
                  </p>
                  <Link href="/auth">
                    <Button
                      variant="outline"
                      className="border-purple-200 dark:border-purple-800 text-purple-600 dark:text-purple-400"
                    >
                      Join our community <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
