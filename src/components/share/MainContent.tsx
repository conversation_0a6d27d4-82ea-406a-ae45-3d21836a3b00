"use client";
import { useState, useEffect } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuthStore } from "@/store/authStore";
import { MessageSquare, Brain, BookOpenCheck } from "lucide-react";
import CloneButton from "../CloneButton";
import MarkdownRender from "../MarkdownRender";
import { ContentType } from "@prisma/client";

interface Summary {
  contentId: string;
  contentType: ContentType;
  title: string;
  content: string;
  sourceId: string;
  generatedAt: string;
  isShared: boolean;
  shareToken?: string;
  originalContent?: string;
  outputType?: string;
}

interface ShareMainComponentProps {
  summary: Summary;
}

export default function ShareMainComponent({
  summary,
}: ShareMainComponentProps) {
  const {
    contentId,
    contentType,
    title,
    sourceId,
    generatedAt,
    shareToken,
    content,
    originalContent,
    outputType,
  } = summary;
  const { user } = useAuthStore();
  const [headerHeight, setHeaderHeight] = useState(0);

  useEffect(() => {
    const updateHeaderHeight = () => {
      const header = document.querySelector("header");
      if (header) {
        setHeaderHeight(header.offsetHeight);
      }
    };
    updateHeaderHeight();
    window.addEventListener("resize", updateHeaderHeight);
    return () => window.removeEventListener("resize", updateHeaderHeight);
  }, []);

  const renderPlaceholder = () => {
    if (!user) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-6">
          <p className="mb-4 text-lg">Sign in to unlock all learning tools</p>
          <Button onClick={() => (window.location.href = "/auth")}>
            Sign In
          </Button>
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <p className="mb-4 text-lg">
          Clone this content to access interactive features
        </p>
        <CloneButton
          contentId={contentId}
          contentType={contentType}
          title={title}
          summary={content}
          sourceId={sourceId}
          originalContent={originalContent}
          outputType={outputType}
        />
        <p className="mt-4 text-sm text-muted-foreground">
          Cloning creates a copy in your account where you can use all learning
          tools
        </p>
      </div>
    );
  };

  return (
    <div className="flex-1 overflow-y-auto h-full relative">
      <div className="w-full max-w-full mx-auto">
        <div className="w-full mx-auto px-4">
          <Tabs defaultValue="content" className="w-full">
            <div
              className="sticky-tabs bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 w-full"
              style={{
                position: "sticky",
                top: `${headerHeight}px`,
                zIndex: 20,
              }}
            >
              <div className="md:hidden border-y">
                <TabsList className="w-full grid grid-cols-4 h-16 bg-transparent">
                  <TabsTrigger
                    value="content"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full ml-2 md:ml-0"
                  >
                    <MessageSquare className="h-5 w-5" />
                    <span className="text-xs">Content</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="chat"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full"
                  >
                    <MessageSquare className="h-5 w-5" />
                    <span className="text-xs">Chat</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="quiz"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full"
                  >
                    <Brain className="h-5 w-5" />
                    <span className="text-xs">Quiz</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="flashcards"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full"
                  >
                    <BookOpenCheck className="h-5 w-5" />
                    <span className="text-xs">Cards</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="hidden md:block border-b">
                <TabsList className="w-full h-12 bg-transparent">
                  <div className="flex max-w-[800px] mx-auto w-full">
                    <TabsTrigger
                      value="content"
                      className="flex-1 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none h-12 px-6"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Content
                    </TabsTrigger>
                    <TabsTrigger
                      value="flashcards"
                      className="flex-1 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none h-12 px-6"
                    >
                      <BookOpenCheck className="h-4 w-4 mr-2" />
                      Flashcards
                    </TabsTrigger>
                    <TabsTrigger
                      value="quiz"
                      className="flex-1 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none h-12 px-6"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Quiz
                    </TabsTrigger>
                  </div>
                </TabsList>
              </div>
            </div>

            <div className="md:pb-0">
              <TabsContent
                value="content"
                className="mt-4 focus-visible:outline-none"
              >
                <Card className="border-0 shadow-none">
                  <div
                    id="summary-container"
                    className="prose dark:prose-invert"
                  >
                    <MarkdownRender content={content} />
                  </div>
                </Card>
              </TabsContent>

              <TabsContent
                value="chat"
                className="mt-4 focus-visible:outline-none md:hidden"
              >
                <Card className="border-0 shadow-none h-64">
                  {renderPlaceholder()}
                </Card>
              </TabsContent>
              <TabsContent
                value="quiz"
                className="mt-4 focus-visible:outline-none md:hidden"
              >
                <Card className="border-0 shadow-none h-64">
                  {renderPlaceholder()}
                </Card>
              </TabsContent>
              <TabsContent
                value="flashcards"
                className="mt-4 focus-visible:outline-none md:hidden"
              >
                <Card className="border-0 shadow-none h-64">
                  {renderPlaceholder()}
                </Card>
              </TabsContent>
              <TabsContent
                value="flashcards"
                className="mt-4 focus-visible:outline-none hidden md:block"
              >
                <Card className="border-0 shadow-none">
                  {renderPlaceholder()}
                </Card>
              </TabsContent>
              <TabsContent
                value="quiz"
                className="mt-4 focus-visible:outline-none hidden md:block"
              >
                <Card className="border-0 shadow-none">
                  {renderPlaceholder()}
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
