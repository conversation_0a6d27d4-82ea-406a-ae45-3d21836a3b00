"use client";

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { FeatureType } from "@/types/subscription";
import { PlanType } from "@prisma/client";
import { useRouter } from "next/navigation";
import { UsageCheckResult } from "@/lib/usage-utils";

interface UsageLimitPromptProps {
  isOpen: boolean;
  onClose: () => void;
  feature: FeatureType;
  usageData: UsageCheckResult;
  planType: PlanType;
}

const UsageLimitPrompt: React.FC<UsageLimitPromptProps> = ({
  isOpen,
  onClose,
  feature,
  usageData,
  planType,
}) => {
  const router = useRouter();

  const getFeatureDisplayName = (feature: FeatureType): string => {
    const featureNames: Record<FeatureType, string> = {
      [FeatureType.TRANSFORMATIONS]: "Content Transformations",
      [FeatureType.QUIZZES]: "Quiz Generation",
      [FeatureType.FLASHCARDS]: "Flashcard Creation",
      [FeatureType.AI_CHAT]: "AI Chat",
    };
    return featureNames[feature] || feature;
  };

  const handleUpgrade = () => {
    router.push("/pricing");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <span>Usage Limit Reached</span>
          </DialogTitle>
          <DialogDescription>
            You've reached your limit for{" "}
            {getFeatureDisplayName(feature).toLowerCase()}.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Current Usage</span>
            <Badge variant="secondary">{planType} Plan</Badge>
          </div>

          {!usageData.unlimited && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>
                  {usageData.current} of {usageData.limit} used
                </span>
                <span>{Math.round(usageData.percentage)}%</span>
              </div>
              <Progress value={usageData.percentage} className="h-2" />
            </div>
          )}

          {usageData.message && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-800">{usageData.message}</p>
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <h4 className="font-medium mb-2 flex items-center">
              <Crown className="h-4 w-4 mr-2 text-yellow-500" />
              Upgrade Benefits
            </h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Higher usage limits</li>
              <li>• Priority processing</li>
              <li>• Advanced features</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            className="w-full sm:w-auto"
          >
            Continue
          </Button>
          <Button onClick={handleUpgrade} className="w-full sm:w-auto">
            <Crown className="h-4 w-4 mr-2" />
            Upgrade Plan
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UsageLimitPrompt;
