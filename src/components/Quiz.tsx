"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useContentStore } from "@/store/contentStore";
import { RefreshCw, FileText } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useQuizStore } from "@/store/quizStore";

import MarkdownRender from "./MarkdownRender";
import { LoadingCard } from "@/components/ui/loading-card";
import { useUsageLimit } from "@/hooks/useUsageLimit";
import UsageLimitModal from "@/components/common/UsageLimitModal";
import { FeatureType } from "@/types/subscription";

const Quiz = () => {
  const {
    questions,
    currentQuestion,
    selectedAnswer,
    answers,
    quizCompleted,
    contentId: storedContentId,
    contentType: storedContentType,
    setQuestions,
    setCurrentQuestion,
    setSelectedAnswer,
    setAnswers,
    setQuizCompleted,
    setContentInfo,
    resetQuiz,
  } = useQuizStore();

  const { sourceContent, metadata, contentType } = useContentStore();
  const {
    isModalOpen,
    feature,
    usageData,
    planType,
    isChecking,
    checkUsageLimit,
    closeModal,
  } = useUsageLimit();

  useEffect(() => {
    // Only reset if content ID changes and doesn't match stored quiz ID
    if (metadata?.contentId && storedContentId !== metadata.contentId) {
      resetQuiz();
      setContentInfo(metadata.contentId, contentType);
    }
  }, [
    metadata?.contentId,
    storedContentId,
    contentType,
    resetQuiz,
    setContentInfo,
  ]);

  const [isAnswered, setIsAnswered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const generateQuiz = async () => {
    if (!sourceContent || !metadata) {
      toast.error("Please analyze content first to generate a quiz");
      return;
    }

    // Check usage limit before processing
    const canProceed = await checkUsageLimit(FeatureType.QUIZZES);
    if (!canProceed) {
      return;
    }

    // Always reset quiz state when generating a new quiz
    resetQuiz();
    setContentInfo(metadata.contentId, contentType);

    setIsLoading(true);

    try {
      // Format source content based on type
      let formattedContent = "";
      if (Array.isArray(sourceContent)) {
        formattedContent = sourceContent.map((t) => t.text).join("\n\n");
      } else {
        formattedContent = sourceContent.toString();
      }

      const response = await fetch("/api/quiz", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          sourceContent: formattedContent,
          metadata,
          contentId: metadata.contentId,
          contentType,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 429 && data.isRateLimit) {
          throw new Error(
            "Rate limit reached. Please wait a minute before trying again."
          );
        }
        if (response.status === 504 && data.isTimeout) {
          throw new Error(
            "The request took too long to process. Please try with shorter content."
          );
        }
        throw new Error(data.error || "Failed to generate quiz");
      }

      setQuestions(data.questions);
      setContentInfo(metadata.contentId, contentType);
    } catch (error) {
      console.error("Quiz generation error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to generate quiz"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswer = (answerIndex: number) => {
    if (isAnswered) return;
    setSelectedAnswer(answerIndex);
    setIsAnswered(true);

    const isCorrect = answerIndex === questions[currentQuestion].correctAnswer;
    const newAnswers = [...answers];
    newAnswers[currentQuestion] = {
      score: isCorrect ? 1 : 0,
      isCorrect,
    };
    setAnswers(newAnswers);
  };

  const nextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setIsAnswered(false);
    } else {
      setQuizCompleted(true);
    }
  };

  const totalScore = answers.reduce((sum, answer) => sum + answer?.score, 0);

  useEffect(() => {
    if (quizCompleted && storedContentId) {
      const submitQuizAttempt = async () => {
        try {
          await fetch("/api/quiz", {
            method: "PUT",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              contentId: storedContentId,
              contentType: storedContentType,
              answers,
              score: totalScore,
            }),
          });
        } catch (err) {
          console.error("Failed to save quiz attempt", err);
        }
      };
      submitQuizAttempt();
    }
  }, [quizCompleted, storedContentId, storedContentType, answers, totalScore]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <LoadingCard
          title="Generating Quiz..."
          message="Creating questions based on your content"
          icon={FileText}
          className="py-12"
        />
      </div>
    );
  }

  if (questions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full gap-4 p-4">
        <p className="text-center text-muted-foreground">
          Generate a quiz based on the{" "}
          {contentType === "text" ? "text" : "content"} to test your
          understanding
        </p>

        <Button
          onClick={generateQuiz}
          disabled={isLoading || isChecking || !sourceContent}
          size="sm"
        >
          {isLoading ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Generating Quiz...
            </>
          ) : isChecking ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Checking Usage...
            </>
          ) : (
            <>
              <FileText className="mr-2 h-4 w-4" />
              Generate Quiz
            </>
          )}
        </Button>
      </div>
    );
  }

  if (quizCompleted) {
    const scorePercentage = (totalScore / questions.length) * 100;
    return (
      <div className="flex flex-col items-center justify-center h-full gap-6 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center pb-2">
            <CardTitle className="text-xl">Quiz Completed!</CardTitle>
            <div className="mt-4 flex flex-col items-center gap-2">
              <div className="w-20 h-20 rounded-full border-4 border-primary flex items-center justify-center">
                <span className="text-xl font-bold">
                  {Math.round(scorePercentage)}%
                </span>
              </div>
              <p className="text-base mt-2">
                Score: {totalScore}/{questions.length}
              </p>
              <Progress value={scorePercentage} className="w-full mt-2" />
            </div>
          </CardHeader>
          <CardContent className="flex justify-center pt-2">
            <Button
              onClick={() => {
                // First reset the quiz state completely
                resetQuiz();
                // Then generate a new quiz
                generateQuiz();
              }}
              className="w-full sm:w-auto"
            >
              Try New Quiz
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with progress and controls */}
      <div className="flex items-center justify-between p-2 sm:p-4 border-b sticky top-0 bg-background z-10">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">
            Question {currentQuestion + 1} of {questions.length}
          </span>
          <Progress
            value={((currentQuestion + 1) / questions.length) * 100}
            className="w-[60px] sm:w-[100px]"
          />
        </div>
        <div className="flex items-center gap-2 sm:gap-4">
          <span className="text-sm text-muted-foreground">
            Score: {totalScore}/{currentQuestion + 1}
          </span>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              // First reset the quiz state completely
              resetQuiz();
              // Then generate a new quiz
              generateQuiz();
            }}
            disabled={isLoading}
            className="h-8 w-8"
            title="Generate new quiz"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Main content area with better mobile UX */}
      <div
        className={cn(
          "flex-1 flex flex-col p-2 sm:p-4 gap-3 sm:gap-4",
          isAnswered && "pb-20 sm:pb-4" // Add bottom padding on mobile when button is fixed
        )}
      >
        {/* Explanation section - shown at top when answered */}
        {isAnswered && (
          <Card className="shadow-sm border animate-in slide-in-from-top-2 duration-300">
            <CardContent className="p-3 sm:p-4">
              <div className="bg-muted rounded-lg p-3 text-sm">
                <p className="font-medium mb-2 text-green-600 dark:text-green-400">
                  ✓ Explanation:
                </p>
                <MarkdownRender
                  content={questions[currentQuestion].explanation}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Question and options */}
        <Card className="shadow-sm border">
          <CardHeader className="p-3 sm:p-4">
            <CardTitle className="text-sm sm:text-base">
              <MarkdownRender content={questions[currentQuestion].question} />
            </CardTitle>
          </CardHeader>
          <CardContent className="p-3 sm:p-4 pt-0">
            <div className="grid gap-2">
              {questions[currentQuestion].options.map((option, index) => {
                const isCorrectAnswer =
                  index === questions[currentQuestion].correctAnswer;
                const isSelected = index === selectedAnswer;
                let buttonVariant: "outline" | "default" | "destructive" =
                  "outline";

                if (isAnswered) {
                  if (isCorrectAnswer) {
                    buttonVariant = "default";
                  } else if (isSelected) {
                    buttonVariant = "destructive";
                  }
                } else if (isSelected) {
                  buttonVariant = "default";
                }

                return (
                  <Button
                    key={index}
                    variant={buttonVariant}
                    className={cn(
                      "w-full justify-start text-left h-auto min-h-[40px] px-3 py-2 text-sm",
                      "whitespace-normal break-words transition-all duration-200",
                      isAnswered &&
                        isCorrectAnswer &&
                        "bg-green-500 hover:bg-green-500 text-white",
                      isAnswered &&
                        isSelected &&
                        !isCorrectAnswer &&
                        "bg-red-500 hover:bg-red-500 text-white"
                    )}
                    onClick={() => handleAnswer(index)}
                    disabled={isAnswered}
                  >
                    <MarkdownRender content={option} />
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Fixed bottom navigation - always visible when answered */}
      {isAnswered && (
        <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-3 pb-6 sm:sticky sm:p-4 sm:pb-4 animate-in slide-in-from-bottom-2 duration-300 z-20">
          <div className="flex justify-center max-w-screen-xl mx-auto">
            <Button
              onClick={nextQuestion}
              className="w-full sm:w-auto min-w-[140px] h-12 sm:h-10 text-base sm:text-sm font-medium"
              size="default"
            >
              {currentQuestion < questions.length - 1
                ? "Next Question"
                : "View Results"}
            </Button>
          </div>
        </div>
      )}
      <UsageLimitModal
        isOpen={isModalOpen}
        onClose={closeModal}
        feature={feature}
        usageData={usageData}
        planType={planType}
      />
    </div>
  );
};

export default Quiz;
