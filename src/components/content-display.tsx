"use client";

import { useEffect, useState } from "react";
import { AlertCircle } from "lucide-react";
import { useContentStore } from "@/store/contentStore";
import { ContentState } from "@/types/contentTypes";
import { useTranslateStore } from "@/store/translateStore";
import MarkdownRender from "./MarkdownRender";
import { TransformationProgress } from "./TransformationProgress";
import { toast } from "sonner";
import { ContentOptions } from "@/components/ContentOptions";
import ShareModal from "@/components/ShareModal";
import { getOrGenerateShareToken } from "@/utils/share";
import { useUsageLimit } from "@/hooks/useUsageLimit"; // Added
import UsageLimitModal from "@/components/common/UsageLimitModal"; // Added
import { FeatureType } from "@/types/subscription"; // Added

export function ContentDisplay(): React.ReactElement {
  const contentStore = useContentStore() as ContentState & {
    setOutputType: (type: "summary" | "notes") => void;
    generateOutput: () => Promise<void>;
  };

  const {
    outputContent,
    error: currentStoreError, // Aliased to avoid conflict
    sourceContent,
    outputType,
    metadata,
    contentType,
    isOutputLoading: storeIsOutputLoading, // Aliased
    generateOutput: storeGenerateOutput, // Aliased
  } = contentStore;

  const { translatedOutput, resetTranslation } = useTranslateStore();
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [isCopying, setIsCopying] = useState(false);
  const [currentShareToken, setCurrentShareToken] = useState<
    string | undefined
  >(metadata?.shareToken);

  // Added: Initialize useUsageLimit hook
  const {
    isModalOpen: isUsageModalOpen,
    feature: usageFeature,
    usageData,
    planType,
    // isChecking: isUsageChecking, // Available if needed
    checkUsageLimit,
    closeModal: closeUsageModal,
  } = useUsageLimit();

  useEffect(() => {
    return () => {
      resetTranslation();
    };
  }, [resetTranslation]);

  // Sync share token when metadata changes
  useEffect(() => {
    setCurrentShareToken(metadata?.shareToken);
  }, [metadata?.shareToken]);

  const displayContent = translatedOutput || outputContent;

  useEffect(() => {
    const attemptGenerateOutput = async () => {
      if (
        metadata?.sourceId &&
        sourceContent &&
        !outputContent &&
        !storeIsOutputLoading &&
        // MODIFIED: Condition to check for any existing error to prevent loop
        !currentStoreError
      ) {
        console.log(
          "ContentDisplay: Conditions met, checking usage limit before generating output."
        );

        const canProceed = await checkUsageLimit(FeatureType.TRANSFORMATIONS);
        if (canProceed) {
          console.log(
            "ContentDisplay: Usage limit check passed, proceeding to generateOutput."
          );
          setTimeout(() => {
            // Re-check error state right before calling
            if (!useContentStore.getState().error?.includes("Rate limit")) {
              storeGenerateOutput();
            } else {
              console.log(
                "ContentDisplay: Rate limit error detected before calling generateOutput in setTimeout."
              );
            }
          }, 100);
        } else {
          console.log(
            "ContentDisplay: Usage limit reached. Modal should be displayed by useUsageLimit hook."
          );
          // useUsageLimit hook handles opening the modal
        }
      } else if (currentStoreError) {
        // Simplified condition to catch any existing error
        console.log(
          "ContentDisplay: GenerateOutput attempt skipped due to existing store error:",
          currentStoreError
        );
      } else if (storeIsOutputLoading) {
        console.log(
          "ContentDisplay: GenerateOutput attempt skipped as output is already loading."
        );
      } else if (outputContent) {
        console.log(
          "ContentDisplay: GenerateOutput attempt skipped as output content already exists."
        );
      }
    };

    attemptGenerateOutput();
  }, [
    metadata?.sourceId,
    sourceContent,
    outputContent,
    contentType,
    outputType,
    storeGenerateOutput, // Use aliased store method
    checkUsageLimit, // Added dependency
    currentStoreError, // Added dependency
    storeIsOutputLoading, // Added dependency
  ]);

  const handleCopy = async () => {
    setIsCopying(true);
    try {
      const summaryElement = document.getElementById("summary-container");
      if (!summaryElement) {
        toast.error("No content to copy.");
        return;
      }
      const textToCopy = summaryElement.innerText || "";
      if (!textToCopy.trim()) {
        toast.error("No text found in summary container.");
        return;
      }
      await navigator.clipboard.writeText(textToCopy);
      toast.success("Content copied to clipboard");
    } catch (error) {
      console.error("Copy error:", error);
      toast.error("Failed to copy content");
    } finally {
      setIsCopying(false);
    }
  };

  const handleShare = async () => {
    setShareModalOpen(true);
  };

  const handleShareContent = async () => {
    if (!metadata?.contentId) {
      toast.error("Invalid or missing Content ID.");
      return { success: false };
    }

    const result = await getOrGenerateShareToken(
      metadata.contentId,
      currentShareToken
    );

    // Update local state if we got a new token
    if (result.success && result.shareToken) {
      setCurrentShareToken(result.shareToken);
    }

    return result;
  };

  // Function to handle token regeneration
  const handleTokenRegenerated = (newToken: string) => {
    setCurrentShareToken(newToken);
  };

  return (
    <div className="space-y-6">
      {/* Content Options */}
      {displayContent && (
        <ContentOptions
          outputType={outputType}
          onOutputTypeChange={(type) => {
            // Check usage before setting output type which triggers regeneration
            checkUsageLimit(FeatureType.TRANSFORMATIONS).then((canProceed) => {
              if (canProceed) {
                contentStore.setOutputType(type);
              }
            });
          }}
          onCopy={handleCopy}
          onShare={handleShare}
          isCopying={isCopying}
          hasShareOption={Boolean(metadata?.contentId)}
          outputContent={outputContent || ""}
        />
      )}

      <div className="min-h-[200px]" id="summary-container">
        {currentStoreError ? ( // Use aliased currentStoreError
          <div className="p-4 text-red-500 bg-red-50 dark:bg-red-950/50 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              <span>
                {currentStoreError.includes("Rate limit") ? ( // Use aliased currentStoreError
                  <>
                    Rate limit reached. Please wait a minute before trying
                    again.
                    <br />
                    <span className="text-sm opacity-75">
                      We process up to 15 requests per minute to ensure service
                      quality.
                    </span>
                  </>
                ) : currentStoreError.includes("took too long") ? ( // Use aliased currentStoreError
                  <>
                    The request timed out. Please try again with shorter
                    content.
                    <br />
                    <span className="text-sm opacity-75">
                      Try breaking down your content into smaller sections.
                    </span>
                  </>
                ) : (
                  currentStoreError // Use aliased currentStoreError
                )}
              </span>
            </div>
          </div>
        ) : !displayContent ? (
          // Always show the progress bar for both new and stored content
          <TransformationProgress />
        ) : (
          <div className="prose dark:prose-invert max-w-none pb-8 overflow-x-hidden">
            <MarkdownRender content={displayContent} />
          </div>
        )}
      </div>

      <ShareModal
        isOpen={shareModalOpen}
        onClose={() => setShareModalOpen(false)}
        onShare={handleShareContent}
        title={metadata?.title || ""}
        contentId={metadata?.contentId}
        existingShareToken={currentShareToken}
        onTokenRegenerated={handleTokenRegenerated}
      />

      {/* Added Usage Limit Modal */}
      {usageData && (
        <UsageLimitModal
          isOpen={isUsageModalOpen}
          onClose={closeUsageModal}
          feature={usageFeature || FeatureType.TRANSFORMATIONS}
          usageData={usageData}
          planType={planType}
        />
      )}
    </div>
  );
}
