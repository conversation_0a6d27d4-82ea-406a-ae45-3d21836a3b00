"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Copy, Check, Loader2, Share2, <PERSON>ota<PERSON><PERSON>cw } from "lucide-react";
import { toast } from "sonner";
import { handleUnshare, generateShareToken } from "@/utils/share";

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  onShare: () => Promise<{ success: boolean; shareToken?: string }>;
  title: string;
  contentId?: string;
  existingShareToken?: string;
  onTokenRegenerated?: (newToken: string) => void;
}

export default function ShareModal({
  isOpen,
  onClose,
  onShare,
  title,
  contentId,
  existingShareToken,
  onTokenRegenerated,
}: ShareModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [shareUrl, setShareUrl] = useState<string>("");
  const [isCopied, setIsCopied] = useState(false);

  // Set existing share URL when modal opens or when existing token changes
  useEffect(() => {
    if (isOpen) {
      if (existingShareToken) {
        // Use existing token
        const url = `${window.location.origin}/share/${existingShareToken}`;
        setShareUrl(url);
      } else if (!shareUrl && !isLoading) {
        // Generate new token
        handleShareAction();
      }
    }
  }, [isOpen, existingShareToken]);

  const handleShareAction = async () => {
    setIsLoading(true);
    try {
      const result = await onShare();
      if (result.success && result.shareToken) {
        const url = `${window.location.origin}/share/${result.shareToken}`;
        setShareUrl(url);
      } else {
        throw new Error("Failed to generate share link");
      }
    } catch (error) {
      console.error("Share error:", error);
      toast.error("Failed to generate share link");
      onClose();
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setIsCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error("Copy error:", error);
      toast.error("Failed to copy link");
    }
  };

  const handleRegenerateToken = async () => {
    if (!contentId) {
      toast.error("Content ID is missing");
      return;
    }

    setIsLoading(true);
    try {
      const result = await generateShareToken(contentId, true); // Force regenerate

      if (result.success && result.shareToken) {
        const url = `${window.location.origin}/share/${result.shareToken}`;
        setShareUrl(url);
        onTokenRegenerated?.(result.shareToken);
        toast.success("New share link generated!");
      } else {
        throw new Error("Failed to regenerate share link");
      }
    } catch (error) {
      console.error("Regenerate error:", error);
      toast.error("Failed to regenerate share link");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnshareAction = async () => {
    if (!contentId) {
      toast.error("Content ID is missing");
      return;
    }

    setIsLoading(true);
    try {
      const result = await handleUnshare(contentId);

      if (result.success) {
        toast.success("Share link removed successfully");
        setShareUrl("");
        onClose();
      } else {
        throw new Error("Failed to remove share link");
      }
    } catch (error) {
      console.error("Unshare error:", error);
      toast.error("Failed to remove share link");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setShareUrl("");
    setIsCopied(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Share2 className="h-5 w-5" />
            Share Summary
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-4">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-8 gap-3">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <p className="text-sm text-muted-foreground">
                {shareUrl
                  ? "Removing share link..."
                  : "Generating share link..."}
              </p>
            </div>
          ) : shareUrl ? (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Share this link with others to give them access to your summary:
              </p>
              <div className="flex items-center gap-2">
                <Input value={shareUrl} readOnly className="flex-1" />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={copyToClipboard}
                  className="shrink-0"
                  title="Copy link"
                >
                  {isCopied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={handleRegenerateToken}
                  disabled={isLoading}
                  className="shrink-0"
                  title="Generate new link"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex justify-end mt-4">
                <Button
                  variant="destructive"
                  onClick={handleUnshareAction}
                  disabled={isLoading}
                >
                  Unshare
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                Create a shareable link for: <br />
                <span className="font-medium text-foreground">{title}</span>
              </p>
              <div className="flex justify-end">
                <Button onClick={handleShareAction}>Generate Share Link</Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
