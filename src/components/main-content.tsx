"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>he<PERSON> } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card } from "@/components/ui/card";

import { useEffect, useState } from "react";

import Chat from "@/components/Chat";
import Quiz from "./Quiz";
import Flashcards from "./Flashcards";
import { ContentDisplay } from "@/components/content-display";

interface MainContentProps {
  contentId: string;
}

export function MainContent({ contentId }: MainContentProps) {
  const [headerHeight, setHeaderHeight] = useState(0);

  useEffect(() => {
    const updateHeaderHeight = () => {
      const headerElement = document.querySelector("header");
      if (headerElement) {
        setHeaderHeight(headerElement.offsetHeight);
      }
    };

    updateHeaderHeight();
    window.addEventListener("resize", updateHeaderHeight);

    const resizeObserver = new ResizeObserver(updateHeaderHeight);
    const headerElement = document.querySelector("header");
    if (headerElement) {
      resizeObserver.observe(headerElement);
    }

    document.documentElement.style.setProperty(
      "--header-height",
      `${headerHeight}px`
    );

    return () => {
      window.removeEventListener("resize", updateHeaderHeight);
      if (headerElement) {
        resizeObserver.unobserve(headerElement);
      }
    };
  }, [headerHeight]);

  return (
    <div className="flex-1 overflow-y-auto h-full relative">
      <div className="w-full max-w-full mx-auto">
        <div className="w-full mx-auto px-4">
          <Tabs defaultValue="content" className="w-full">
            <div
              className="sticky-tabs bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 w-full"
              style={{
                position: "sticky",
                top: `${headerHeight}px`,
                zIndex: 20,
              }}
            >
              <div className="md:hidden border-y w-full">
                <TabsList className="w-full grid grid-cols-4 h-16 bg-transparent">
                  <TabsTrigger
                    value="content"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full cursor-pointer"
                  >
                    <FileText className="h-5 w-5" />
                    <span className="text-xs">Content</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="chat"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full cursor-pointer"
                  >
                    <MessageSquare className="h-5 w-5" />
                    <span className="text-xs">Chat</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="quiz"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full cursor-pointer"
                  >
                    <Brain className="h-5 w-5" />
                    <span className="text-xs">Quiz</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="flashcards"
                    className="flex flex-col items-center justify-center data-[state=active]:text-primary gap-1 h-full cursor-pointer"
                  >
                    <BookOpenCheck className="h-5 w-5" />
                    <span className="text-xs">Cards</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              <div className="hidden md:block border-b">
                <TabsList className="w-full h-12 bg-transparent">
                  <div className="flex max-w-[800px] mx-auto w-full">
                    <TabsTrigger
                      value="content"
                      className="flex-1 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none h-12 px-6 cursor-pointer"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Content
                    </TabsTrigger>
                    <TabsTrigger
                      value="flashcards"
                      className="flex-1 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none h-12 px-6 cursor-pointer"
                    >
                      <BookOpenCheck className="h-4 w-4 mr-2" />
                      Flashcards
                    </TabsTrigger>
                    <TabsTrigger
                      value="quiz"
                      className="flex-1 data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none h-12 px-6 cursor-pointer"
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Quiz
                    </TabsTrigger>
                  </div>
                </TabsList>
              </div>
            </div>

            <div className="md:pb-0">
              <TabsContent
                value="content"
                className="mt-4 focus-visible:outline-none overflow-x-hidden"
              >
                <Card className="border-0 shadow-none">
                  <ContentDisplay />
                </Card>
              </TabsContent>
              <TabsContent
                value="chat"
                className="mt-0 focus-visible:outline-none md:hidden"
              >
                <div className="flex-1 flex flex-col h-[calc(100vh-var(--header-height)-4rem)] overflow-hidden">
                  <Chat />
                </div>
              </TabsContent>
              <TabsContent
                value="quiz"
                className="mt-4 focus-visible:outline-none md:hidden"
              >
                <Card className="border-0 shadow-none">
                  <Quiz />
                </Card>
              </TabsContent>
              <TabsContent
                value="flashcards"
                className="mt-4 focus-visible:outline-none md:hidden"
              >
                <Card className="border-0 shadow-none">
                  <Flashcards />
                </Card>
              </TabsContent>
              <TabsContent
                value="flashcards"
                className="mt-4 focus-visible:outline-none hidden md:block"
              >
                <Card className="border-0 shadow-none">
                  <Flashcards />
                </Card>
              </TabsContent>
              <TabsContent
                value="quiz"
                className="mt-4 focus-visible:outline-none hidden md:block"
              >
                <Card className="border-0 shadow-none">
                  <Quiz />
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
