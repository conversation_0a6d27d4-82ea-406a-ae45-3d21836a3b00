"use client";

import React from "react";

import { useTransformationsStore } from "@/store/transformationsStore";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Clock,
  Trash2,
  AlertTriangle,
  Loader2,
  MoreVertical,
  Share2,
  File, // PDF icon
  Video, // YouTube icon
  BookText, // Text icon
  Globe, // Webpage icon
} from "lucide-react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { useState, useMemo, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { Separator } from "@/components/ui/separator";
import { EmptyState } from "./EmptyState";
import { cn } from "@/lib/utils";
import ShareModal from "../ShareModal";
import { OutputType } from "@/types/contentTypes";
import { ContentSummary } from "@/store/transformationsStore";
import { getOrGenerateShareToken } from "@/utils/share";

interface ContentGridProps {
  filter: OutputType | "all";
  currentPage: number;
  onPageChange: (page: number) => void;
}

export const ContentGrid = React.memo(function ContentGrid({
  filter,
  currentPage,
  onPageChange,
}: ContentGridProps) {
  const router = useRouter();
  const { content, deleteContent, fetchPageContent, isLoading, pagination } =
    useTransformationsStore();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState<boolean>(false);
  const [contentToDelete, setContentToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedContent, setSelectedContent] = useState<ContentSummary | null>(
    null
  );
  const itemsPerPage = pagination.pageSize;

  // Fetch data when page changes - handle both initial load and load more
  useEffect(() => {
    fetchPageContent(currentPage, itemsPerPage);
  }, [currentPage, fetchPageContent, itemsPerPage]);

  // Reset isDeleting when dialog closes
  useEffect(() => {
    if (!deleteDialogOpen) {
      setIsDeleting(false);
      setContentToDelete(null);
    }
  }, [deleteDialogOpen]);

  // Filter content based on selected filter - optimize with early return
  const filteredContent = useMemo(() => {
    if (filter === "all") return content;
    if (filter === "summary")
      return content.filter((item) => item.outputType === "summary");
    if (filter === "notes")
      return content.filter((item) => item.outputType === "notes");
    return content;
  }, [content, filter]);

  // Get content for current page - optimize pagination logic
  const paginatedContent = useMemo(() => {
    // If we're filtering content locally, slice the filtered content
    if (filter !== "all") {
      const start = (currentPage - 1) * itemsPerPage;
      return filteredContent.slice(start, start + itemsPerPage);
    }

    // For unfiltered content, use the content as is (already paginated from API)
    return filteredContent;
  }, [filteredContent, filter, currentPage, itemsPerPage]);

  const handleDeleteClick = (e: React.MouseEvent, contentId: string) => {
    e.stopPropagation();
    setIsDeleting(false);
    setContentToDelete(contentId);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async (e: React.MouseEvent) => {
    e.preventDefault();

    if (!contentToDelete) return;
    setIsDeleting(true);

    try {
      const success = await deleteContent(contentToDelete);
      if (success) {
        toast.success("The content and all associated data have been deleted.");
        setDeleteDialogOpen(false);
      } else {
        throw new Error("Delete operation did not return success");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Failed to delete the content. Please try again.");
      setIsDeleting(false);
    }
  };

  const handleDialogClose = (open: boolean) => {
    // Only allow closing if not in deleting state
    if (!isDeleting && !open) {
      setDeleteDialogOpen(false);
      setContentToDelete(null);
      setIsDeleting(false);
    }
  };

  // Function to handle share modal opening
  const openShareModal = (e: React.MouseEvent, content: ContentSummary) => {
    e.stopPropagation();
    setSelectedContent(content);
    setShareModalOpen(true);
  };

  // Function to handle share for the ShareModal component
  const handleShareContent = async () => {
    if (!selectedContent?.contentId) {
      toast.error("Invalid or missing Content ID.");
      return { success: false };
    }

    const result = await getOrGenerateShareToken(
      selectedContent.contentId,
      selectedContent.shareToken
    );

    // If successful, update the selectedContent with the shareToken
    if (result.success && selectedContent) {
      setSelectedContent({
        ...selectedContent,
        isShared: true,
        shareToken: result.shareToken,
      });
    }

    return result;
  };

  // Function to handle token regeneration
  const handleTokenRegenerated = (newToken: string) => {
    if (selectedContent) {
      setSelectedContent({
        ...selectedContent,
        shareToken: newToken,
        isShared: true,
      });
    }
  };

  // Function to get card background color based on content type
  const getCardBackground = (contentType: string) => {
    switch (contentType?.toLowerCase()) {
      case "youtube":
        return "bg-gradient-to-br from-red-500 to-rose-600";
      case "text":
        return "bg-gradient-to-br from-indigo-500 to-purple-600";
      case "webpage":
        return "bg-gradient-to-br from-blue-500 to-cyan-600";
      case "pdf":
        return "bg-gradient-to-br from-orange-500 to-amber-600";
      default:
        return "bg-gradient-to-br from-gray-500 to-slate-600";
    }
  };

  // Function to get content type icon
  const getContentTypeIcon = (contentType: string) => {
    switch (contentType?.toLowerCase()) {
      case "youtube":
        return <Video className="h-16 w-16 text-white/80" />;
      case "pdf":
        return <File className="h-16 w-16 text-white/80" />;
      case "text":
        return <BookText className="h-16 w-16 text-white/80" />;
      case "webpage":
        return <Globe className="h-16 w-16 text-white/80" />;
      default:
        return <FileText className="h-16 w-16 text-white/80" />;
    }
  };

  // Function to navigate to the correct transform page with content type
  const navigateToTransform = (item: ContentSummary) => {
    router.push(
      `/transform/${
        item.contentId
      }?type=${item.contentType.toLowerCase()}&output=${item.outputType}`
    );
  };

  if (filteredContent.length === 0) {
    return <EmptyState filter={filter} />;
  }

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-4 gap-6">
        {isLoading && paginatedContent.length === 0
          ? // Show loading placeholders
            Array.from({ length: 6 }).map((_, index) => (
              <Card
                key={`loading-${index}`}
                className="overflow-hidden flex flex-col border border-border/60 animate-pulse h-full"
              >
                {/* Content placeholder at the top */}
                <div className="h-32 bg-muted"></div>
                {/* Title placeholder */}
                <div className="p-4 pb-2">
                  <div className="h-5 bg-muted rounded w-3/4"></div>
                </div>
                {/* Footer placeholder */}
                <div className="p-4 pt-3 mt-auto">
                  <div className="h-px bg-muted mb-3"></div>
                  <div className="flex items-center justify-between">
                    <div className="h-8 bg-muted rounded w-24"></div>
                    <div className="h-4 bg-muted rounded w-16"></div>
                  </div>
                </div>
              </Card>
            ))
          : // Show actual content
            paginatedContent.map((item) => (
              <Card
                key={item.id}
                className="overflow-hidden group flex flex-col hover:shadow-md transition-all duration-300 border border-border/60 hover:border-primary/20 cursor-pointer h-full"
                onClick={() => navigateToTransform(item)}
              >
                {/* Card content with solid background color at the top */}
                <div
                  className={cn(
                    "relative h-32 p-6 flex items-center justify-center",
                    getCardBackground(item.contentType)
                  )}
                >
                  {/* Content type icon */}
                  {getContentTypeIcon(item.contentType)}

                  {/* Output type badge */}
                  <div className="absolute top-2 left-2">
                    <Badge
                      variant="secondary"
                      className="bg-white/20 text-white text-xs font-medium backdrop-blur-sm"
                    >
                      <Clock className="w-3 h-3 mr-1" />
                      {item.outputType}
                    </Badge>
                  </div>

                  {/* Actions dropdown */}
                  <div className="absolute top-2 right-2">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="secondary"
                          size="icon"
                          className="h-7 w-7 rounded-full bg-white/20 hover:bg-white/30 transition-colors"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreVertical className="h-3.5 w-3.5 text-white" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToTransform(item);
                          }}
                        >
                          <FileText className="h-4 w-4" />
                          View Summary
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2 cursor-pointer"
                          onClick={(e) => openShareModal(e, item)}
                        >
                          <Share2 className="h-4 w-4" />
                          Share Summary
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="flex items-center gap-2 text-destructive focus:text-destructive cursor-pointer"
                          onClick={(e) => handleDeleteClick(e, item.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>

                {/* Title section below the banner */}
                <div className="p-4 pb-2">
                  <h2 className="text-lg font-bold tracking-tight line-clamp-2">
                    {item.contentType.toLowerCase() === "text" &&
                    (item.title.startsWith("##") || item.title.startsWith("**"))
                      ? item.title
                          .replace(/^(##|\*\*)\s*/, "")
                          .replace(/(\*\*)$/, "")
                          .trim()
                      : item.title}
                  </h2>
                </div>

                {/* Card footer with date and view button */}
                <div className="p-4 pt-3 mt-auto">
                  <Separator className="mb-3" />
                  <div className="flex items-center justify-between">
                    <Button
                      variant="default"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigateToTransform(item);
                      }}
                      className="transition-all hover:translate-x-1"
                    >
                      <FileText className="h-4 w-4 mr-1" />
                      {item.outputType === "summary"
                        ? "View Summary"
                        : "View Notes"}
                    </Button>
                    <p className="text-sm text-muted-foreground">
                      {format(new Date(item.generatedAt), "MMM d, yyyy")}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
      </div>

      {/* Load More Button */}
      {pagination.hasMore && (
        <div className="mt-8 flex justify-center">
          <Button
            variant="outline"
            size="lg"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={pagination.isLoadingMore}
            className="min-w-[200px] gap-2"
          >
            {pagination.isLoadingMore ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>Load More</>
            )}
          </Button>
        </div>
      )}

      {/* Loading indicator for initial load */}
      {isLoading && !content.length && (
        <div className="mt-8 flex justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={handleDialogClose}>
        <AlertDialogContent className="max-w-[90%] sm:max-w-[425px] md:max-w-[550px] rounded-lg">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Confirm Deletion
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this content and all associated data
              including summaries, quizzes, flashcards, and chat history. This
              action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <Button
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90 flex items-center gap-2"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4" />
                  Delete
                </>
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <ShareModal
        isOpen={shareModalOpen}
        onClose={() => {
          setShareModalOpen(false);
          setSelectedContent(null);
        }}
        onShare={handleShareContent}
        title={selectedContent?.title || ""}
        contentId={selectedContent?.contentId}
        existingShareToken={selectedContent?.shareToken}
        onTokenRegenerated={handleTokenRegenerated}
      />
    </div>
  );
});
