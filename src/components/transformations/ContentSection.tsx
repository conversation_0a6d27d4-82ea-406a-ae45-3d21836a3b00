"use client";

import { ContentFilters } from "./ContentFilters";
import { ContentGrid } from "@/components/transformations/ContentGrid";
import { useState, useEffect } from "react";
import { useTransformationsStore } from "@/store/transformationsStore";

// Define type for filter values to match ContentFilters
type FilterValue = "all" | "summary" | "notes";

export function ContentSection() {
  const [filter, setFilter] = useState<FilterValue>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const { resetPagination } = useTransformationsStore();

  // Reset to page 1 and clear pagination when filter changes
  useEffect(() => {
    setCurrentPage(1);
    resetPagination();
  }, [filter, resetPagination]);

  // Handle loading more content
  const handleLoadMore = (nextPage: number) => {
    // For "Load More" functionality, we don't reset the page
    // but increment it to load the next batch of content
    setCurrentPage(nextPage);
  };

  return (
    <div className="space-y-6">
      <ContentFilters activeFilter={filter} onFilterChange={setFilter} />

      <ContentGrid
        filter={filter}
        currentPage={currentPage}
        onPageChange={handleLoadMore}
      />
    </div>
  );
}
