generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model ContentSummary {
  id                  String        @id @default(uuid())
  contentType         ContentType
  contentId           String        @unique
  title               String
  content             String
  originalContent     String
  sourceId            String
  generatedAt         DateTime      @default(now())
  userId              String
  translatedContent   String?
  translationLanguage String?
  isShared            Boolean       @default(false)
  shareToken          String?       @unique
  outputType          String
  chats               ChatMessage[]
  flashcards          Flashcard[]
  quizzes             Quiz[]

  @@unique([userId, sourceId, contentType])
  @@index([userId])
  @@index([contentType])
  @@index([contentId])
}

model Quiz {
  id               String         @id @default(uuid())
  userId           String
  questions        Json
  createdAt        DateTime       @default(now())
  contentSummaryId String
  contentSummary   ContentSummary @relation(fields: [contentSummaryId], references: [id], onDelete: Cascade)
  attempts         QuizAttempt[]

  @@index([contentSummaryId])
  @@index([userId])
}

model QuizAttempt {
  id        String   @id @default(uuid())
  quizId    String
  userId    String
  answers   Json
  score     Int
  createdAt DateTime @default(now())
  quiz      Quiz     @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@index([quizId])
  @@index([userId])
}

model Flashcard {
  id               String         @id @default(uuid())
  userId           String
  cards            Json
  createdAt        DateTime       @default(now())
  studyProgress    Json?
  contentSummaryId String
  contentSummary   ContentSummary @relation(fields: [contentSummaryId], references: [id], onDelete: Cascade)

  @@index([contentSummaryId])
  @@index([userId])
}

model ChatMessage {
  id               String         @id @default(uuid())
  userId           String
  role             String
  content          String
  createdAt        DateTime       @default(now())
  contentSummaryId String
  contentSummary   ContentSummary @relation(fields: [contentSummaryId], references: [id], onDelete: Cascade)

  @@index([contentSummaryId])
  @@index([userId])
}

model User {
  id            String         @id
  email         String?        @unique
  displayName   String?
  avatarUrl     String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  subscriptions Subscription[]

  @@map("users")
}

model Subscription {
  id                       String             @id @default(uuid())
  userId                   String
  subscriptionId           String             @unique
  customerId               String?
  planType                 PlanType
  status                   SubscriptionStatus
  currentPeriodStart       DateTime
  currentPeriodEnd         DateTime
  billingCycle             String
  cancelAtPeriodEnd        Boolean            @default(false)
  canceledAt               DateTime?
  priceId                  String
  quantity                 Int                @default(1)
  metadata                 Json?
  createdAt                DateTime           @default(now())
  updatedAt                DateTime           @updatedAt
  pendingPlanType          PlanType?
  pendingPlanEffectiveDate DateTime?
  pendingPriceId           String?
  emailToken               String?
  user                     User               @relation(fields: [userId], references: [id], onDelete: Cascade)
  UsageRecord              UsageRecord[]

  @@index([userId])
  @@index([subscriptionId])
  @@index([status])
  @@index([planType])
}

model UsageRecord {
  id             String          @id @default(uuid())
  userId         String
  subscriptionId String?
  feature        String
  count          Int             @default(0)
  resetDate      DateTime
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  lastUsedAt     DateTime        @default(now())
  metadata       Json?
  auditLogs      UsageAuditLog[]
  Subscription   Subscription?   @relation(fields: [subscriptionId], references: [id])

  @@unique([userId, feature, resetDate], name: "userId_feature_resetDate")
  @@index([userId])
  @@index([feature])
  @@index([resetDate])
}

model UsageAuditLog {
  id            String      @id @default(uuid())
  usageRecordId String
  userId        String
  feature       String
  action        String
  previousCount Int
  newCount      Int
  metadata      Json?
  timestamp     DateTime    @default(now())
  usageRecord   UsageRecord @relation(fields: [usageRecordId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([feature])
  @@index([timestamp])
  @@index([action])
}

model Product {
  id          String   @id @default(uuid())
  productId   String   @unique
  planType    PlanType
  name        String
  description String?
  price       Float
  currency    String   @default("USD")
  interval    String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([planType])
  @@index([isActive])
  @@index([interval])
}

enum PlanType {
  Free
  Starter
  Pro
  Unlimited
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  NON_RENEWING
  ATTENTION
  COMPLETED
  CANCELLED
}

enum ContentType {
  YOUTUBE
  PDF
  TEXT
  WEBPAGE
}
