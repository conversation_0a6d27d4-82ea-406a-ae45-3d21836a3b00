/*
  Warnings:

  - A unique constraint covering the columns `[userId,feature,resetDate]` on the table `UsageRecord` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "UsageRecord" DROP CONSTRAINT "UsageRecord_subscriptionId_fkey";

-- DropIndex
DROP INDEX "UsageRecord_subscriptionId_idx";

-- DropIndex
DROP INDEX "UsageRecord_userId_subscriptionId_feature_resetDate_key";

-- AlterTable
ALTER TABLE "UsageRecord" ADD COLUMN     "lastUsedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "metadata" JSONB;

-- CreateTable
CREATE TABLE "UsageAuditLog" (
    "id" TEXT NOT NULL,
    "usageRecordId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "feature" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "previousCount" INTEGER NOT NULL,
    "newCount" INTEGER NOT NULL,
    "metadata" JSONB,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UsageAuditLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UsageAuditLog_userId_idx" ON "UsageAuditLog"("userId");

-- CreateIndex
CREATE INDEX "UsageAuditLog_feature_idx" ON "UsageAuditLog"("feature");

-- CreateIndex
CREATE INDEX "UsageAuditLog_timestamp_idx" ON "UsageAuditLog"("timestamp");

-- CreateIndex
CREATE INDEX "UsageAuditLog_action_idx" ON "UsageAuditLog"("action");

-- CreateIndex
CREATE UNIQUE INDEX "UsageRecord_userId_feature_resetDate_key" ON "UsageRecord"("userId", "feature", "resetDate");

-- AddForeignKey
ALTER TABLE "UsageRecord" ADD CONSTRAINT "UsageRecord_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "UsageAuditLog" ADD CONSTRAINT "UsageAuditLog_usageRecordId_fkey" FOREIGN KEY ("usageRecordId") REFERENCES "UsageRecord"("id") ON DELETE CASCADE ON UPDATE CASCADE;
