# Paystack Webhook Setup Guide

This guide explains how to set up Paystack webhooks for your application to handle subscription events properly.

## Overview

The Paystack webhook endpoint is located at `/api/webhooks/paystack` and handles the following events:

- `charge.success` - Successful payments
- `subscription.create` - New subscription created
- `subscription.not_renew` - Subscription cancelled (will not renew)
- `subscription.disable` - Subscription fully disabled
- `invoice.create` - Invoice created
- `invoice.payment_failed` - Payment failed
- `invoice.update` - Invoice updated

## Setup Instructions

### 1. Configure Webhook URL in Paystack Dashboard

1. Log in to your [Paystack Dashboard](https://dashboard.paystack.com/)
2. Navigate to **Settings** → **Webhooks**
3. Add your webhook URL:
   - **Test Environment**: `https://your-domain.com/api/webhooks/paystack`
   - **Live Environment**: `https://your-domain.com/api/webhooks/paystack`

### 2. Environment Variables

Ensure you have the following environment variable set:

```env
PAYSTACK_SECRET_KEY=sk_test_xxx... # or sk_live_xxx... for production
```

### 3. Webhook Security

The webhook endpoint automatically:
- Validates the `x-paystack-signature` header using HMAC SHA512
- Rejects requests with invalid signatures
- Logs all webhook events for debugging

### 4. Supported Events

#### Subscription Events

- **subscription.not_renew**: Triggered when a subscription is cancelled but still active until the end of the current period
  - Sets subscription status to `NON_RENEWING`
  - Sets `cancelAtPeriodEnd` to `true`

- **subscription.disable**: Triggered when a subscription is fully disabled
  - Sets subscription status to `CANCELLED`
  - Sets `canceledAt` to current timestamp

- **subscription.create**: Triggered when a new subscription is created
  - Activates pending subscriptions
  - Stores the `email_token` for future operations

#### Payment Events

- **charge.success**: Handles successful payments
  - Updates subscription payment metadata
  - Activates subscriptions for first-time payments

- **invoice.payment_failed**: Handles failed payments
  - Sets subscription status to `ATTENTION`
  - Logs failure reason

### 5. Testing

To test the webhook:

1. Use Paystack's webhook testing tool in the dashboard
2. Create test subscriptions and cancel them
3. Check your application logs for webhook processing messages
4. Verify subscription statuses are updated correctly

### 6. Monitoring

The webhook logs detailed information for debugging:
- Event types received
- Signature validation results
- Database updates performed
- Any errors encountered

Check your application logs for messages starting with:
- `🔔 Paystack webhook received`
- `✅ Paystack webhook signature verified`
- `📦 Processing Paystack webhook event`

### 7. Error Handling

The webhook returns appropriate HTTP status codes:
- `200 OK` - Event processed successfully
- `401 Unauthorized` - Invalid or missing signature
- `500 Internal Server Error` - Processing error

Paystack will retry failed webhooks according to their retry policy.

## Differences from Previous Implementation

This new Paystack webhook replaces the old "dodo" webhook system and provides:

1. **Proper signature validation** using Paystack's HMAC SHA512 method
2. **Comprehensive event handling** for all subscription lifecycle events
3. **Better error handling** and logging
4. **Correct subscription status mapping** according to Paystack's event system

## Important Notes

- The webhook endpoint uses your `PAYSTACK_SECRET_KEY` for signature validation
- Make sure your webhook URL is publicly accessible (no localhost URLs in production)
- Paystack sends webhooks for both test and live environments
- The webhook handles both subscription and one-time payment events

## Troubleshooting

### Common Issues

1. **Invalid signature errors**
   - Verify `PAYSTACK_SECRET_KEY` is correct
   - Check that the webhook URL matches exactly

2. **Subscription not updating**
   - Check logs for webhook events being received
   - Verify subscription IDs match between Paystack and your database

3. **Webhook not receiving events**
   - Ensure webhook URL is publicly accessible
   - Check Paystack dashboard for webhook delivery status
   - Verify SSL certificate is valid

For more information, refer to the [Paystack Webhooks Documentation](https://paystack.com/docs/payments/webhooks/).