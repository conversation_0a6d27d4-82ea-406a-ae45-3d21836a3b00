# Dodo Payments Subscription Implementation Guide

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Database Schema Updates](#database-schema-updates)
4. [Environment Variables](#environment-variables)
5. [Installation & Setup](#installation--setup)
6. [Product Creation](#product-creation)
7. [Subscription Flow Implementation](#subscription-flow-implementation)
8. [Webhook Implementation](#webhook-implementation)
9. [Customer Portal Integration](#customer-portal-integration)
10. [Plan Management](#plan-management)
11. [Testing Strategy](#testing-strategy)
12. [Cost Analysis](#cost-analysis)
13. [Security Considerations](#security-considerations)
14. [Implementation Checklist](#implementation-checklist)

## Overview

Dodo Payments is a comprehensive payment platform that supports subscription billing with features like:

- Recurring billing automation
- Customer portal for self-service
- Plan upgrades/downgrades with proration
- Global payment methods (150+ countries)
- Built-in tax compliance
- Webhook-based event handling

### Key Benefits for Qlipify

- **Lower fees**: 4% + $0.40 (vs Stripe's 2.9% + $0.30 but with additional subscription fees)
- **Built-in subscription management**: No need for additional tools
- **Global reach**: Support for international customers
- **Customer portal**: Self-service subscription management
- **Proration handling**: Automatic upgrade/downgrade calculations

## Prerequisites

### Business Requirements

1. **Dodo Payments Account**: Contact founder or fill [early access form](https://dodopayments.com/early-access)
2. **Business Verification**: Complete verification process for live mode
3. **API Keys**: Obtain test and live API keys from dashboard

### Technical Requirements

- Node.js 18+ (already met)
- Next.js 13+ (already met)
- PostgreSQL database (already met)
- Prisma ORM (already met)

## Database Schema Updates

Add the following models to your `prisma/schema.prisma`:

```prisma
model Subscription {
  id                    String            @id @default(uuid())
  userId                String
  dodoSubscriptionId    String            @unique
  dodoCustomerId        String?
  planType              PlanType
  status                SubscriptionStatus
  currentPeriodStart    DateTime
  currentPeriodEnd      DateTime
  cancelAtPeriodEnd     Boolean           @default(false)
  canceledAt            DateTime?
  trialEnd              DateTime?
  priceId               String
  quantity              Int               @default(1)
  metadata              Json?
  createdAt             DateTime          @default(now())
  updatedAt             DateTime          @updatedAt

  // Relations
  user                  User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  usageRecords          UsageRecord[]

  @@index([userId])
  @@index([dodoSubscriptionId])
  @@index([status])
}

model UsageRecord {
  id             String       @id @default(uuid())
  userId         String
  subscriptionId String
  feature        String       // 'transformations', 'quizzes', 'flashcards', 'ai_chat'
  count          Int          @default(0)
  resetDate      DateTime     // When the counter resets (weekly/monthly)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  subscription   Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)

  @@unique([userId, subscriptionId, feature, resetDate])
  @@index([userId])
  @@index([subscriptionId])
}

model DodoProduct {
  id          String   @id @default(uuid())
  dodoId      String   @unique
  planType    PlanType
  name        String
  description String?
  price       Float
  currency    String   @default("USD")
  interval    String   // 'month' or 'year'
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([planType])
  @@index([isActive])
}

enum SubscriptionStatus {
  PENDING
  ACTIVE
  ON_HOLD
  PAUSED
  CANCELLED
  FAILED
  EXPIRED
  RENEWED
}

// Update existing User model
model User {
  id            String         @id
  email         String?        @unique
  displayName   String?
  avatarUrl     String?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // Add subscription relation
  subscriptions Subscription[]

  @@map("users")
}
```

## Environment Variables

Add to your `.env.local`:

```env
# Dodo Payments Configuration
DODO_PAYMENTS_API_KEY=your_test_api_key_here
DODO_PAYMENTS_WEBHOOK_SECRET=your_webhook_secret_here
DODO_PAYMENTS_ENVIRONMENT=test_mode  # or 'live_mode' for production

# URLs
NEXT_PUBLIC_DODO_TEST_API=https://api.dodopayments.com/v1
NEXT_PUBLIC_RETURN_URL=http://localhost:3000/subscription/success
NEXT_PUBLIC_CANCEL_URL=http://localhost:3000/subscription/cancel

# Webhook endpoint
WEBHOOK_URL=https://yourdomain.com/api/webhooks/dodo
```

## Installation & Setup

### 1. Install Dodo Payments SDK

```bash
npm install dodopayments
```

### 2. Create Dodo Client Utility

Create `src/lib/dodo-payments.ts`:

```typescript
import DodoPayments from "dodopayments";

if (!process.env.DODO_PAYMENTS_API_KEY) {
  throw new Error("DODO_PAYMENTS_API_KEY is required");
}

export const dodoClient = new DodoPayments({
  bearerToken: process.env.DODO_PAYMENTS_API_KEY,
  environment:
    (process.env.DODO_PAYMENTS_ENVIRONMENT as "test_mode" | "live_mode") ||
    "test_mode",
});

export default dodoClient;
```

## Product Creation

### 1. Create Products in Dodo Dashboard

You'll need to create products for each plan in your Dodo Payments dashboard:

**Starter Plan (Monthly)**: $5.99/month
**Starter Plan (Annual)**: $58/year
**Pro Plan (Monthly)**: $11.99/month
**Pro Plan (Annual)**: $115/year
**Unlimited Plan (Monthly)**: $24.99/month
**Unlimited Plan (Annual)**: $240/year

### 2. Store Product IDs

Create `src/lib/subscription-config.ts`:

```typescript
import { PlanType } from "@/data/pricingData";

export const DODO_PRODUCT_IDS = {
  starter_monthly: "prod_starter_monthly_id",
  starter_annual: "prod_starter_annual_id",
  pro_monthly: "prod_pro_monthly_id",
  pro_annual: "prod_pro_annual_id",
  unlimited_monthly: "prod_unlimited_monthly_id",
  unlimited_annual: "prod_unlimited_annual_id",
} as const;

export const getPlanLimits = (planType: PlanType) => {
  switch (planType) {
    case "Free":
      return {
        transformations: { weekly: 2 },
        quizzes: { perContent: 1 },
        flashcards: { perContent: 1 },
        aiChat: { daily: 10 },
        sharing: false,
        translation: false,
      };
    case "Starter":
      return {
        transformations: { weekly: 10 },
        quizzes: { perContent: 3 },
        flashcards: { perContent: 3 },
        aiChat: { unlimited: true },
        sharing: true,
        translation: true,
      };
    case "Pro":
      return {
        transformations: { weekly: 20 },
        quizzes: { unlimited: true },
        flashcards: { unlimited: true },
        aiChat: { unlimited: true },
        sharing: true,
        translation: true,
        analytics: true,
      };
    case "Unlimited":
      return {
        transformations: { unlimited: true },
        quizzes: { unlimited: true },
        flashcards: { unlimited: true },
        aiChat: { unlimited: true },
        sharing: true,
        translation: true,
        analytics: true,
        priority: true,
      };
    default:
      return getPlanLimits("Free");
  }
};
```

## Subscription Flow Implementation

### 1. Create Subscription API Route

Create `src/app/api/subscriptions/create/route.ts`:

```typescript
import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { dodoClient } from "@/lib/dodo-payments";
import { prisma } from "@/lib/prisma";
import { DODO_PRODUCT_IDS } from "@/lib/subscription-config";
import { PlanType } from "@/data/pricingData";

interface CreateSubscriptionRequest {
  planType: PlanType;
  billingCycle: "monthly" | "annual";
  customerInfo: {
    email: string;
    name: string;
    phone?: string;
  };
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipcode: string;
    country: string;
  };
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body: CreateSubscriptionRequest = await request.json();
    const { planType, billingCycle, customerInfo, billingAddress } = body;

    // Get product ID
    const productKey =
      `${planType.toLowerCase()}_${billingCycle}` as keyof typeof DODO_PRODUCT_IDS;
    const productId = DODO_PRODUCT_IDS[productKey];

    if (!productId) {
      return NextResponse.json(
        { error: "Invalid plan selected" },
        { status: 400 }
      );
    }

    // Create subscription with Dodo Payments
    const subscription = await dodoClient.subscriptions.create({
      billing: {
        city: billingAddress.city,
        country: billingAddress.country,
        state: billingAddress.state,
        street: billingAddress.street,
        zipcode: parseInt(billingAddress.zipcode),
      },
      customer: {
        email: customerInfo.email,
        name: customerInfo.name,
        phone_number: customerInfo.phone,
      },
      product_id: productId,
      payment_link: true,
      return_url: process.env.NEXT_PUBLIC_RETURN_URL,
      quantity: 1,
      metadata: {
        userId: user.id,
        planType,
        billingCycle,
      },
    });

    // Store subscription in database (pending status)
    await prisma.subscription.create({
      data: {
        userId: user.id,
        dodoSubscriptionId: subscription.subscription_id,
        dodoCustomerId: subscription.customer.customer_id,
        planType: planType as PlanType,
        status: "PENDING",
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(
          Date.now() +
            (billingCycle === "annual" ? 365 : 30) * 24 * 60 * 60 * 1000
        ),
        priceId: productId,
        metadata: {
          billingCycle,
          dodoPaymentLink: subscription.payment_link,
        },
      },
    });

    return NextResponse.json({
      paymentLink: subscription.payment_link,
      subscriptionId: subscription.subscription_id,
    });
  } catch (error) {
    console.error("Subscription creation error:", error);
    return NextResponse.json(
      { error: "Failed to create subscription" },
      { status: 500 }
    );
  }
}
```

### 2. Update Upgrade Page

Update `src/app/upgrade/page.tsx` to integrate with Dodo Payments:

```typescript
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import pricingData, { PlanType } from "@/data/pricingData";

export default function UpgradePage() {
  const [paymentOption, setPaymentOption] = useState<"monthly" | "annual">(
    "monthly"
  );
  const [loading, setLoading] = useState<string | null>(null);

  const handleProceed = async (plan: PlanType) => {
    if (plan === "Free") return;

    setLoading(plan);

    try {
      // Collect customer information (you might want to use a form/modal)
      const customerInfo = {
        email: "<EMAIL>", // Get from auth context
        name: "User Name", // Get from auth context
      };

      const billingAddress = {
        street: "123 Main St",
        city: "City",
        state: "State",
        zipcode: "12345",
        country: "US",
      };

      const response = await fetch("/api/subscriptions/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          planType: plan,
          billingCycle: paymentOption,
          customerInfo,
          billingAddress,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Redirect to Dodo Payments checkout
        window.location.href = data.paymentLink;
      } else {
        toast.error(data.error || "Failed to create subscription");
      }
    } catch (error) {
      toast.error("An error occurred. Please try again.");
    } finally {
      setLoading(null);
    }
  };

  // Rest of your existing component code...
  // Just update the button to call handleProceed with loading state
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Your existing UI */}
      <Button onClick={() => handleProceed(plan)} disabled={loading === plan}>
        {loading === plan ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          `Upgrade to ${plan}`
        )}
      </Button>
    </div>
  );
}
```

## Webhook Implementation

### 1. Create Webhook Handler

Create `src/app/api/webhooks/dodo/route.ts`:

```typescript
import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { prisma } from "@/lib/prisma";
import crypto from "crypto";

// Webhook signature verification
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  const expectedSignature = crypto
    .createHmac("sha256", secret)
    .update(payload)
    .digest("hex");

  return crypto.timingSafeEqual(
    Buffer.from(signature, "hex"),
    Buffer.from(expectedSignature, "hex")
  );
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get("dodo-signature");

    if (!signature || !process.env.DODO_PAYMENTS_WEBHOOK_SECRET) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
    }

    // Verify webhook signature
    const isValid = verifyWebhookSignature(
      body,
      signature,
      process.env.DODO_PAYMENTS_WEBHOOK_SECRET
    );

    if (!isValid) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
    }

    const event = JSON.parse(body);

    console.log("Received Dodo webhook:", event.type, event.data);

    switch (event.type) {
      case "subscription.active":
        await handleSubscriptionActive(event.data);
        break;

      case "subscription.on_hold":
        await handleSubscriptionOnHold(event.data);
        break;

      case "subscription.failed":
        await handleSubscriptionFailed(event.data);
        break;

      case "subscription.renewed":
        await handleSubscriptionRenewed(event.data);
        break;

      case "subscription.cancelled":
        await handleSubscriptionCancelled(event.data);
        break;

      case "payment.succeeded":
        await handlePaymentSucceeded(event.data);
        break;

      case "payment.failed":
        await handlePaymentFailed(event.data);
        break;

      default:
        console.log("Unhandled webhook event:", event.type);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook error:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}

async function handleSubscriptionActive(data: any) {
  await prisma.subscription.update({
    where: { dodoSubscriptionId: data.subscription_id },
    data: {
      status: "ACTIVE",
      currentPeriodStart: new Date(data.created_at),
      currentPeriodEnd: new Date(data.next_billing_date),
    },
  });

  // Initialize usage records for the new billing period
  const subscription = await prisma.subscription.findUnique({
    where: { dodoSubscriptionId: data.subscription_id },
  });

  if (subscription) {
    await initializeUsageRecords(subscription.id, subscription.userId);
  }
}

async function handleSubscriptionOnHold(data: any) {
  await prisma.subscription.update({
    where: { dodoSubscriptionId: data.subscription_id },
    data: { status: "ON_HOLD" },
  });
}

async function handleSubscriptionFailed(data: any) {
  await prisma.subscription.update({
    where: { dodoSubscriptionId: data.subscription_id },
    data: { status: "FAILED" },
  });
}

async function handleSubscriptionRenewed(data: any) {
  const subscription = await prisma.subscription.update({
    where: { dodoSubscriptionId: data.subscription_id },
    data: {
      status: "ACTIVE",
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(data.next_billing_date),
    },
  });

  // Reset usage records for new billing period
  await resetUsageRecords(subscription.id, subscription.userId);
}

async function handleSubscriptionCancelled(data: any) {
  await prisma.subscription.update({
    where: { dodoSubscriptionId: data.subscription_id },
    data: {
      status: "CANCELLED",
      canceledAt: new Date(),
    },
  });
}

async function handlePaymentSucceeded(data: any) {
  // Log successful payment, update any payment-specific records
  console.log("Payment succeeded:", data);
}

async function handlePaymentFailed(data: any) {
  // Handle failed payment, possibly notify user
  console.log("Payment failed:", data);
}

async function initializeUsageRecords(subscriptionId: string, userId: string) {
  const features = ["transformations", "quizzes", "flashcards", "ai_chat"];
  const resetDate = new Date();
  resetDate.setDate(resetDate.getDate() + 7); // Weekly reset for transformations

  for (const feature of features) {
    await prisma.usageRecord.upsert({
      where: {
        userId_subscriptionId_feature_resetDate: {
          userId,
          subscriptionId,
          feature,
          resetDate,
        },
      },
      update: { count: 0 },
      create: {
        userId,
        subscriptionId,
        feature,
        count: 0,
        resetDate,
      },
    });
  }
}

async function resetUsageRecords(subscriptionId: string, userId: string) {
  // Reset usage counts for new billing period
  await prisma.usageRecord.updateMany({
    where: { subscriptionId, userId },
    data: { count: 0 },
  });
}
```

### 2. Configure Webhook URL

In your Dodo Payments dashboard, set your webhook URL to:

```
https://yourdomain.com/api/webhooks/dodo
```

## Customer Portal Integration

### 1. Create Customer Portal API

Create `src/app/api/customer-portal/route.ts`:

```typescript
import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { dodoClient } from "@/lib/dodo-payments";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's active subscription
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: { in: ["ACTIVE", "ON_HOLD"] },
      },
    });

    if (!subscription?.dodoCustomerId) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Create customer portal session
    const portalSession =
      await dodoClient.customers.createCustomerPortalSession({
        customer_id: subscription.dodoCustomerId,
        return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
      });

    return NextResponse.json({ url: portalSession.url });
  } catch (error) {
    console.error("Customer portal error:", error);
    return NextResponse.json(
      { error: "Failed to create portal session" },
      { status: 500 }
    );
  }
}
```

### 2. Add Portal Button to Dashboard

```typescript
// In your dashboard component
const handleManageSubscription = async () => {
  try {
    const response = await fetch("/api/customer-portal", {
      method: "POST",
    });

    const data = await response.json();

    if (response.ok) {
      window.location.href = data.url;
    } else {
      toast.error("Failed to open customer portal");
    }
  } catch (error) {
    toast.error("An error occurred");
  }
};

// Add button in your UI
<Button onClick={handleManageSubscription}>Manage Subscription</Button>;
```

## Plan Management

### 1. Create Plan Change API

Create `src/app/api/subscriptions/change-plan/route.ts`:

```typescript
import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { dodoClient } from "@/lib/dodo-payments";
import { prisma } from "@/lib/prisma";
import { DODO_PRODUCT_IDS } from "@/lib/subscription-config";
import { PlanType } from "@/data/pricingData";

interface ChangePlanRequest {
  newPlanType: PlanType;
  newBillingCycle: "monthly" | "annual";
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { newPlanType, newBillingCycle }: ChangePlanRequest =
      await request.json();

    // Get current subscription
    const currentSubscription = await prisma.subscription.findFirst({
      where: {
        userId: user.id,
        status: "ACTIVE",
      },
    });

    if (!currentSubscription) {
      return NextResponse.json(
        { error: "No active subscription found" },
        { status: 404 }
      );
    }

    // Get new product ID
    const productKey =
      `${newPlanType.toLowerCase()}_${newBillingCycle}` as keyof typeof DODO_PRODUCT_IDS;
    const newProductId = DODO_PRODUCT_IDS[productKey];

    if (!newProductId) {
      return NextResponse.json(
        { error: "Invalid plan selected" },
        { status: 400 }
      );
    }

    // Change plan with Dodo Payments
    const updatedSubscription = await dodoClient.subscriptions.changePlan({
      subscription_id: currentSubscription.dodoSubscriptionId,
      product_id: newProductId,
      quantity: 1,
      prorated_immediately: true,
    });

    // Update subscription in database
    await prisma.subscription.update({
      where: { id: currentSubscription.id },
      data: {
        planType: newPlanType,
        priceId: newProductId,
        metadata: {
          ...currentSubscription.metadata,
          billingCycle: newBillingCycle,
        },
      },
    });

    return NextResponse.json({
      success: true,
      subscription: updatedSubscription,
    });
  } catch (error) {
    console.error("Plan change error:", error);
    return NextResponse.json(
      { error: "Failed to change plan" },
      { status: 500 }
    );
  }
}
```

## Testing Strategy

### 1. Test Mode Setup

- Use test API keys for development
- Test with Dodo's test card numbers
- Verify webhook delivery in test mode

### 2. Test Scenarios

```typescript
// Test cases to implement:
const testScenarios = [
  "Successful subscription creation",
  "Failed payment handling",
  "Subscription renewal",
  "Plan upgrades/downgrades",
  "Subscription cancellation",
  "Webhook signature verification",
  "Usage limit enforcement",
  "Customer portal access",
];
```

### 3. Usage Tracking Implementation

Create `src/lib/usage-tracker.ts`:

```typescript
import { prisma } from "@/lib/prisma";
import { PlanType } from "@/data/pricingData";
import { getPlanLimits } from "./subscription-config";

export async function checkUsageLimit(
  userId: string,
  feature: string,
  increment: number = 1
): Promise<{ allowed: boolean; remaining: number; limit: number }> {
  // Get user's current subscription
  const subscription = await prisma.subscription.findFirst({
    where: {
      userId,
      status: "ACTIVE",
    },
  });

  if (!subscription) {
    // Free tier limits
    const limits = getPlanLimits("Free");
    return checkFreeTierLimits(userId, feature, limits, increment);
  }

  const limits = getPlanLimits(subscription.planType);

  // Check if feature is unlimited
  if (limits[feature]?.unlimited) {
    return { allowed: true, remaining: Infinity, limit: Infinity };
  }

  // Get current usage
  const usageRecord = await prisma.usageRecord.findFirst({
    where: {
      userId,
      subscriptionId: subscription.id,
      feature,
      resetDate: { gte: new Date() },
    },
  });

  const currentUsage = usageRecord?.count || 0;
  const limit = getFeatureLimit(limits, feature);
  const remaining = Math.max(0, limit - currentUsage);

  return {
    allowed: remaining >= increment,
    remaining,
    limit,
  };
}

export async function incrementUsage(
  userId: string,
  feature: string,
  increment: number = 1
): Promise<void> {
  const subscription = await prisma.subscription.findFirst({
    where: {
      userId,
      status: "ACTIVE",
    },
  });

  if (!subscription) return; // Free tier tracking handled separately

  const resetDate = calculateResetDate(feature);

  await prisma.usageRecord.upsert({
    where: {
      userId_subscriptionId_feature_resetDate: {
        userId,
        subscriptionId: subscription.id,
        feature,
        resetDate,
      },
    },
    update: {
      count: { increment },
    },
    create: {
      userId,
      subscriptionId: subscription.id,
      feature,
      count: increment,
      resetDate,
    },
  });
}

function calculateResetDate(feature: string): Date {
  const now = new Date();

  switch (feature) {
    case "transformations":
      // Weekly reset (every Monday)
      const daysUntilMonday = (8 - now.getDay()) % 7;
      const nextMonday = new Date(now);
      nextMonday.setDate(now.getDate() + daysUntilMonday);
      nextMonday.setHours(0, 0, 0, 0);
      return nextMonday;

    case "ai_chat":
      // Daily reset
      const tomorrow = new Date(now);
      tomorrow.setDate(now.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      return tomorrow;

    default:
      // Monthly reset
      const nextMonth = new Date(now);
      nextMonth.setMonth(now.getMonth() + 1);
      nextMonth.setDate(1);
      nextMonth.setHours(0, 0, 0, 0);
      return nextMonth;
  }
}

function getFeatureLimit(limits: any, feature: string): number {
  switch (feature) {
    case "transformations":
      return limits.transformations?.weekly || 0;
    case "ai_chat":
      return limits.aiChat?.daily || 0;
    case "quizzes":
      return limits.quizzes?.perContent || 0;
    case "flashcards":
      return limits.flashcards?.perContent || 0;
    default:
      return 0;
  }
}

async function checkFreeTierLimits(
  userId: string,
  feature: string,
  limits: any,
  increment: number
) {
  // Implement free tier usage tracking
  // This could use a separate table or Redis for temporary storage
  return { allowed: true, remaining: 0, limit: 0 };
}
```

## Cost Analysis

### Dodo Payments Pricing Structure

**Transaction Fees:**

- US customers: 4% + $0.40
- International customers: +1.5% (total: 5.5% + $0.40)
- Subscription payments: +0.5% (total: 4.5% + $0.40 for US, 6% + $0.40 for international)

**Example Cost Calculation for $11.99 Pro Plan:**

- US customer: $11.99 × 4.5% + $0.40 = $0.54 + $0.40 = $0.94 per transaction
- International: $11.99 × 6% + $0.40 = $0.72 + $0.40 = $1.12 per transaction

**Comparison with Stripe:**

- Stripe: 2.9% + $0.30 + additional subscription fees
- Dodo: Higher percentage but includes subscription management

**Additional Fees:**

- Payout fees: $5 for amounts < $1000 (free for $1000+)
- Refund fees: $1 per refund
- Chargeback fees: $30 per chargeback

## Security Considerations

### 1. Webhook Security

```typescript
// Always verify webhook signatures
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  const expectedSignature = crypto
    .createHmac("sha256", secret)
    .update(payload)
    .digest("hex");

  return crypto.timingSafeEqual(
    Buffer.from(signature, "hex"),
    Buffer.from(expectedSignature, "hex")
  );
}
```

### 2. API Key Management

- Store API keys in environment variables
- Use different keys for test/live modes
- Rotate keys regularly
- Never expose keys in client-side code

### 3. Data Protection

- Encrypt sensitive subscription data
- Implement proper access controls
- Log all subscription changes
- Regular security audits

## Implementation Checklist

### Phase 1: Setup & Basic Integration

- [ ] Create Dodo Payments account and get API keys
- [ ] Install Dodo Payments SDK
- [ ] Update database schema with subscription models
- [ ] Create basic subscription creation flow
- [ ] Implement webhook handler for basic events
- [ ] Test subscription creation in test mode

### Phase 2: Core Features

- [ ] Implement usage tracking system
- [ ] Add plan upgrade/downgrade functionality
- [ ] Create customer portal integration
- [ ] Implement subscription cancellation
- [ ] Add billing history and invoices
- [ ] Test all subscription lifecycle events

### Phase 3: Advanced Features

- [ ] Implement proration handling
- [ ] Add subscription analytics
- [ ] Create admin dashboard for subscription management
- [ ] Implement dunning management for failed payments
- [ ] Add subscription metrics and reporting
- [ ] Performance optimization and caching

### Phase 4: Production Deployment

- [ ] Complete business verification with Dodo Payments
- [ ] Switch to live API keys
- [ ] Configure production webhook endpoints
- [ ] Implement monitoring and alerting
- [ ] Create backup and recovery procedures
- [ ] Launch with limited user group
- [ ] Full production rollout

### Testing Checklist

- [ ] Test subscription creation flow
- [ ] Test webhook event handling
- [ ] Test plan changes and proration
- [ ] Test subscription cancellation
- [ ] Test customer portal functionality
- [ ] Test usage limit enforcement
- [ ] Test payment failure scenarios
- [ ] Test international customer flows

### Security Checklist

- [ ] Webhook signature verification
- [ ] API key security
- [ ] Data encryption at rest
- [ ] Access control implementation
- [ ] Audit logging
- [ ] Regular security reviews

## Next Steps

1. **Start with Phase 1**: Set up basic integration and test subscription creation
2. **Implement Core Features**: Add usage tracking and plan management
3. **Test Thoroughly**: Use Dodo's test environment extensively
4. **Gradual Rollout**: Start with a small user group before full deployment
5. **Monitor and Optimize**: Track metrics and optimize based on user behavior

## Support and Resources

- **Dodo Payments Documentation**: https://docs.dodopayments.com
- **Node.js SDK**: https://github.com/dodopayments/dodopayments-node
- **Community Discord**: https://discord.gg/bYqAp4ayYh
- **Support Email**: <EMAIL>

This implementation guide provides a comprehensive roadmap for integrating Dodo Payments subscriptions into your Qlipify application. The modular approach allows you to implement features incrementally while maintaining system stability.
