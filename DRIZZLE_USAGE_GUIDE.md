# 🚀 Drizzle ORM Usage Guide

## ✅ Migration Complete!

You have successfully migrated from Prisma to Drizzle ORM without any data loss. Your database contains:
- **57 users** 
- **31 content summaries**
- All other data intact

## 📁 Project Structure

```
lib/db/
├── index.ts          # Database connection & exports
├── schema.ts         # Database schema definitions
drizzle.config.ts     # Drizzle configuration
drizzle/              # Migration files
```

## 🔧 Available Scripts

```bash
npm run db:generate   # Generate new migrations from schema changes
npm run db:push      # Push schema changes to database (development)
npm run db:studio    # Open Drizzle Studio (database GUI)
```

## 📖 Basic Usage

### Import Database and Tables
```typescript
import { db, users, contentSummary, quiz, flashcard } from '@/lib/db';
import { eq, and, or, desc, asc, count } from 'drizzle-orm';
```

### 1. Select Queries

```typescript
// Get all users
const allUsers = await db.select().from(users);

// Get user by ID
const user = await db.select().from(users).where(eq(users.id, userId));

// Get user's content summaries
const userContent = await db
  .select()
  .from(contentSummary)
  .where(eq(contentSummary.userId, userId))
  .orderBy(desc(contentSummary.generatedAt));

// Get content with limit
const recentContent = await db
  .select()
  .from(contentSummary)
  .orderBy(desc(contentSummary.generatedAt))
  .limit(10);
```

### 2. Insert Operations

```typescript
// Create new user
const newUser = await db.insert(users).values({
  id: 'user-123',
  email: '<EMAIL>',
  displayName: 'John Doe',
}).returning();

// Create content summary
const newContent = await db.insert(contentSummary).values({
  contentType: 'YOUTUBE',
  contentId: 'video-123',
  title: 'My Video',
  content: 'Summary content...',
  originalContent: 'Original content...',
  sourceId: 'youtube-123',
  userId: 'user-123',
  outputType: 'summary',
}).returning();
```

### 3. Update Operations

```typescript
// Update user
const updatedUser = await db
  .update(users)
  .set({ 
    displayName: 'New Name',
    updatedAt: new Date() 
  })
  .where(eq(users.id, userId))
  .returning();

// Update content summary
await db
  .update(contentSummary)
  .set({ 
    isShared: true,
    shareToken: 'abc123' 
  })
  .where(eq(contentSummary.id, contentId));
```

### 4. Delete Operations

```typescript
// Delete content summary (cascades to related data)
await db.delete(contentSummary).where(eq(contentSummary.id, contentId));

// Delete user
await db.delete(users).where(eq(users.id, userId));
```

### 5. Complex Queries

```typescript
// Count user's content by type
const contentStats = await db
  .select({
    contentType: contentSummary.contentType,
    count: count()
  })
  .from(contentSummary)
  .where(eq(contentSummary.userId, userId))
  .groupBy(contentSummary.contentType);

// Search content
const searchResults = await db
  .select()
  .from(contentSummary)
  .where(
    and(
      eq(contentSummary.userId, userId),
      or(
        contentSummary.title.ilike(`%${searchTerm}%`),
        contentSummary.content.ilike(`%${searchTerm}%`)
      )
    )
  );
```

## 🔄 Making Schema Changes

### 1. Edit Schema
Edit `lib/db/schema.ts` to add/modify tables or columns:

```typescript
// Add new column to existing table
export const users = pgTable('users', {
  // ... existing columns
  newField: text('newField'), // Add this
});

// Add new table
export const newTable = pgTable('new_table', {
  id: text('id').primaryKey().$defaultFn(() => crypto.randomUUID()),
  name: text('name').notNull(),
  createdAt: timestamp('createdAt').defaultNow().notNull(),
});
```

### 2. Generate Migration
```bash
npm run db:generate
```

### 3. Apply Changes
```bash
npm run db:push
```

## 🎯 Migration from Prisma Code

### Replace Prisma Client
```typescript
// OLD (Prisma)
import { prisma } from '@/lib/prisma';
const users = await prisma.user.findMany();

// NEW (Drizzle)
import { db, users } from '@/lib/db';
const userList = await db.select().from(users);
```

### Common Patterns
```typescript
// Prisma → Drizzle equivalents

// findUnique
const user = await prisma.user.findUnique({ where: { id } });
const user = await db.select().from(users).where(eq(users.id, id)).limit(1);

// findMany with where
const content = await prisma.contentSummary.findMany({ 
  where: { userId } 
});
const content = await db.select().from(contentSummary)
  .where(eq(contentSummary.userId, userId));

// create
const newUser = await prisma.user.create({ data: userData });
const newUser = await db.insert(users).values(userData).returning();

// update
const updated = await prisma.user.update({ 
  where: { id }, 
  data: updateData 
});
const updated = await db.update(users)
  .set(updateData)
  .where(eq(users.id, id))
  .returning();
```

## 🛠️ Development Tools

### Drizzle Studio
```bash
npm run db:studio
```
Opens a web-based database browser at `https://local.drizzle.studio`

### Raw SQL (when needed)
```typescript
const result = await db.execute('SELECT * FROM users WHERE email = $1', [email]);
```

## 🚨 Important Notes

1. **No More Migration Hell** - Drizzle will never force database resets
2. **Type Safety** - Full TypeScript support with better inference than Prisma
3. **Performance** - Smaller bundle size and faster queries
4. **SQL Control** - You can always see and control the exact SQL being executed
5. **Production Ready** - Safe for production use with existing data

## 🎉 You're All Set!

Your migration is complete and Drizzle is ready to use. Start by replacing Prisma calls in your existing code gradually, one file at a time.
