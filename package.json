{"name": "qlipify", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbopack", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "clean": "rm -rf .next && rm -rf node_modules/.cache", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@google/generative-ai": "^0.24.0", "@marsidev/react-turnstile": "^1.1.0", "@postlight/parser": "^2.2.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@tailwindcss/typography": "^0.5.16", "@types/mdast": "^4.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@types/unist": "^3.0.3", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dodopayments": "^1.27.0", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.2", "drizzle-orm": "^0.44.2", "googleapis": "^146.0.0", "highlight.js": "^11.11.1", "html-react-parser": "^5.2.5", "jsdom": "^26.0.0", "jsonrepair": "^3.12.0", "katex": "^0.16.22", "limiter": "^3.0.0", "lucide-react": "^0.479.0", "nanoid": "^5.1.5", "next": "15.2.4", "next-themes": "^0.4.6", "pg": "^8.16.2", "postgres": "^3.4.7", "posthog-js": "^1.234.10", "posthog-node": "^4.11.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-pdftotext": "^1.3.4", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark": "^15.0.1", "remark-emoji": "^5.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "resend": "^4.1.2", "sharp": "^0.33.5", "sonner": "^2.0.1", "standardwebhooks": "^1.0.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "youtubei.js": "^13.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.2", "next-sitemap": "^4.2.3", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}