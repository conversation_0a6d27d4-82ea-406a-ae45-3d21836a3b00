CREATE TABLE "test_table" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "_prisma_migrations" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "_prisma_migrations" CASCADE;--> statement-breakpoint
ALTER TABLE "Quiz" DROP CONSTRAINT "Quiz_contentSummaryId_fkey";
--> statement-breakpoint
ALTER TABLE "QuizAttempt" DROP CONSTRAINT "QuizAttempt_quizId_fkey";
--> statement-breakpoint
ALTER TABLE "Flashcard" DROP CONSTRAINT "Flashcard_contentSummaryId_fkey";
--> statement-breakpoint
ALTER TABLE "ChatMessage" DROP CONSTRAINT "ChatMessage_contentSummaryId_fkey";
--> statement-breakpoint
ALTER TABLE "Subscription" DROP CONSTRAINT "Subscription_userId_fkey";
--> statement-breakpoint
ALTER TABLE "UsageRecord" DROP CONSTRAINT "UsageRecord_subscriptionId_fkey";
--> statement-breakpoint
ALTER TABLE "UsageAuditLog" DROP CONSTRAINT "UsageAuditLog_usageRecordId_fkey";
--> statement-breakpoint
DROP INDEX "users_email_key";--> statement-breakpoint
DROP INDEX "ContentSummary_contentId_key";--> statement-breakpoint
DROP INDEX "ContentSummary_shareToken_key";--> statement-breakpoint
DROP INDEX "ContentSummary_userId_sourceId_contentType_key";--> statement-breakpoint
DROP INDEX "Product_productId_key";--> statement-breakpoint
DROP INDEX "Subscription_subscriptionId_key";--> statement-breakpoint
DROP INDEX "UsageRecord_userId_feature_resetDate_key";--> statement-breakpoint
DROP INDEX "Quiz_contentSummaryId_idx";--> statement-breakpoint
DROP INDEX "Quiz_userId_idx";--> statement-breakpoint
DROP INDEX "QuizAttempt_quizId_idx";--> statement-breakpoint
DROP INDEX "QuizAttempt_userId_idx";--> statement-breakpoint
DROP INDEX "Flashcard_contentSummaryId_idx";--> statement-breakpoint
DROP INDEX "Flashcard_userId_idx";--> statement-breakpoint
DROP INDEX "ChatMessage_contentSummaryId_idx";--> statement-breakpoint
DROP INDEX "ChatMessage_userId_idx";--> statement-breakpoint
DROP INDEX "ContentSummary_contentId_idx";--> statement-breakpoint
DROP INDEX "ContentSummary_contentType_idx";--> statement-breakpoint
DROP INDEX "ContentSummary_userId_idx";--> statement-breakpoint
DROP INDEX "Product_interval_idx";--> statement-breakpoint
DROP INDEX "Product_isActive_idx";--> statement-breakpoint
DROP INDEX "Product_planType_idx";--> statement-breakpoint
DROP INDEX "Subscription_planType_idx";--> statement-breakpoint
DROP INDEX "Subscription_status_idx";--> statement-breakpoint
DROP INDEX "Subscription_subscriptionId_idx";--> statement-breakpoint
DROP INDEX "Subscription_userId_idx";--> statement-breakpoint
DROP INDEX "UsageRecord_feature_idx";--> statement-breakpoint
DROP INDEX "UsageRecord_resetDate_idx";--> statement-breakpoint
DROP INDEX "UsageRecord_userId_idx";--> statement-breakpoint
DROP INDEX "UsageAuditLog_action_idx";--> statement-breakpoint
DROP INDEX "UsageAuditLog_feature_idx";--> statement-breakpoint
DROP INDEX "UsageAuditLog_timestamp_idx";--> statement-breakpoint
DROP INDEX "UsageAuditLog_userId_idx";--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "updatedAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "users" ALTER COLUMN "updatedAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Quiz" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Quiz" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "QuizAttempt" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "QuizAttempt" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Flashcard" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Flashcard" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "ChatMessage" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "ChatMessage" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "ContentSummary" ALTER COLUMN "generatedAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "ContentSummary" ALTER COLUMN "generatedAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Product" ALTER COLUMN "price" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "Product" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Product" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Product" ALTER COLUMN "updatedAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Product" ALTER COLUMN "updatedAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "currentPeriodStart" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "currentPeriodEnd" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "canceledAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "updatedAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "updatedAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "Subscription" ALTER COLUMN "pendingPlanEffectiveDate" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "resetDate" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "createdAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "createdAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "updatedAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "updatedAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "lastUsedAt" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "UsageRecord" ALTER COLUMN "lastUsedAt" SET DEFAULT now();--> statement-breakpoint
ALTER TABLE "UsageAuditLog" ALTER COLUMN "timestamp" SET DATA TYPE timestamp;--> statement-breakpoint
ALTER TABLE "UsageAuditLog" ALTER COLUMN "timestamp" SET DEFAULT now();--> statement-breakpoint
CREATE INDEX "users_email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE INDEX "Quiz_contentSummaryId_idx" ON "Quiz" USING btree ("contentSummaryId");--> statement-breakpoint
CREATE INDEX "Quiz_userId_idx" ON "Quiz" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "QuizAttempt_quizId_idx" ON "QuizAttempt" USING btree ("quizId");--> statement-breakpoint
CREATE INDEX "QuizAttempt_userId_idx" ON "QuizAttempt" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "Flashcard_contentSummaryId_idx" ON "Flashcard" USING btree ("contentSummaryId");--> statement-breakpoint
CREATE INDEX "Flashcard_userId_idx" ON "Flashcard" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "ChatMessage_contentSummaryId_idx" ON "ChatMessage" USING btree ("contentSummaryId");--> statement-breakpoint
CREATE INDEX "ChatMessage_userId_idx" ON "ChatMessage" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "ContentSummary_contentId_idx" ON "ContentSummary" USING btree ("contentId");--> statement-breakpoint
CREATE INDEX "ContentSummary_contentType_idx" ON "ContentSummary" USING btree ("contentType");--> statement-breakpoint
CREATE INDEX "ContentSummary_userId_idx" ON "ContentSummary" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "Product_interval_idx" ON "Product" USING btree ("interval");--> statement-breakpoint
CREATE INDEX "Product_isActive_idx" ON "Product" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "Product_planType_idx" ON "Product" USING btree ("planType");--> statement-breakpoint
CREATE INDEX "Subscription_planType_idx" ON "Subscription" USING btree ("planType");--> statement-breakpoint
CREATE INDEX "Subscription_status_idx" ON "Subscription" USING btree ("status");--> statement-breakpoint
CREATE INDEX "Subscription_subscriptionId_idx" ON "Subscription" USING btree ("subscriptionId");--> statement-breakpoint
CREATE INDEX "Subscription_userId_idx" ON "Subscription" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "UsageRecord_feature_idx" ON "UsageRecord" USING btree ("feature");--> statement-breakpoint
CREATE INDEX "UsageRecord_resetDate_idx" ON "UsageRecord" USING btree ("resetDate");--> statement-breakpoint
CREATE INDEX "UsageRecord_userId_idx" ON "UsageRecord" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "UsageAuditLog_action_idx" ON "UsageAuditLog" USING btree ("action");--> statement-breakpoint
CREATE INDEX "UsageAuditLog_feature_idx" ON "UsageAuditLog" USING btree ("feature");--> statement-breakpoint
CREATE INDEX "UsageAuditLog_timestamp_idx" ON "UsageAuditLog" USING btree ("timestamp");--> statement-breakpoint
CREATE INDEX "UsageAuditLog_userId_idx" ON "UsageAuditLog" USING btree ("userId");--> statement-breakpoint
ALTER TABLE "users" ADD CONSTRAINT "users_email_unique" UNIQUE("email");--> statement-breakpoint
ALTER TABLE "ContentSummary" ADD CONSTRAINT "ContentSummary_contentId_unique" UNIQUE("contentId");--> statement-breakpoint
ALTER TABLE "ContentSummary" ADD CONSTRAINT "ContentSummary_shareToken_unique" UNIQUE("shareToken");--> statement-breakpoint
ALTER TABLE "ContentSummary" ADD CONSTRAINT "ContentSummary_userId_sourceId_contentType_key" UNIQUE("userId","sourceId","contentType");--> statement-breakpoint
ALTER TABLE "Product" ADD CONSTRAINT "Product_productId_unique" UNIQUE("productId");--> statement-breakpoint
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_subscriptionId_unique" UNIQUE("subscriptionId");--> statement-breakpoint
ALTER TABLE "UsageRecord" ADD CONSTRAINT "userId_feature_resetDate" UNIQUE("userId","feature","resetDate");