import { relations } from "drizzle-orm/relations";
import { contentSummary, quiz, quizAttempt, flashcard, chatMessage, users, subscription, usageRecord, usageAuditLog } from "./schema";

export const quizRelations = relations(quiz, ({one, many}) => ({
	contentSummary: one(contentSummary, {
		fields: [quiz.contentSummaryId],
		references: [contentSummary.id]
	}),
	quizAttempts: many(quizAttempt),
}));

export const contentSummaryRelations = relations(contentSummary, ({many}) => ({
	quizzes: many(quiz),
	flashcards: many(flashcard),
	chatMessages: many(chatMessage),
}));

export const quizAttemptRelations = relations(quizAttempt, ({one}) => ({
	quiz: one(quiz, {
		fields: [quizAttempt.quizId],
		references: [quiz.id]
	}),
}));

export const flashcardRelations = relations(flashcard, ({one}) => ({
	contentSummary: one(contentSummary, {
		fields: [flashcard.contentSummaryId],
		references: [contentSummary.id]
	}),
}));

export const chatMessageRelations = relations(chatMessage, ({one}) => ({
	contentSummary: one(contentSummary, {
		fields: [chatMessage.contentSummaryId],
		references: [contentSummary.id]
	}),
}));

export const subscriptionRelations = relations(subscription, ({one, many}) => ({
	user: one(users, {
		fields: [subscription.userId],
		references: [users.id]
	}),
	usageRecords: many(usageRecord),
}));

export const usersRelations = relations(users, ({many}) => ({
	subscriptions: many(subscription),
}));

export const usageRecordRelations = relations(usageRecord, ({one, many}) => ({
	subscription: one(subscription, {
		fields: [usageRecord.subscriptionId],
		references: [subscription.id]
	}),
	usageAuditLogs: many(usageAuditLog),
}));

export const usageAuditLogRelations = relations(usageAuditLog, ({one}) => ({
	usageRecord: one(usageRecord, {
		fields: [usageAuditLog.usageRecordId],
		references: [usageRecord.id]
	}),
}));