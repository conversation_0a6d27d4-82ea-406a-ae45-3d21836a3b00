{"id": "00000000-0000-0000-0000-000000000000", "prevId": "", "version": "7", "dialect": "postgresql", "tables": {"public._prisma_migrations": {"name": "_prisma_migrations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(36)", "primaryKey": true, "notNull": true}, "checksum": {"name": "checksum", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "finished_at": {"name": "finished_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "migration_name": {"name": "migration_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "logs": {"name": "logs", "type": "text", "primaryKey": false, "notNull": false}, "rolled_back_at": {"name": "rolled_back_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "applied_steps_count": {"name": "applied_steps_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "displayName": {"name": "displayName", "type": "text", "primaryKey": false, "notNull": false}, "avatarUrl": {"name": "avatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"users_email_key": {"name": "users_email_key", "columns": [{"expression": "email", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Quiz": {"name": "Quiz", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "questions": {"name": "questions", "type": "jsonb", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "contentSummaryId": {"name": "contentSummaryId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Quiz_contentSummaryId_idx": {"name": "Quiz_contentSummaryId_idx", "columns": [{"expression": "contentSummaryId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Quiz_userId_idx": {"name": "Quiz_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Quiz_contentSummaryId_fkey": {"name": "Quiz_contentSummaryId_fkey", "tableFrom": "Quiz", "tableTo": "ContentSummary", "schemaTo": "public", "columnsFrom": ["contentSummaryId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.QuizAttempt": {"name": "QuizAttempt", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quizId": {"name": "quizId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "answers": {"name": "answers", "type": "jsonb", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"QuizAttempt_quizId_idx": {"name": "QuizAttempt_quizId_idx", "columns": [{"expression": "quizId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "QuizAttempt_userId_idx": {"name": "QuizAttempt_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"QuizAttempt_quizId_fkey": {"name": "QuizAttempt_quizId_fkey", "tableFrom": "QuizAttempt", "tableTo": "Quiz", "schemaTo": "public", "columnsFrom": ["quizId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Flashcard": {"name": "Flashcard", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "cards": {"name": "cards", "type": "jsonb", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "studyProgress": {"name": "studyProgress", "type": "jsonb", "primaryKey": false, "notNull": false}, "contentSummaryId": {"name": "contentSummaryId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Flashcard_contentSummaryId_idx": {"name": "Flashcard_contentSummaryId_idx", "columns": [{"expression": "contentSummaryId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Flashcard_userId_idx": {"name": "Flashcard_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Flashcard_contentSummaryId_fkey": {"name": "Flashcard_contentSummaryId_fkey", "tableFrom": "Flashcard", "tableTo": "ContentSummary", "schemaTo": "public", "columnsFrom": ["contentSummaryId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.ChatMessage": {"name": "ChatMessage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "contentSummaryId": {"name": "contentSummaryId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"ChatMessage_contentSummaryId_idx": {"name": "ChatMessage_contentSummaryId_idx", "columns": [{"expression": "contentSummaryId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ChatMessage_userId_idx": {"name": "ChatMessage_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ChatMessage_contentSummaryId_fkey": {"name": "ChatMessage_contentSummaryId_fkey", "tableFrom": "ChatMessage", "tableTo": "ContentSummary", "schemaTo": "public", "columnsFrom": ["contentSummaryId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.ContentSummary": {"name": "ContentSummary", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contentType": {"name": "contentType", "type": "ContentType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "contentId": {"name": "contentId", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "originalContent": {"name": "originalContent", "type": "text", "primaryKey": false, "notNull": true}, "sourceId": {"name": "sourceId", "type": "text", "primaryKey": false, "notNull": true}, "generatedAt": {"name": "generatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "translatedContent": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "translationLanguage": {"name": "translationLanguage", "type": "text", "primaryKey": false, "notNull": false}, "isShared": {"name": "isShared", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "shareToken": {"name": "shareToken", "type": "text", "primaryKey": false, "notNull": false}, "outputType": {"name": "outputType", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"ContentSummary_contentId_idx": {"name": "ContentSummary_contentId_idx", "columns": [{"expression": "contentId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_contentId_key": {"name": "ContentSummary_contentId_key", "columns": [{"expression": "contentId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_contentType_idx": {"name": "ContentSummary_contentType_idx", "columns": [{"expression": "contentType", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_shareToken_key": {"name": "ContentSummary_shareToken_key", "columns": [{"expression": "shareToken", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_userId_idx": {"name": "ContentSummary_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_userId_sourceId_contentType_key": {"name": "ContentSummary_userId_sourceId_contentType_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "sourceId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "contentType", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Product": {"name": "Product", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "productId": {"name": "productId", "type": "text", "primaryKey": false, "notNull": true}, "planType": {"name": "planType", "type": "PlanType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "double precision", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "interval": {"name": "interval", "type": "text", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}}, "indexes": {"Product_interval_idx": {"name": "Product_interval_idx", "columns": [{"expression": "interval", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Product_isActive_idx": {"name": "Product_isActive_idx", "columns": [{"expression": "isActive", "asc": true, "nulls": "last", "opclass": "bool_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Product_planType_idx": {"name": "Product_planType_idx", "columns": [{"expression": "planType", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Product_productId_key": {"name": "Product_productId_key", "columns": [{"expression": "productId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.Subscription": {"name": "Subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": true}, "customerId": {"name": "customerId", "type": "text", "primaryKey": false, "notNull": false}, "planType": {"name": "planType", "type": "PlanType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "SubscriptionStatus", "typeSchema": "public", "primaryKey": false, "notNull": true}, "currentPeriodStart": {"name": "currentPeriodStart", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "billingCycle": {"name": "billingCycle", "type": "text", "primaryKey": false, "notNull": true}, "cancelAtPeriodEnd": {"name": "cancelAtPeriodEnd", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "canceledAt": {"name": "canceledAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "priceId": {"name": "priceId", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "pendingPlanType": {"name": "pendingPlanType", "type": "PlanType", "typeSchema": "public", "primaryKey": false, "notNull": false}, "pendingPlanEffectiveDate": {"name": "pendingPlanEffectiveDate", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "pendingPriceId": {"name": "pendingPriceId", "type": "text", "primaryKey": false, "notNull": false}, "emailToken": {"name": "emailToken", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"Subscription_planType_idx": {"name": "Subscription_planType_idx", "columns": [{"expression": "planType", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Subscription_status_idx": {"name": "Subscription_status_idx", "columns": [{"expression": "status", "asc": true, "nulls": "last", "opclass": "enum_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Subscription_subscriptionId_idx": {"name": "Subscription_subscriptionId_idx", "columns": [{"expression": "subscriptionId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Subscription_subscriptionId_key": {"name": "Subscription_subscriptionId_key", "columns": [{"expression": "subscriptionId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "Subscription_userId_idx": {"name": "Subscription_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Subscription_userId_fkey": {"name": "Subscription_userId_fkey", "tableFrom": "Subscription", "tableTo": "users", "schemaTo": "public", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.UsageRecord": {"name": "UsageRecord", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": false}, "feature": {"name": "feature", "type": "text", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "resetDate": {"name": "resetDate", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "lastUsedAt": {"name": "lastUsedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"UsageRecord_feature_idx": {"name": "UsageRecord_feature_idx", "columns": [{"expression": "feature", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageRecord_resetDate_idx": {"name": "UsageRecord_resetDate_idx", "columns": [{"expression": "resetDate", "asc": true, "nulls": "last", "opclass": "timestamp_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageRecord_userId_feature_resetDate_key": {"name": "UsageRecord_userId_feature_resetDate_key", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "feature", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}, {"expression": "resetDate", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "UsageRecord_userId_idx": {"name": "UsageRecord_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UsageRecord_subscriptionId_fkey": {"name": "UsageRecord_subscriptionId_fkey", "tableFrom": "UsageRecord", "tableTo": "Subscription", "schemaTo": "public", "columnsFrom": ["subscriptionId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}, "public.UsageAuditLog": {"name": "UsageAuditLog", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "usageRecordId": {"name": "usageRecordId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "feature": {"name": "feature", "type": "text", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "previousCount": {"name": "previousCount", "type": "integer", "primaryKey": false, "notNull": true}, "newCount": {"name": "newCount", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"UsageAuditLog_action_idx": {"name": "UsageAuditLog_action_idx", "columns": [{"expression": "action", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageAuditLog_feature_idx": {"name": "UsageAuditLog_feature_idx", "columns": [{"expression": "feature", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageAuditLog_timestamp_idx": {"name": "UsageAuditLog_timestamp_idx", "columns": [{"expression": "timestamp", "asc": true, "nulls": "last", "opclass": "timestamp_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageAuditLog_userId_idx": {"name": "UsageAuditLog_userId_idx", "columns": [{"expression": "userId", "asc": true, "nulls": "last", "opclass": "text_ops", "isExpression": false}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UsageAuditLog_usageRecordId_fkey": {"name": "UsageAuditLog_usageRecordId_fkey", "tableFrom": "UsageAuditLog", "tableTo": "UsageRecord", "schemaTo": "public", "columnsFrom": ["usageRecordId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}, "policies": {}, "isRLSEnabled": false}}, "enums": {"public.ContentType": {"name": "ContentType", "values": ["YOUTUBE", "PDF", "TEXT", "WEBPAGE"], "schema": "public"}, "public.PlanType": {"name": "PlanType", "values": ["Free", "Starter", "Pro", "Unlimited"], "schema": "public"}, "public.SubscriptionStatus": {"name": "SubscriptionStatus", "values": ["PENDING", "ACTIVE", "NON_RENEWING", "ATTENTION", "COMPLETED", "CANCELLED"], "schema": "public"}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}}}