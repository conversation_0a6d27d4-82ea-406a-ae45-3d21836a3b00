{"id": "ae93af76-ef84-4294-8bb1-e30b65d83d44", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ChatMessage": {"name": "ChatMessage", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "contentSummaryId": {"name": "contentSummaryId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"ChatMessage_contentSummaryId_idx": {"name": "ChatMessage_contentSummaryId_idx", "columns": [{"expression": "contentSummaryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ChatMessage_userId_idx": {"name": "ChatMessage_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ContentSummary": {"name": "ContentSummary", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "contentType": {"name": "contentType", "type": "ContentType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "contentId": {"name": "contentId", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "originalContent": {"name": "originalContent", "type": "text", "primaryKey": false, "notNull": true}, "sourceId": {"name": "sourceId", "type": "text", "primaryKey": false, "notNull": true}, "generatedAt": {"name": "generatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "translatedContent": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "translationLanguage": {"name": "translationLanguage", "type": "text", "primaryKey": false, "notNull": false}, "isShared": {"name": "isShared", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "shareToken": {"name": "shareToken", "type": "text", "primaryKey": false, "notNull": false}, "outputType": {"name": "outputType", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"ContentSummary_userId_idx": {"name": "ContentSummary_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_contentType_idx": {"name": "ContentSummary_contentType_idx", "columns": [{"expression": "contentType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ContentSummary_contentId_idx": {"name": "ContentSummary_contentId_idx", "columns": [{"expression": "contentId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ContentSummary_contentId_unique": {"name": "ContentSummary_contentId_unique", "nullsNotDistinct": false, "columns": ["contentId"]}, "ContentSummary_shareToken_unique": {"name": "ContentSummary_shareToken_unique", "nullsNotDistinct": false, "columns": ["shareToken"]}, "ContentSummary_userId_sourceId_contentType_key": {"name": "ContentSummary_userId_sourceId_contentType_key", "nullsNotDistinct": false, "columns": ["userId", "sourceId", "contentType"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Flashcard": {"name": "Flashcard", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "cards": {"name": "cards", "type": "jsonb", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "studyProgress": {"name": "studyProgress", "type": "jsonb", "primaryKey": false, "notNull": false}, "contentSummaryId": {"name": "contentSummaryId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Flashcard_contentSummaryId_idx": {"name": "Flashcard_contentSummaryId_idx", "columns": [{"expression": "contentSummaryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Flashcard_userId_idx": {"name": "Flashcard_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Product": {"name": "Product", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "productId": {"name": "productId", "type": "text", "primaryKey": false, "notNull": true}, "planType": {"name": "planType", "type": "PlanType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true, "default": "'USD'"}, "interval": {"name": "interval", "type": "text", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"Product_planType_idx": {"name": "Product_planType_idx", "columns": [{"expression": "planType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Product_isActive_idx": {"name": "Product_isActive_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Product_interval_idx": {"name": "Product_interval_idx", "columns": [{"expression": "interval", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Product_productId_unique": {"name": "Product_productId_unique", "nullsNotDistinct": false, "columns": ["productId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Quiz": {"name": "Quiz", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "questions": {"name": "questions", "type": "jsonb", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "contentSummaryId": {"name": "contentSummaryId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"Quiz_contentSummaryId_idx": {"name": "Quiz_contentSummaryId_idx", "columns": [{"expression": "contentSummaryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Quiz_userId_idx": {"name": "Quiz_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.QuizAttempt": {"name": "QuizAttempt", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quizId": {"name": "quizId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "answers": {"name": "answers", "type": "jsonb", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"QuizAttempt_quizId_idx": {"name": "QuizAttempt_quizId_idx", "columns": [{"expression": "quizId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "QuizAttempt_userId_idx": {"name": "QuizAttempt_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Subscription": {"name": "Subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": true}, "customerId": {"name": "customerId", "type": "text", "primaryKey": false, "notNull": false}, "planType": {"name": "planType", "type": "PlanType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "SubscriptionStatus", "typeSchema": "public", "primaryKey": false, "notNull": true}, "currentPeriodStart": {"name": "currentPeriodStart", "type": "timestamp", "primaryKey": false, "notNull": true}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "timestamp", "primaryKey": false, "notNull": true}, "billingCycle": {"name": "billingCycle", "type": "text", "primaryKey": false, "notNull": true}, "cancelAtPeriodEnd": {"name": "cancelAtPeriodEnd", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "canceledAt": {"name": "canceledAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "priceId": {"name": "priceId", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "pendingPlanType": {"name": "pendingPlanType", "type": "PlanType", "typeSchema": "public", "primaryKey": false, "notNull": false}, "pendingPlanEffectiveDate": {"name": "pendingPlanEffectiveDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "pendingPriceId": {"name": "pendingPriceId", "type": "text", "primaryKey": false, "notNull": false}, "emailToken": {"name": "emailToken", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"Subscription_userId_idx": {"name": "Subscription_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Subscription_subscriptionId_idx": {"name": "Subscription_subscriptionId_idx", "columns": [{"expression": "subscriptionId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Subscription_status_idx": {"name": "Subscription_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "Subscription_planType_idx": {"name": "Subscription_planType_idx", "columns": [{"expression": "planType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Subscription_subscriptionId_unique": {"name": "Subscription_subscriptionId_unique", "nullsNotDistinct": false, "columns": ["subscriptionId"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.test_table": {"name": "test_table", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.UsageAuditLog": {"name": "UsageAuditLog", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "usageRecordId": {"name": "usageRecordId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "feature": {"name": "feature", "type": "text", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "previousCount": {"name": "previousCount", "type": "integer", "primaryKey": false, "notNull": true}, "newCount": {"name": "newCount", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"UsageAuditLog_userId_idx": {"name": "UsageAuditLog_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageAuditLog_feature_idx": {"name": "UsageAuditLog_feature_idx", "columns": [{"expression": "feature", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageAuditLog_timestamp_idx": {"name": "UsageAuditLog_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageAuditLog_action_idx": {"name": "UsageAuditLog_action_idx", "columns": [{"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.UsageRecord": {"name": "UsageRecord", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "subscriptionId": {"name": "subscriptionId", "type": "text", "primaryKey": false, "notNull": false}, "feature": {"name": "feature", "type": "text", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "resetDate": {"name": "resetDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "lastUsedAt": {"name": "lastUsedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"UsageRecord_userId_idx": {"name": "UsageRecord_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageRecord_feature_idx": {"name": "UsageRecord_feature_idx", "columns": [{"expression": "feature", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "UsageRecord_resetDate_idx": {"name": "UsageRecord_resetDate_idx", "columns": [{"expression": "resetDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"userId_feature_resetDate": {"name": "userId_feature_resetDate", "nullsNotDistinct": false, "columns": ["userId", "feature", "resetDate"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "displayName": {"name": "displayName", "type": "text", "primaryKey": false, "notNull": false}, "avatarUrl": {"name": "avatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.ContentType": {"name": "ContentType", "schema": "public", "values": ["YOUTUBE", "PDF", "TEXT", "WEBPAGE"]}, "public.PlanType": {"name": "PlanType", "schema": "public", "values": ["Free", "Starter", "Pro", "Unlimited"]}, "public.SubscriptionStatus": {"name": "SubscriptionStatus", "schema": "public", "values": ["PENDING", "ACTIVE", "NON_RENEWING", "ATTENTION", "COMPLETED", "CANCELLED"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}