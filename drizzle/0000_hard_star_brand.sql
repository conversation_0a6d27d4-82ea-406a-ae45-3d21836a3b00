-- Current sql file was generated after introspecting the database
-- If you want to run this migration please uncomment this code before executing migrations
/*
CREATE TYPE "public"."ContentType" AS ENUM('YOUTUBE', 'PDF', 'TEXT', 'WEBPAGE');--> statement-breakpoint
CREATE TYPE "public"."PlanType" AS ENUM('Free', 'Starter', 'Pro', 'Unlimited');--> statement-breakpoint
CREATE TYPE "public"."SubscriptionStatus" AS ENUM('PENDING', 'ACTIVE', 'NON_RENEWING', 'ATTENTION', 'COMPLETED', 'CANCELLED');--> statement-breakpoint
CREATE TABLE "_prisma_migrations" (
	"id" varchar(36) PRIMARY KEY NOT NULL,
	"checksum" varchar(64) NOT NULL,
	"finished_at" timestamp with time zone,
	"migration_name" varchar(255) NOT NULL,
	"logs" text,
	"rolled_back_at" timestamp with time zone,
	"started_at" timestamp with time zone DEFAULT now() NOT NULL,
	"applied_steps_count" integer DEFAULT 0 NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" text PRIMARY KEY NOT NULL,
	"email" text,
	"displayName" text,
	"avatarUrl" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Quiz" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"questions" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"contentSummaryId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "QuizAttempt" (
	"id" text PRIMARY KEY NOT NULL,
	"quizId" text NOT NULL,
	"userId" text NOT NULL,
	"answers" jsonb NOT NULL,
	"score" integer NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Flashcard" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"cards" jsonb NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"studyProgress" jsonb,
	"contentSummaryId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ChatMessage" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"role" text NOT NULL,
	"content" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"contentSummaryId" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ContentSummary" (
	"id" text PRIMARY KEY NOT NULL,
	"contentType" "ContentType" NOT NULL,
	"contentId" text NOT NULL,
	"title" text NOT NULL,
	"content" text NOT NULL,
	"originalContent" text NOT NULL,
	"sourceId" text NOT NULL,
	"generatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"userId" text NOT NULL,
	"translatedContent" text,
	"translationLanguage" text,
	"isShared" boolean DEFAULT false NOT NULL,
	"shareToken" text,
	"outputType" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Product" (
	"id" text PRIMARY KEY NOT NULL,
	"productId" text NOT NULL,
	"planType" "PlanType" NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"price" double precision NOT NULL,
	"currency" text DEFAULT 'USD' NOT NULL,
	"interval" text NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Subscription" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"subscriptionId" text NOT NULL,
	"customerId" text,
	"planType" "PlanType" NOT NULL,
	"status" "SubscriptionStatus" NOT NULL,
	"currentPeriodStart" timestamp(3) NOT NULL,
	"currentPeriodEnd" timestamp(3) NOT NULL,
	"billingCycle" text NOT NULL,
	"cancelAtPeriodEnd" boolean DEFAULT false NOT NULL,
	"canceledAt" timestamp(3),
	"priceId" text NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"metadata" jsonb,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"pendingPlanType" "PlanType",
	"pendingPlanEffectiveDate" timestamp(3),
	"pendingPriceId" text,
	"emailToken" text
);
--> statement-breakpoint
CREATE TABLE "UsageRecord" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"subscriptionId" text,
	"feature" text NOT NULL,
	"count" integer DEFAULT 0 NOT NULL,
	"resetDate" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) NOT NULL,
	"lastUsedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"metadata" jsonb
);
--> statement-breakpoint
CREATE TABLE "UsageAuditLog" (
	"id" text PRIMARY KEY NOT NULL,
	"usageRecordId" text NOT NULL,
	"userId" text NOT NULL,
	"feature" text NOT NULL,
	"action" text NOT NULL,
	"previousCount" integer NOT NULL,
	"newCount" integer NOT NULL,
	"metadata" jsonb,
	"timestamp" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
ALTER TABLE "Quiz" ADD CONSTRAINT "Quiz_contentSummaryId_fkey" FOREIGN KEY ("contentSummaryId") REFERENCES "public"."ContentSummary"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "QuizAttempt" ADD CONSTRAINT "QuizAttempt_quizId_fkey" FOREIGN KEY ("quizId") REFERENCES "public"."Quiz"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Flashcard" ADD CONSTRAINT "Flashcard_contentSummaryId_fkey" FOREIGN KEY ("contentSummaryId") REFERENCES "public"."ContentSummary"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_contentSummaryId_fkey" FOREIGN KEY ("contentSummaryId") REFERENCES "public"."ContentSummary"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_userId_fkey" FOREIGN KEY ("userId") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "UsageRecord" ADD CONSTRAINT "UsageRecord_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "public"."Subscription"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "UsageAuditLog" ADD CONSTRAINT "UsageAuditLog_usageRecordId_fkey" FOREIGN KEY ("usageRecordId") REFERENCES "public"."UsageRecord"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE UNIQUE INDEX "users_email_key" ON "users" USING btree ("email" text_ops);--> statement-breakpoint
CREATE INDEX "Quiz_contentSummaryId_idx" ON "Quiz" USING btree ("contentSummaryId" text_ops);--> statement-breakpoint
CREATE INDEX "Quiz_userId_idx" ON "Quiz" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE INDEX "QuizAttempt_quizId_idx" ON "QuizAttempt" USING btree ("quizId" text_ops);--> statement-breakpoint
CREATE INDEX "QuizAttempt_userId_idx" ON "QuizAttempt" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE INDEX "Flashcard_contentSummaryId_idx" ON "Flashcard" USING btree ("contentSummaryId" text_ops);--> statement-breakpoint
CREATE INDEX "Flashcard_userId_idx" ON "Flashcard" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE INDEX "ChatMessage_contentSummaryId_idx" ON "ChatMessage" USING btree ("contentSummaryId" text_ops);--> statement-breakpoint
CREATE INDEX "ChatMessage_userId_idx" ON "ChatMessage" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE INDEX "ContentSummary_contentId_idx" ON "ContentSummary" USING btree ("contentId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "ContentSummary_contentId_key" ON "ContentSummary" USING btree ("contentId" text_ops);--> statement-breakpoint
CREATE INDEX "ContentSummary_contentType_idx" ON "ContentSummary" USING btree ("contentType" enum_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "ContentSummary_shareToken_key" ON "ContentSummary" USING btree ("shareToken" text_ops);--> statement-breakpoint
CREATE INDEX "ContentSummary_userId_idx" ON "ContentSummary" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "ContentSummary_userId_sourceId_contentType_key" ON "ContentSummary" USING btree ("userId" text_ops,"sourceId" text_ops,"contentType" text_ops);--> statement-breakpoint
CREATE INDEX "Product_interval_idx" ON "Product" USING btree ("interval" text_ops);--> statement-breakpoint
CREATE INDEX "Product_isActive_idx" ON "Product" USING btree ("isActive" bool_ops);--> statement-breakpoint
CREATE INDEX "Product_planType_idx" ON "Product" USING btree ("planType" enum_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Product_productId_key" ON "Product" USING btree ("productId" text_ops);--> statement-breakpoint
CREATE INDEX "Subscription_planType_idx" ON "Subscription" USING btree ("planType" enum_ops);--> statement-breakpoint
CREATE INDEX "Subscription_status_idx" ON "Subscription" USING btree ("status" enum_ops);--> statement-breakpoint
CREATE INDEX "Subscription_subscriptionId_idx" ON "Subscription" USING btree ("subscriptionId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Subscription_subscriptionId_key" ON "Subscription" USING btree ("subscriptionId" text_ops);--> statement-breakpoint
CREATE INDEX "Subscription_userId_idx" ON "Subscription" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE INDEX "UsageRecord_feature_idx" ON "UsageRecord" USING btree ("feature" text_ops);--> statement-breakpoint
CREATE INDEX "UsageRecord_resetDate_idx" ON "UsageRecord" USING btree ("resetDate" timestamp_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "UsageRecord_userId_feature_resetDate_key" ON "UsageRecord" USING btree ("userId" text_ops,"feature" text_ops,"resetDate" text_ops);--> statement-breakpoint
CREATE INDEX "UsageRecord_userId_idx" ON "UsageRecord" USING btree ("userId" text_ops);--> statement-breakpoint
CREATE INDEX "UsageAuditLog_action_idx" ON "UsageAuditLog" USING btree ("action" text_ops);--> statement-breakpoint
CREATE INDEX "UsageAuditLog_feature_idx" ON "UsageAuditLog" USING btree ("feature" text_ops);--> statement-breakpoint
CREATE INDEX "UsageAuditLog_timestamp_idx" ON "UsageAuditLog" USING btree ("timestamp" timestamp_ops);--> statement-breakpoint
CREATE INDEX "UsageAuditLog_userId_idx" ON "UsageAuditLog" USING btree ("userId" text_ops);
*/