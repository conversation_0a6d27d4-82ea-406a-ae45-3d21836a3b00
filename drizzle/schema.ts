import { pgTable, varchar, timestamp, text, integer, uniqueIndex, index, foreignKey, jsonb, boolean, doublePrecision, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const contentType = pgEnum("ContentType", ['YOUTUBE', 'PDF', 'TEXT', 'WEBPAGE'])
export const planType = pgEnum("PlanType", ['Free', 'Starter', 'Pro', 'Unlimited'])
export const subscriptionStatus = pgEnum("SubscriptionStatus", ['PENDING', 'ACTIVE', 'NON_RENEWING', 'ATTENTION', 'COMPLETED', 'CANCELLED'])


export const prismaMigrations = pgTable("_prisma_migrations", {
	id: varchar({ length: 36 }).primaryKey().notNull(),
	checksum: varchar({ length: 64 }).notNull(),
	finishedAt: timestamp("finished_at", { withTimezone: true, mode: 'string' }),
	migrationName: varchar("migration_name", { length: 255 }).notNull(),
	logs: text(),
	rolledBackAt: timestamp("rolled_back_at", { withTimezone: true, mode: 'string' }),
	startedAt: timestamp("started_at", { withTimezone: true, mode: 'string' }).defaultNow().notNull(),
	appliedStepsCount: integer("applied_steps_count").default(0).notNull(),
});

export const users = pgTable("users", {
	id: text().primaryKey().notNull(),
	email: text(),
	displayName: text(),
	avatarUrl: text(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	uniqueIndex("users_email_key").using("btree", table.email.asc().nullsLast().op("text_ops")),
]);

export const quiz = pgTable("Quiz", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	questions: jsonb().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	contentSummaryId: text().notNull(),
}, (table) => [
	index("Quiz_contentSummaryId_idx").using("btree", table.contentSummaryId.asc().nullsLast().op("text_ops")),
	index("Quiz_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.contentSummaryId],
			foreignColumns: [contentSummary.id],
			name: "Quiz_contentSummaryId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const quizAttempt = pgTable("QuizAttempt", {
	id: text().primaryKey().notNull(),
	quizId: text().notNull(),
	userId: text().notNull(),
	answers: jsonb().notNull(),
	score: integer().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	index("QuizAttempt_quizId_idx").using("btree", table.quizId.asc().nullsLast().op("text_ops")),
	index("QuizAttempt_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.quizId],
			foreignColumns: [quiz.id],
			name: "QuizAttempt_quizId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const flashcard = pgTable("Flashcard", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	cards: jsonb().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	studyProgress: jsonb(),
	contentSummaryId: text().notNull(),
}, (table) => [
	index("Flashcard_contentSummaryId_idx").using("btree", table.contentSummaryId.asc().nullsLast().op("text_ops")),
	index("Flashcard_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.contentSummaryId],
			foreignColumns: [contentSummary.id],
			name: "Flashcard_contentSummaryId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const chatMessage = pgTable("ChatMessage", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	role: text().notNull(),
	content: text().notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	contentSummaryId: text().notNull(),
}, (table) => [
	index("ChatMessage_contentSummaryId_idx").using("btree", table.contentSummaryId.asc().nullsLast().op("text_ops")),
	index("ChatMessage_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.contentSummaryId],
			foreignColumns: [contentSummary.id],
			name: "ChatMessage_contentSummaryId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const contentSummary = pgTable("ContentSummary", {
	id: text().primaryKey().notNull(),
	contentType: contentType().notNull(),
	contentId: text().notNull(),
	title: text().notNull(),
	content: text().notNull(),
	originalContent: text().notNull(),
	sourceId: text().notNull(),
	generatedAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	userId: text().notNull(),
	translatedContent: text(),
	translationLanguage: text(),
	isShared: boolean().default(false).notNull(),
	shareToken: text(),
	outputType: text().notNull(),
}, (table) => [
	index("ContentSummary_contentId_idx").using("btree", table.contentId.asc().nullsLast().op("text_ops")),
	uniqueIndex("ContentSummary_contentId_key").using("btree", table.contentId.asc().nullsLast().op("text_ops")),
	index("ContentSummary_contentType_idx").using("btree", table.contentType.asc().nullsLast().op("enum_ops")),
	uniqueIndex("ContentSummary_shareToken_key").using("btree", table.shareToken.asc().nullsLast().op("text_ops")),
	index("ContentSummary_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	uniqueIndex("ContentSummary_userId_sourceId_contentType_key").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.sourceId.asc().nullsLast().op("text_ops"), table.contentType.asc().nullsLast().op("text_ops")),
]);

export const product = pgTable("Product", {
	id: text().primaryKey().notNull(),
	productId: text().notNull(),
	planType: planType().notNull(),
	name: text().notNull(),
	description: text(),
	price: doublePrecision().notNull(),
	currency: text().default('USD').notNull(),
	interval: text().notNull(),
	isActive: boolean().default(true).notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
}, (table) => [
	index("Product_interval_idx").using("btree", table.interval.asc().nullsLast().op("text_ops")),
	index("Product_isActive_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("Product_planType_idx").using("btree", table.planType.asc().nullsLast().op("enum_ops")),
	uniqueIndex("Product_productId_key").using("btree", table.productId.asc().nullsLast().op("text_ops")),
]);

export const subscription = pgTable("Subscription", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	subscriptionId: text().notNull(),
	customerId: text(),
	planType: planType().notNull(),
	status: subscriptionStatus().notNull(),
	currentPeriodStart: timestamp({ precision: 3, mode: 'string' }).notNull(),
	currentPeriodEnd: timestamp({ precision: 3, mode: 'string' }).notNull(),
	billingCycle: text().notNull(),
	cancelAtPeriodEnd: boolean().default(false).notNull(),
	canceledAt: timestamp({ precision: 3, mode: 'string' }),
	priceId: text().notNull(),
	quantity: integer().default(1).notNull(),
	metadata: jsonb(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	pendingPlanType: planType(),
	pendingPlanEffectiveDate: timestamp({ precision: 3, mode: 'string' }),
	pendingPriceId: text(),
	emailToken: text(),
}, (table) => [
	index("Subscription_planType_idx").using("btree", table.planType.asc().nullsLast().op("enum_ops")),
	index("Subscription_status_idx").using("btree", table.status.asc().nullsLast().op("enum_ops")),
	index("Subscription_subscriptionId_idx").using("btree", table.subscriptionId.asc().nullsLast().op("text_ops")),
	uniqueIndex("Subscription_subscriptionId_key").using("btree", table.subscriptionId.asc().nullsLast().op("text_ops")),
	index("Subscription_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [users.id],
			name: "Subscription_userId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);

export const usageRecord = pgTable("UsageRecord", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	subscriptionId: text(),
	feature: text().notNull(),
	count: integer().default(0).notNull(),
	resetDate: timestamp({ precision: 3, mode: 'string' }).notNull(),
	createdAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	updatedAt: timestamp({ precision: 3, mode: 'string' }).notNull(),
	lastUsedAt: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
	metadata: jsonb(),
}, (table) => [
	index("UsageRecord_feature_idx").using("btree", table.feature.asc().nullsLast().op("text_ops")),
	index("UsageRecord_resetDate_idx").using("btree", table.resetDate.asc().nullsLast().op("timestamp_ops")),
	uniqueIndex("UsageRecord_userId_feature_resetDate_key").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.feature.asc().nullsLast().op("text_ops"), table.resetDate.asc().nullsLast().op("text_ops")),
	index("UsageRecord_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.subscriptionId],
			foreignColumns: [subscription.id],
			name: "UsageRecord_subscriptionId_fkey"
		}).onUpdate("cascade").onDelete("set null"),
]);

export const usageAuditLog = pgTable("UsageAuditLog", {
	id: text().primaryKey().notNull(),
	usageRecordId: text().notNull(),
	userId: text().notNull(),
	feature: text().notNull(),
	action: text().notNull(),
	previousCount: integer().notNull(),
	newCount: integer().notNull(),
	metadata: jsonb(),
	timestamp: timestamp({ precision: 3, mode: 'string' }).default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => [
	index("UsageAuditLog_action_idx").using("btree", table.action.asc().nullsLast().op("text_ops")),
	index("UsageAuditLog_feature_idx").using("btree", table.feature.asc().nullsLast().op("text_ops")),
	index("UsageAuditLog_timestamp_idx").using("btree", table.timestamp.asc().nullsLast().op("timestamp_ops")),
	index("UsageAuditLog_userId_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.usageRecordId],
			foreignColumns: [usageRecord.id],
			name: "UsageAuditLog_usageRecordId_fkey"
		}).onUpdate("cascade").onDelete("cascade"),
]);
