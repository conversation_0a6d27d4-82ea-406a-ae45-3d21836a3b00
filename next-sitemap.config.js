/** @type {import('next-sitemap').IConfig} */
const sitemapConfig = {
  siteUrl: process.env.SITE_URL || "https://qlipify.com",
  generateRobotsTxt: true,
  robotsTxtOptions: {
    policies: [
      {
        userAgent: "*",
        allow: "/",
      },
    ],
  },
  changefreq: "weekly",
  priority: 0.7,
  sitemapSize: 5000,
  exclude: [
    "/auth",
    "/new",
    "/dashboard",
    "/api/*",
    "/settings",
    "/transformations",
  ],
  additionalPaths: async () => {
    // Import blog posts data - using dynamic import for ES modules
    const blogPostsModule = await import("./src/data/blog-posts.js");
    const blogPosts = blogPostsModule.blogPosts || [];

    // Create paths for blog posts
    const blogPostPaths = blogPosts.map((post) => ({
      loc: `/blog/${post.slug}`,
      changefreq: "monthly",
      priority: 0.7,
      lastmod: new Date(post.date).toISOString(),
    }));

    // Create paths for main pages
    const mainPaths = [
      {
        loc: "/",
        changefreq: "daily",
        priority: 1.0,
        lastmod: new Date().toISOString(),
      },
      {
        loc: "/blog",
        changefreq: "weekly",
        priority: 0.8,
        lastmod: new Date().toISOString(),
      },
      {
        loc: "/pricing",
        changefreq: "monthly",
        priority: 0.8,
        lastmod: new Date().toISOString(),
      },
      {
        loc: "/about",
        changefreq: "monthly",
        priority: 0.6,
        lastmod: new Date().toISOString(),
      },
      {
        loc: "/privacy",
        changefreq: "yearly",
        priority: 0.5,
        lastmod: new Date().toISOString(),
      },
      {
        loc: "/terms",
        changefreq: "yearly",
        priority: 0.5,
        lastmod: new Date().toISOString(),
      },
    ];

    return [...mainPaths, ...blogPostPaths];
  },
};

export default sitemapConfig;
