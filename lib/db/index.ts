import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "./schema";

// Create the connection
const connectionString = process.env.DATABASE_URL!;
const client = postgres(connectionString, {
  max: 1,
  ssl: connectionString.includes("supabase")
    ? { rejectUnauthorized: false }
    : false,
});
export const db = drizzle(client, { schema });

// Export all schema for easy access
export * from "./schema";
