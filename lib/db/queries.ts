/**
 * Common database queries using Drizzle ORM
 * Ready-to-use functions for your application
 */

import { eq, desc, and, count } from 'drizzle-orm';
import { db, users, contentSummary, quiz, flashcard, chatMessage } from './index';

// User operations
export const userQueries = {
  async getById(id: string) {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0] || null;
  },

  async getByEmail(email: string) {
    const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
    return result[0] || null;
  },

  async create(userData: {
    id: string;
    email?: string;
    displayName?: string;
    avatarUrl?: string;
  }) {
    const result = await db.insert(users).values(userData).returning();
    return result[0];
  },

  async update(id: string, updateData: {
    email?: string;
    displayName?: string;
    avatarUrl?: string;
  }) {
    const result = await db
      .update(users)
      .set({ ...updateData, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return result[0];
  }
};

// Content operations
export const contentQueries = {
  async getByUserId(userId: string, limit = 50) {
    return await db
      .select()
      .from(contentSummary)
      .where(eq(contentSummary.userId, userId))
      .orderBy(desc(contentSummary.generatedAt))
      .limit(limit);
  },

  async getById(id: string) {
    const result = await db.select().from(contentSummary).where(eq(contentSummary.id, id)).limit(1);
    return result[0] || null;
  },

  async getByContentId(contentId: string) {
    const result = await db.select().from(contentSummary).where(eq(contentSummary.contentId, contentId)).limit(1);
    return result[0] || null;
  },

  async create(contentData: {
    contentType: 'YOUTUBE' | 'PDF' | 'TEXT' | 'WEBPAGE';
    contentId: string;
    title: string;
    content: string;
    originalContent: string;
    sourceId: string;
    userId: string;
    outputType: string;
    translatedContent?: string;
    translationLanguage?: string;
  }) {
    const result = await db.insert(contentSummary).values(contentData).returning();
    return result[0];
  },

  async update(id: string, updateData: {
    title?: string;
    content?: string;
    translatedContent?: string;
    translationLanguage?: string;
    isShared?: boolean;
    shareToken?: string;
  }) {
    const result = await db
      .update(contentSummary)
      .set(updateData)
      .where(eq(contentSummary.id, id))
      .returning();
    return result[0];
  },

  async delete(id: string) {
    await db.delete(contentSummary).where(eq(contentSummary.id, id));
  },

  async getSharedByToken(shareToken: string) {
    const result = await db
      .select()
      .from(contentSummary)
      .where(and(
        eq(contentSummary.shareToken, shareToken),
        eq(contentSummary.isShared, true)
      ))
      .limit(1);
    return result[0] || null;
  }
};

// Quiz operations
export const quizQueries = {
  async getByContentId(contentSummaryId: string) {
    const result = await db
      .select()
      .from(quiz)
      .where(eq(quiz.contentSummaryId, contentSummaryId))
      .limit(1);
    return result[0] || null;
  },

  async create(quizData: {
    userId: string;
    questions: any;
    contentSummaryId: string;
  }) {
    const result = await db.insert(quiz).values(quizData).returning();
    return result[0];
  }
};

// Flashcard operations
export const flashcardQueries = {
  async getByContentId(contentSummaryId: string) {
    const result = await db
      .select()
      .from(flashcard)
      .where(eq(flashcard.contentSummaryId, contentSummaryId))
      .limit(1);
    return result[0] || null;
  },

  async create(flashcardData: {
    userId: string;
    cards: any;
    contentSummaryId: string;
    studyProgress?: any;
  }) {
    const result = await db.insert(flashcard).values(flashcardData).returning();
    return result[0];
  },

  async updateProgress(id: string, studyProgress: any) {
    const result = await db
      .update(flashcard)
      .set({ studyProgress })
      .where(eq(flashcard.id, id))
      .returning();
    return result[0];
  }
};

// Chat operations
export const chatQueries = {
  async getByContentId(contentSummaryId: string) {
    return await db
      .select()
      .from(chatMessage)
      .where(eq(chatMessage.contentSummaryId, contentSummaryId))
      .orderBy(chatMessage.createdAt);
  },

  async create(messageData: {
    userId: string;
    role: string;
    content: string;
    contentSummaryId: string;
  }) {
    const result = await db.insert(chatMessage).values(messageData).returning();
    return result[0];
  }
};

// Analytics queries
export const analyticsQueries = {
  async getUserContentStats(userId: string) {
    return await db
      .select({
        contentType: contentSummary.contentType,
        count: count()
      })
      .from(contentSummary)
      .where(eq(contentSummary.userId, userId))
      .groupBy(contentSummary.contentType);
  },

  async getTotalUserContent(userId: string) {
    const result = await db
      .select({ count: count() })
      .from(contentSummary)
      .where(eq(contentSummary.userId, userId));
    return result[0]?.count || 0;
  }
};
