/**
 * Example usage of Drizzle ORM
 * This file shows how to use Drizzle instead of Prisma
 */

import { eq, and, desc } from 'drizzle-orm';
import { db, users, contentSummary, quiz } from './index';

// Example: Get user by ID
export async function getUserById(id: string) {
  const user = await db.select().from(users).where(eq(users.id, id)).limit(1);
  return user[0] || null;
}

// Example: Get user's content summaries
export async function getUserContentSummaries(userId: string) {
  return await db
    .select()
    .from(contentSummary)
    .where(eq(contentSummary.userId, userId))
    .orderBy(desc(contentSummary.generatedAt));
}

// Example: Create a new content summary
export async function createContentSummary(data: {
  contentType: 'YOUTUBE' | 'PDF' | 'TEXT' | 'WEBPAGE';
  contentId: string;
  title: string;
  content: string;
  originalContent: string;
  sourceId: string;
  userId: string;
  outputType: string;
}) {
  const result = await db.insert(contentSummary).values(data).returning();
  return result[0];
}

// Example: Update content summary
export async function updateContentSummary(id: string, data: Partial<{
  title: string;
  content: string;
  translatedContent: string;
  translationLanguage: string;
  isShared: boolean;
}>) {
  const result = await db
    .update(contentSummary)
    .set(data)
    .where(eq(contentSummary.id, id))
    .returning();
  return result[0];
}

// Example: Delete content summary and related data
export async function deleteContentSummary(id: string) {
  // Drizzle will handle cascade deletes based on foreign key constraints
  await db.delete(contentSummary).where(eq(contentSummary.id, id));
}

// Example: Complex query with joins (if you add relations)
export async function getUserWithRecentContent(userId: string) {
  return await db
    .select({
      user: users,
      contentCount: 'count(*)',
    })
    .from(users)
    .leftJoin(contentSummary, eq(users.id, contentSummary.userId))
    .where(eq(users.id, userId))
    .groupBy(users.id);
}
